package com.midea.pam.basedata.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.mcomponent.core.util.StringUtil;
import com.midea.pam.basedata.common.utils.BeanConverter;
import com.midea.pam.basedata.mapper.RoleGrantUserInfoMapper;
import com.midea.pam.basedata.mapper.RoleInfoExtMapper;
import com.midea.pam.basedata.mapper.RoleInfoMapper;
import com.midea.pam.basedata.mapper.RoleMenuFuncMapper;
import com.midea.pam.basedata.mapper.RoleMenuInfoExtMapper;
import com.midea.pam.basedata.mapper.RoleMenuInfoMapper;
import com.midea.pam.basedata.mapper.UnitExtMapper;
import com.midea.pam.basedata.service.RoleInfoService;
import com.midea.pam.common.basedata.dto.RoleGrantUserDto;
import com.midea.pam.common.basedata.dto.RoleInfoDto;
import com.midea.pam.common.basedata.dto.RoleMenuFuncDto;
import com.midea.pam.common.basedata.dto.RoleMenuFuncInfoDto;
import com.midea.pam.common.basedata.dto.UnitDto;
import com.midea.pam.common.basedata.entity.Grant;
import com.midea.pam.common.basedata.entity.Menu;
import com.midea.pam.common.basedata.entity.RoleGrantUser;
import com.midea.pam.common.basedata.entity.RoleInfo;
import com.midea.pam.common.basedata.entity.RoleInfoExample;
import com.midea.pam.common.basedata.entity.RoleMenuFunc;
import com.midea.pam.common.basedata.entity.RoleMenuFuncExample;
import com.midea.pam.common.basedata.entity.RoleMenuInfo;
import com.midea.pam.common.basedata.entity.RoleMenuInfoExample;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.esb.vo.AuthorityDimensionSynVo;
import com.midea.pam.common.esb.vo.AuthorityPushVo;
import com.midea.pam.common.esb.vo.AuthoritySynVo;
import com.midea.pam.common.esb.vo.AuthorityUserSynVo;
import com.midea.pam.common.util.AuthorityUtils;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.common.util.UserInfoUtile;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.security.GeneralSecurityException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @program: pam
 * @description: 角色service实现类
 * @author: CHENMK3
 * @create: 2019-3-6 15:08
 **/
public class RoleInfoServiceImpl implements RoleInfoService {

    private static Logger logger = LoggerFactory.getLogger(RoleInfoServiceImpl.class);

    @Resource
    private RoleGrantUserInfoMapper roleGrantUserInfoMapper;

    @Resource
    private RoleInfoMapper roleInfoMapper;

    @Resource
    private RoleMenuInfoMapper roleMenuInfoMapper;

    @Resource
    private RoleMenuInfoExtMapper roleMenuInfoExtMapper;

    @Resource
    private RoleMenuFuncMapper roleMenuFuncMapper;

    @Resource
    private RoleInfoExtMapper roleInfoExtMapper;

    @Resource
    private UnitExtMapper unitExtMapper;

    @Value("${perm.keyset}")
    private String keySet;

    @Value("${perm.secret}")
    private String secret;

    @Override
    public PageInfo<RoleInfoDto> findRoleList(Integer start, Integer limit, String roleName, String roleDescribe, String unitName) {
        PageHelper.startPage(start, limit);
        RoleInfoExample example = new RoleInfoExample();
        RoleInfoExample.Criteria criteria = example.createCriteria();
        RoleInfoExample.Criteria criteriaOr1 = example.or();
        RoleInfoExample.Criteria criteriaOr2 = example.or();
        if (StringUtils.isNotEmpty(roleName)) {
            criteria.andNameLike("%" + roleName + "%");
            criteriaOr1.andNameLike("%" + roleName + "%");
            criteriaOr2.andNameLike("%" + roleName + "%");
        }
        if (StringUtils.isNotEmpty(roleDescribe)) {
            criteria.andDescriptionLike("%" + roleDescribe + "%");
            criteriaOr1.andDescriptionLike("%" + roleDescribe + "%");
            criteriaOr2.andDescriptionLike("%" + roleDescribe + "%");
        }
        if (StringUtils.isNotEmpty(unitName)) {
            criteria.andUnitNameLike("%" + unitName + "%");
            criteriaOr1.andUnitNameLike("%系统超级管理%");
            criteriaOr2.andUnitNameIsNull();
        }
        // 3.角色列表按创建时间的升序排序
        example.setOrderByClause("create_at asc");
        List<RoleInfo> roleInfos = roleInfoMapper.selectByExample(example);
        final PageInfo<RoleInfoDto> pageInfo = BeanConverter.convertPage(roleInfos, RoleInfoDto.class);
        final List<RoleInfoDto> roleInfoDtos = pageInfo.getList();

        if (CollectionUtils.isNotEmpty(roleInfoDtos)) {
            roleInfoDtos.forEach(roleInfoDto -> {
                // 1. 在“创建时间“前增加“更新人”字段，“创建时间”更为“更新时间”， 当“更新人”、“更新时间”为空时，取创建人和创建时间 added at 20200721
                if (roleInfoDto.getUpdateBy() != null) {
                    final UserInfo userInfo = CacheDataUtils.findUserById(roleInfoDto.getUpdateBy());
                    if (userInfo != null) {
                        roleInfoDto.setUpdateName(userInfo.getName());
                    }
                } else {
                    if (roleInfoDto.getCreateBy() != null) {
                        final UserInfo userInfo = CacheDataUtils.findUserById(roleInfoDto.getCreateBy());
                        if (userInfo != null) {
                            roleInfoDto.setUpdateName(userInfo.getName());
                        }
                    }
                }

                if (roleInfoDto.getUpdateAt() == null) {
                    roleInfoDto.setUpdateAt(roleInfoDto.getCreateAt());
                }
                //  --------------------------------------------------------------------------------------------------------------------
                // 2.在“描述”后增加“用户数”，显示角色详情页面关联用户的数量
                final List<RoleGrantUserDto> userListByRoleDtos = roleGrantUserInfoMapper.findUserListByRole(roleInfoDto.getId());
                roleInfoDto.setGrantUserNum(CollectionUtils.isNotEmpty(userListByRoleDtos) ? userListByRoleDtos.size() : Integer.valueOf(0));
            });
        }

        return pageInfo;
    }

    @Transactional
    @Override
    public int addRole(RoleInfo role) {
        role.setDeleteFlag(false);
        return roleInfoMapper.insert(role);
    }

    @Transactional
    @Override
    public int updateRole(RoleInfo role) {
        role.setUpdateBy(SystemContext.getUserId());
        role.setUpdateAt(new Date());
        return roleInfoMapper.updateByPrimaryKeySelective(role);
    }

    @Transactional
    @Override
    public int deleteRole(Long id) {
        final List<RoleGrantUserDto> userListByRoleDtos = roleGrantUserInfoMapper.findUserListByRole(id);
        if (CollectionUtils.isNotEmpty(userListByRoleDtos)) {
            throw new MipException("角色关联的用户数不为0，不能删除!");
        }
        return roleInfoMapper.deleteByPrimaryKey(id);
    }

    @Override
    public RoleInfo findByCode(String code) {
        RoleInfoExample example = new RoleInfoExample();
        example.createCriteria().andCodeEqualTo(code);

        List<RoleInfo> roles = roleInfoMapper.selectByExample(example);

        return Optional.of(roles).orElse(new ArrayList<>()).stream().findFirst().orElse(null);
    }

    @Override
    public List<RoleInfo> getAll() {
        return roleInfoMapper.selectByExample(null);
    }

    @Override
    public RoleInfo getRoleById(Long id) {
        return roleInfoMapper.selectByPrimaryKey(id);
    }

    @Transactional
    @Override
    public void grantMenusToRole(Grant grant) {
        //给角色赋值菜单
        List<Long> entityIds = grant.getEntityIds();
        if (entityIds == null || entityIds.isEmpty()) {
            //如果传入的菜单id数组为空，则直接清空原有菜单授权
            roleMenuInfoExtMapper.deleteByRoleId(grant.getRoleId());
            return;
        }

        final RoleMenuInfoExample example = new RoleMenuInfoExample();
        example.createCriteria().andRoleIdEqualTo(grant.getRoleId());
        List<RoleMenuInfo> existMenus = roleMenuInfoMapper.selectByExample(example);

        List<Long> existIds = new ArrayList<>();
        if (existMenus != null) {
            for (RoleMenuInfo existMenu : existMenus) {
                if (!entityIds.contains(existMenu.getMenuId())) {
                    //旧的授权不在传入的数组内则删除
                    roleMenuInfoMapper.deleteByPrimaryKey(existMenu.getId());
                } else {
                    existIds.add(existMenu.getMenuId());
                }
            }
        }

        entityIds.forEach(id -> {
            if (!existIds.contains(id)) {
                //插入新的数据
                RoleMenuInfo roleMenu = new RoleMenuInfo();
                roleMenu.setMenuId(id);
                roleMenu.setRoleId(grant.getRoleId());
                roleMenuInfoMapper.insert(roleMenu);
            }
        });

        List<RoleMenuFuncInfoDto> listFuncInfo = grant.getListFuncInfo();
        for (RoleMenuFuncInfoDto roleMenuFuncInfoDto : listFuncInfo) {
            List<RoleMenuFuncDto> roleMenuFuncDtos = roleMenuFuncInfoDto.getListFunc();
            if (roleMenuFuncDtos.size() > 0) {
                for (RoleMenuFuncDto roleMenuFuncDto1 : roleMenuFuncDtos) {
                    RoleMenuFuncExample example1 = new RoleMenuFuncExample();
                    RoleMenuFuncExample.Criteria criteria = example1.createCriteria();
                    criteria.andRoleIdEqualTo(grant.getRoleId());
                    criteria.andMenuIdEqualTo(roleMenuFuncInfoDto.getMenuId());
                    criteria.andFuncIdEqualTo(roleMenuFuncDto1.getFuncId());
                    //查询唯一的按钮id的对象信息
                    List<RoleMenuFunc> listRoleMenuFunc = roleMenuFuncMapper.selectByExample(example1);
                    example1.clear();
                    listRoleMenuFunc.get(0).setFuncType(roleMenuFuncDto1.getFuncType());
                    //更新按钮现在状态
                    roleMenuFuncMapper.updateByPrimaryKeySelective(listRoleMenuFunc.get(0));
                }
            }
        }


        //更新角色表最后更新人
        RoleInfo roleInfo = new RoleInfo();
        roleInfo.setId(grant.getRoleId());
        roleInfo.setUpdateAt(new Date());
        roleInfo.setUpdateBy(SystemContext.getUserId());
        roleInfoMapper.updateByPrimaryKeySelective(roleInfo);

        logger.info("操作完成");

    }

    @Override
    public List<Menu> getGrantedAvailableMenus(Long roleId) {
        return roleMenuInfoExtMapper.getGrantedAvailableMenus(roleId);
    }

    @Override
    public List<Long> getGrantedMenusId(Long roleId) {

        final RoleMenuInfoExample example = new RoleMenuInfoExample();
        example.createCriteria().andRoleIdEqualTo(roleId);

        List<RoleMenuInfo> roleMenus = roleMenuInfoMapper.selectByExample(example);

        final List<Long> menusIds = new ArrayList<>();
        if (roleMenus != null && !roleMenus.isEmpty()) {
            roleMenus.forEach(rm -> menusIds.add(rm.getMenuId()));
        }
        return menusIds;
    }

    @Override
    public List<Long> getGrantedMenusIds(List<Long> roleIds) {
        RoleMenuInfoExample example = new RoleMenuInfoExample();
        example.createCriteria().andRoleIdIn(roleIds);

        List<RoleMenuInfo> roleMenus = roleMenuInfoMapper.selectByExample(example);

        List<Long> menusIds = new ArrayList<>();
        if (!ListUtils.isEmpty(roleMenus)) {
            for (RoleMenuInfo roleMenuInfo : roleMenus) {
                if (!menusIds.contains(roleMenuInfo.getMenuId())) {
                    menusIds.add(roleMenuInfo.getMenuId());
                }
            }
        }
        return menusIds;
    }

    @Override
    public List<RoleInfoDto> getRoleByUser(Long userId) {
        List<RoleInfo> roleInfos = roleInfoMapper.getRoleByUser(userId, new Date());
        if (!ListUtils.isEmpty(roleInfos)) {
            return new ArrayList<>();
        }
        return BeanConverter.copy(roleInfos, RoleInfoDto.class);
    }

    @Override
    public PageInfo<RoleInfo> pageUnAuthRoles(Long userId, String name, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<RoleInfo> roleInfos = roleInfoExtMapper.getUnAuthRoles(userId, name);
        return new PageInfo<>(roleInfos);
    }

    @Override
    public String pagePushUnit(String param) throws GeneralSecurityException, IOException {
        logger.info("parm密文信息：{}", param);
        String jsonString = AuthorityUtils.decryptAes256(param, keySet, secret);
        //json字符串
        JSONObject jsonParam = JSONObject.parseObject(jsonString);

        Long id = null;
        String unitId = jsonParam.getString("id");
        if (StringUtil.isNotNull(unitId)) {
            id = Long.parseLong(unitId);
        } else {
            logger.info("传来的参数id为空");
        }
        String pageNo = jsonParam.getString("pageNo");
        String pageSize = jsonParam.getString("pageSize");

        PageHelper.startPage(Integer.parseInt(pageNo), Integer.parseInt(pageSize));
        AuthoritySynVo authoritySynVo = JSON.parseObject(JSONArray.toJSONString(jsonParam), AuthoritySynVo.class);
        authoritySynVo.setUnitId(id);//-------------------------todo
        List<UnitDto> unitInfo = unitExtMapper.getUnitInfo(authoritySynVo);
        List<AuthorityDimensionSynVo> resultUnit = new ArrayList<>();
        PageInfo<UnitDto> page = BeanConverter.convertPage(unitInfo, UnitDto.class);
        if (ListUtils.isNotEmpty(unitInfo)) {
            for (UnitDto unitDto : unitInfo) {
                AuthorityDimensionSynVo authorityDimensionSynVo = new AuthorityDimensionSynVo();
                Map<String, Object> map1 = new HashMap<>();
                Map<String, Object> map2 = new HashMap<>();
                //id
                authorityDimensionSynVo.setId(String.valueOf(unitDto.getId()));
                //unit_name
                map1.put("zh", unitDto.getUnitName());
                map1.put("en", unitDto.getUnitName());
                map1.put("ja", unitDto.getUnitName());
                authorityDimensionSynVo.setName(map1);
                //description
                map2.put("zh", "");
                map2.put("en", "");
                map2.put("ja", "");
                authorityDimensionSynVo.setDescription(map2);
                authorityDimensionSynVo.setScreeningId(null);
                authorityDimensionSynVo.setParentId("0");
                authorityDimensionSynVo.setOrder(0);
                resultUnit.add(authorityDimensionSynVo);
            }
        }
        JSONObject jsonObject = new JSONObject();

        try {
            jsonObject.put("status", 10000);
            jsonObject.put("message", "请求成功");
            jsonObject.put("total", page.getTotal());
            if (CollectionUtils.isNotEmpty(unitInfo)) {
                jsonObject.put("data", resultUnit);
            } else {
                jsonObject.put("data", new ArrayList<>());
            }
            logger.info("用户权限获取成功,详情：{}", JSONObject.toJSONString(jsonObject));
        } catch (JSONException e) {
            logger.error("用户权限为空，请检查传入参数是否有误", e);
        }
        // 明文字符串
        String plaintext = JSONObject.toJSONString(jsonObject);
        //对要回收的数据加密
        String enString = AuthorityUtils.encryptAes256(plaintext, keySet, secret);

        return enString;
    }

    @Override
    public String pushUnitOrgInfo(String param) throws GeneralSecurityException, IOException {
        String jsonString = AuthorityUtils.decryptAes256(param, keySet, secret);
        //json字符串
        JSONObject jsonParam = JSONObject.parseObject(jsonString);

        Long id = null;
        String unitId = jsonParam.getString("id");
        if (StringUtil.isNotNull(unitId)) {
            id = Long.parseLong(unitId);
        } else {
            logger.error("传来的参数id为空");
        }
        String pageNo = jsonParam.getString("pageNo");
        String pageSize = jsonParam.getString("pageSize");

        PageHelper.startPage(Integer.parseInt(pageNo), Integer.parseInt(pageSize));
        AuthoritySynVo authoritySynVo = JSON.parseObject(JSONArray.toJSONString(jsonParam), AuthoritySynVo.class);
        authoritySynVo.setUnitId(id);//-------------------------todo
        List<UnitDto> unitInfo = unitExtMapper.getUnitOrgInfo(authoritySynVo);
        List<AuthorityDimensionSynVo> resultUnit = new ArrayList<>();
        PageInfo<UnitDto> page = BeanConverter.convertPage(unitInfo, UnitDto.class);
        if (ListUtils.isNotEmpty(unitInfo)) {
            for (UnitDto unitDto : unitInfo) {
                AuthorityDimensionSynVo authorityDimensionSynVo = new AuthorityDimensionSynVo();
                Map<String, Object> map1 = new HashMap<>();
                Map<String, Object> map2 = new HashMap<>();
                //id
                authorityDimensionSynVo.setId(String.valueOf(unitDto.getId()));
                //unit_name
                map1.put("zh", unitDto.getUnitName());
                map1.put("en", unitDto.getUnitName());
                map1.put("ja", unitDto.getUnitName());
                authorityDimensionSynVo.setName(map1);
                //description
                map2.put("zh", "");
                map2.put("en", "");
                map2.put("ja", "");
                authorityDimensionSynVo.setDescription(map2);
                Long parentId = unitDto.getParentId();
                List<String> pId = new ArrayList<>();
                if (parentId != null) {
                    pId.add(String.valueOf(parentId));
                    authorityDimensionSynVo.setScreeningId(pId);
                } else {
                    authorityDimensionSynVo.setScreeningId(null);
                }
/*
                //获取对应的使用单位的id
                String productName=unitDto.getUnitName();
                Map<String,Object> map = new HashMap<>();
                map.put("productName",productName);
                UnitDto unitOrgNameList = roleInfoExtMapper.getUnitOrgNameList(map);
                String[] split =null;
                if(unitOrgNameList!=null){
                    split = unitOrgNameList.getUnitParentIdList().split(",");
                    List<String> pId = new ArrayList<>();
                    for (int i=0;i<split.length;i++){
                        String p_id = split[i];
                        pId.add(p_id);
                    }
                    authorityDimensionSynVo.setScreeningId(pId);
                }else {
                    authorityDimensionSynVo.setScreeningId(null);
                }

 */
                authorityDimensionSynVo.setParentId("0");
                authorityDimensionSynVo.setOrder(0);
                resultUnit.add(authorityDimensionSynVo);
            }
        }
        JSONObject jsonObject = new JSONObject();

        try {
            jsonObject.put("status", 10000);
            jsonObject.put("message", "请求成功");
            jsonObject.put("total", page.getTotal());
            if (CollectionUtils.isNotEmpty(unitInfo)) {
                jsonObject.put("data", resultUnit);
            } else {
                jsonObject.put("data", new ArrayList<>());
            }
            logger.info("用户权限获取成功,详情：{}", JSONObject.toJSONString(jsonObject));
        } catch (JSONException e) {
            logger.error("用户权限为空，请检查传入参数是否有误", e);
        }
        // 明文字符串
        String plaintext = JSONObject.toJSONString(jsonObject);
        //对要回收的数据加密
        String enString = AuthorityUtils.encryptAes256(plaintext, keySet, secret);

        return enString;
    }

    @Override
    public String pagePushRoleData(String param) throws GeneralSecurityException, IOException {
        JSONObject jsonObject = new JSONObject();
        String jsonString = AuthorityUtils.decryptAes256(param, keySet, secret);
        if (StringUtils.isEmpty(jsonString)) {
            jsonObject.put("status", 10003);
            jsonObject.put("message", "解密失败");
        }
        //json字符串
        JSONObject jsonParam = JSONObject.parseObject(jsonString);

        Long id = null;
        String roleId = jsonParam.getString("id");
        if (StringUtil.isNotNull(roleId)) {
            id = Long.parseLong(roleId);
        } else {
            logger.error("传来的参数id为空");
        }

        String pageNo = jsonParam.getString("pageNo");
        String pageSize = jsonParam.getString("pageSize");

        PageHelper.startPage(Integer.parseInt(pageNo), Integer.parseInt(pageSize));
        AuthoritySynVo authoritySynVo = JSON.parseObject(JSONArray.toJSONString(jsonParam), AuthoritySynVo.class);
        authoritySynVo.setRoleId(id);
        List<RoleInfo> roleInfos = roleInfoExtMapper.getRoleInfo(authoritySynVo);
        List<AuthorityDimensionSynVo> resultUnit = new ArrayList<>();
        PageInfo<RoleInfo> page = BeanConverter.convertPage(roleInfos, RoleInfo.class);
        if (ListUtils.isNotEmpty(roleInfos)) {
            for (RoleInfo roleInfo : roleInfos) {
                AuthorityDimensionSynVo authorityDimensionSynVo = new AuthorityDimensionSynVo();
                Map<String, Object> map1 = new HashMap<>();
                Map<String, Object> map2 = new HashMap<>();
                //id
                authorityDimensionSynVo.setId(String.valueOf(roleInfo.getId()));
                //name
                map1.put("zh", roleInfo.getName());
                map1.put("en", roleInfo.getName());
                map1.put("ja", roleInfo.getName());
                authorityDimensionSynVo.setName(map1);
                //description
                map2.put("zh", roleInfo.getDescription());
                map2.put("en", roleInfo.getDescription());
                map2.put("ja", roleInfo.getDescription());
                authorityDimensionSynVo.setDescription(map2);

                String unitName = roleInfo.getUnitName();
                Map<String, Object> sunit = new HashMap<>();
                sunit.put("unitName", unitName);
                UnitDto unitId = unitExtMapper.getUnitId(sunit);
                List<String> uId = new ArrayList<>();
                if (unitId != null) {
                    uId.add(unitId.getId().toString());
                    authorityDimensionSynVo.setScreeningId(uId);
                } else {
                    authorityDimensionSynVo.setScreeningId(null);
                }
/*
                //根据角色名获取多个角色名称的id
                String roleName = roleInfo.getName();
                Map<String,Object> map = new HashMap<>();
                map.put("roleName",roleName);
                RoleInfoDto roleIdList = roleInfoExtMapper.getRoleIdList(map);
                String[] split =null;
                if(roleIdList!=null){
                    split = roleIdList.getRoleIdList().split(",");
                    List<String> uId = new ArrayList<>();

                    for (int i = 0;i < split.length;i++){
                        Long rId = Long.parseLong(String.valueOf(split[i]));
                        //根据roleId获取该表下的unitName
                        Map<String,Object> someUnitName = new HashMap<>();
                        someUnitName.put("rId",rId);
                        RoleInfoDto roleIdThUnitName = roleInfoExtMapper.getUnitNameByRoleId(someUnitName);
                        String unitName = roleIdThUnitName.getUnitName();
                        Map<String,Object> sunit = new HashMap<>();
                        sunit.put("unitName",unitName);

                        UnitDto unitId = unitExtMapper.getUnitId(sunit);
                        if(unitId!=null){
                            uId.add(unitId.getId().toString());
                        }
                    }
                    //使用单位id数组
                    authorityDimensionSynVo.setScreeningId(uId);
                }
                 */
                //parentId
                authorityDimensionSynVo.setParentId("0");
                authorityDimensionSynVo.setOrder(0);
                //全部结果
                resultUnit.add(authorityDimensionSynVo);
            }
        }

        try {
            jsonObject.put("status", 10000);
            jsonObject.put("message", "请求成功");
            jsonObject.put("total", page.getTotal());
            if (CollectionUtils.isNotEmpty(roleInfos)) {
                jsonObject.put("data", resultUnit);
            } else {
                jsonObject.put("data", new ArrayList<>());
            }
            logger.info("用户权限获取成功,详情：{}", JSONObject.toJSONString(jsonObject));
        } catch (JSONException e) {
            logger.error("用户权限为空，请检查传入参数是否有误", e);
        }
        // 明文字符串
        String plaintext = JSONObject.toJSONString(jsonObject);
        //对要回收的数据加密
        String enString = AuthorityUtils.encryptAes256(plaintext, keySet, secret);

        return enString;
    }

    @Override
    public String pagePushUserRoleData(String param) throws GeneralSecurityException, IOException {
        String jsonString = AuthorityUtils.decryptAes256(param, keySet, secret);

        //json字符串
        JSONObject jsonParam = JSONObject.parseObject(jsonString);

        String pageNo = jsonParam.getString("pageNo");
        String pageSize = jsonParam.getString("pageSize");

        PageHelper.startPage(Integer.parseInt(pageNo), Integer.parseInt(pageSize));
        //AuthoritySynVo authoritySynVo = JSON.parseObject(JSONArray.toJSONString(jsonParam), AuthoritySynVo.class);
        List<RoleGrantUser> roleGrantUser = roleGrantUserInfoMapper.getRoleGrantUserInfo();
        PageInfo<RoleGrantUser> page = BeanConverter.convertPage(roleGrantUser, RoleGrantUser.class);
        List<AuthorityUserSynVo> resultGrantUser = new ArrayList<>();
        if (ListUtils.isNotEmpty(roleGrantUser)) {
            for (RoleGrantUser roleGrantUser1 : roleGrantUser) {
                AuthorityUserSynVo authorityUserSynVo = new AuthorityUserSynVo();
                authorityUserSynVo.setId(String.valueOf(roleGrantUser1.getId()));
                String userName = UserInfoUtile.getUserName(roleGrantUser1.getUserId());//通过userid获取mip账号
                authorityUserSynVo.setUserId(userName);//mip账号
                List<String> lRole = new ArrayList<>();
                String s = String.valueOf(roleGrantUser1.getRoleId());
                lRole.add(s);
                authorityUserSynVo.setRoleId(lRole);

                Long roleId = roleGrantUser1.getRoleId();
                Map<String, Object> sunit = new HashMap<>();
                sunit.put("roleId", roleId);

                UnitDto unitId = roleGrantUserInfoMapper.getUnitId(sunit);
                if (unitId != null) {
                    String str = String.valueOf(unitId.getId());
                    List<String> rId = new ArrayList<>();
                    rId.add(str);
                    authorityUserSynVo.setUnitId(rId);
                }

                resultGrantUser.add(authorityUserSynVo);
            }
        }
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("status", 10000);
            jsonObject.put("message", "请求成功");
            jsonObject.put("total", page.getTotal());

            if (!CollectionUtils.isEmpty(roleGrantUser)) {
                jsonObject.put("data", resultGrantUser);
            }
            logger.info("用户权限获取成功,详情：{}", JSONObject.toJSONString(jsonObject));
        } catch (JSONException e) {
            logger.error("用户权限为空，请检查传入参数是否有误", e);
        }
        // 明文字符串
        String plaintext = JSONObject.toJSONString(jsonObject);
        //对要回收的数据加密
        String enString = AuthorityUtils.encryptAes256(plaintext, keySet, secret);

        return enString;
    }

    @Override
    public String writeRoleInfo(String param) throws GeneralSecurityException, IOException {
        //对密文解密
        String jsonString = AuthorityUtils.decryptAes256(param, keySet, secret);

        //json字符串
        JSONObject jsonParam = JSONObject.parseObject(jsonString);


        //数据更新方式：UPDATED（更新） 或者 CANCELLED（取消）
        Object data = jsonParam.get("data");
        //String substring = data.substring(1, data.length()-1);
        String type = jsonParam.getString("type");
        //Object exist = jsonParam.getString("exist");

        //传递参数的解密集合
        List<AuthorityPushVo> data_list = new ArrayList<>();
        //需要操作的结果集
        List<RoleGrantUser> result_list = new ArrayList<>();
        try {
            //data中的信息转换为列表对象形式
            data_list = JSON.parseObject(JSON.toJSONString(data), new TypeReference<List<AuthorityPushVo>>() {
            });
            logger.info("data_list详情为：{}", JSON.toJSONString(data_list));
            if (ListUtils.isNotEmpty(data_list)) {
                for (AuthorityPushVo item : data_list) {
                    RoleGrantUser roleGrantUser = new RoleGrantUser();
                    //roleGrantUser.setId(Long.parseLong(item.getId())); //id
                    //通过mip账号查userId
                    Long userId = UserInfoUtile.getUserId(item.getUserId());
                    Guard.notNull(userId, String.format("用户【%s】不存在", item.getUserId()));

                    roleGrantUser.setUserId(userId);//userId  MIP账号
                    //roleGrantUser.setRoleId(Long.parseLong(item.getRoleId().get(0)));//roleId
                    if (item.getEndDate() != null || !item.getEndDate().isEmpty()) {
                        Date endDate = new Date(Long.parseLong(item.getEndDate()) * 1000);
                        roleGrantUser.setEndDate(endDate);
                    }
//                    Date endDate = new Date(Long.parseLong(item.getEndDate()) * 1000);
//                    roleGrantUser.setEndDate(endDate);
                    List<String> roleId = item.getRoleId();
                    for (String role_id : roleId) {
                        Long rId = Long.parseLong(role_id);
                        roleGrantUser.setRoleId(rId);
                        result_list.add(roleGrantUser);
                    }
                }
            }
            logger.info("用户权限推送数据成功，等待检测");
        } catch (Exception e) {
            logger.error("用户权限推送数据异常，原因为：", e);
        }

        //更新或删除
        if ("UPDATED".equals(type)) {
            logger.info("调用更新方法");
            updateRoleGrantUser(result_list.get(0).getUserId(), result_list);
        } else if ("CANCELLED".equals(type)) {
            logger.info("调用删除方法");
            deleteByUserIdRole(result_list);
        }

        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("status", 10000);
            jsonObject.put("message", "请求成功");
        } catch (JSONException e) {
            logger.error(e.getMessage(), e);
        }
        // 明文字符串
        String plaintext = JSONObject.toJSONString(jsonObject);
        //对要回收的数据加密
        String enString = AuthorityUtils.encryptAes256(plaintext, keySet, secret);

        return enString;
    }

    @Override
    public List<Long> copyMenuToRole(Grant grant) {
        // 先清空当前菜单配置
        final RoleMenuInfoExample example = new RoleMenuInfoExample();
        example.createCriteria().andRoleIdEqualTo(grant.getRoleId());
        List<RoleMenuInfo> existMenus = roleMenuInfoMapper.selectByExample(example);
        if (existMenus != null) {
            for (RoleMenuInfo existMenu : existMenus) {
                roleMenuInfoMapper.deleteByPrimaryKey(existMenu.getId());
            }
        }

        final List<Long> menusIds = new ArrayList<>();
        // 复制来源角色配置
        example.clear();
        example.createCriteria().andRoleIdEqualTo(grant.getCopyRoleId());
        List<RoleMenuInfo> copyMenus = roleMenuInfoMapper.selectByExample(example);
        if (copyMenus != null) {
            for (RoleMenuInfo existMenu : copyMenus) {
                menusIds.add(existMenu.getMenuId());
                existMenu.setRoleId(grant.getRoleId());
                existMenu.setId(null);
                roleMenuInfoMapper.insert(existMenu);
            }
        }
        return menusIds;
    }

    @Override
    public Boolean copyRoleToOtherMenuByMenu(Long sourceMenuId, Long otherMenuId) {
        RoleMenuInfoExample example = new RoleMenuInfoExample();
        example.createCriteria().andMenuIdEqualTo(sourceMenuId);
        List<RoleMenuInfo> roleMenus = roleMenuInfoMapper.selectByExample(example);
        if (ListUtils.isEmpty(roleMenus)) {
            return Boolean.FALSE;
        }
        //先清空
        RoleMenuInfoExample delExample = new RoleMenuInfoExample();
        delExample.createCriteria().andMenuIdEqualTo(otherMenuId);
        List<RoleMenuInfo> delRoleMenus = roleMenuInfoMapper.selectByExample(delExample);
        if (ListUtils.isNotEmpty(delRoleMenus)) {
            for (RoleMenuInfo delRoleMenu : delRoleMenus) {
                roleMenuInfoMapper.deleteByPrimaryKey(delRoleMenu.getId());
            }
        }
        for (RoleMenuInfo roleMenuInfo : roleMenus) {
            roleMenuInfo.setId(null);
            roleMenuInfo.setMenuId(otherMenuId);
            roleMenuInfoMapper.insert(roleMenuInfo);
        }
        return Boolean.TRUE;
    }

    /*
     * 插入和更新
     */
    private void updateRoleGrantUser(Long userId, List<RoleGrantUser> roleGrantUserList) {
        if (ListUtils.isNotEmpty(roleGrantUserList)) {
            for (RoleGrantUser roleGrantUser : roleGrantUserList) {
                RoleGrantUser roleTemp = new RoleGrantUser();
                roleTemp.setUserId(userId);
                roleTemp.setRoleId(roleGrantUser.getRoleId());
                roleTemp.setStartDate(new Date());
                roleTemp.setEndDate(roleGrantUser.getEndDate());
                //判断数据库是否有数据，有则更新，没有则新增。
                Long id = this.isUserGrantedRole(roleTemp);
                if (id == null) {
                    this.roleGrantUserInfoMapper.insert(roleTemp);
                } else {
                    roleTemp.setId(id);
                    this.roleGrantUserInfoMapper.updateByPrimaryKeySelective(roleTemp);
                    //前端把时间置空，则需单独更新该字段
                    if (roleGrantUser.getEndDate() == null || roleGrantUser.getStartDate() == null) {
                        roleTemp = this.roleGrantUserInfoMapper.selectByPrimaryKey(id);
                        roleTemp.setStartDate(new Date());
                        roleTemp.setEndDate(roleGrantUser.getEndDate());
                        this.roleGrantUserInfoMapper.updateByPrimaryKey(roleTemp);
                    }
                }
            }
        }
    }

    private Long isUserGrantedRole(RoleGrantUser roleGrantUser) {
        logger.info("获取的请求参数为:{}", JSONObject.toJSONString(roleGrantUser));
        Long id = this.roleGrantUserInfoMapper.findIdByQuery(roleGrantUser);
        return id;
    }

    /*
     *删除
     */
    private void deleteByUserIdRole(List<RoleGrantUser> roleGrantUserList) {
        if (ListUtils.isNotEmpty(roleGrantUserList)) {
            for (RoleGrantUser roleGrantUser : roleGrantUserList) {
                RoleGrantUser roleGrantUser1 = new RoleGrantUser();
                roleGrantUser1.setUserId(roleGrantUser.getUserId());
                roleGrantUser1.setRoleId(roleGrantUser.getRoleId());
                roleGrantUserInfoMapper.deleteByUserIdRole(roleGrantUser1);
            }
        }
    }
}
