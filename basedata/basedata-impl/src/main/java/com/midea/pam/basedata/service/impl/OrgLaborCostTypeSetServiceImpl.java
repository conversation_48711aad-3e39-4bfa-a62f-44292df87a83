package com.midea.pam.basedata.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.midea.mcomponent.core.util.Assert;
import com.midea.pam.basedata.common.enums.DeletedFlag;
import com.midea.pam.basedata.common.utils.BeanConverter;
import com.midea.pam.basedata.config.CommonProperties;
import com.midea.pam.basedata.mapper.DiffCompanyLaborCostSetMapper;
import com.midea.pam.basedata.mapper.LaborExternalCostMapper;
import com.midea.pam.basedata.mapper.OrgLaborCostTypeSetDetailMapper;
import com.midea.pam.basedata.mapper.OrgLaborCostTypeSetExtMapper;
import com.midea.pam.basedata.mapper.OrgLaborCostTypeSetMapper;
import com.midea.pam.basedata.mapper.WbsConstraintMapper;
import com.midea.pam.basedata.service.EmployeeInfoService;
import com.midea.pam.basedata.service.LaborCostService;
import com.midea.pam.basedata.service.OperatingUnitService;
import com.midea.pam.basedata.service.OrgLaborCostTypeSetService;
import com.midea.pam.basedata.service.UnitService;
import com.midea.pam.basedata.service.WbsConstraintService;
import com.midea.pam.common.basedata.dto.DiffCompanyLaborCostSetDTO;
import com.midea.pam.common.basedata.dto.LaborCostDto;
import com.midea.pam.common.basedata.dto.OperatingUnitDto;
import com.midea.pam.common.basedata.dto.OrgLaborCostTypeSetDTO;
import com.midea.pam.common.basedata.dto.WbsConstraintDto;
import com.midea.pam.common.basedata.entity.DiffCompanyLaborCostSet;
import com.midea.pam.common.basedata.entity.LaborCost;
import com.midea.pam.common.basedata.entity.LaborCostExample;
import com.midea.pam.common.basedata.entity.LaborExternalCost;
import com.midea.pam.common.basedata.entity.LaborExternalCostExample;
import com.midea.pam.common.basedata.entity.OrgLaborCostTypeSet;
import com.midea.pam.common.basedata.entity.OrgLaborCostTypeSetDetail;
import com.midea.pam.common.basedata.entity.OrgLaborCostTypeSetDetailExample;
import com.midea.pam.common.basedata.entity.OrgLaborCostTypeSetExample;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.basedata.entity.WbsConstraint;
import com.midea.pam.common.basedata.entity.WbsConstraintExample;
import com.midea.pam.common.ctc.excelVo.OrgLaborCostTypeSetExcelVo;
import com.midea.pam.common.ctc.excelVo.TicketWorkingHourTemplateLaborCostExcelVo;
import com.midea.pam.common.ctc.excelVo.TicketWorkingHourTemplateLaborExternalCostExcelVo;
import com.midea.pam.common.ctc.excelVo.WbsWorkingHourTemplateDeptExcelVo;
import com.midea.pam.common.ctc.excelVo.WbsWorkingHourTemplateRoleExcelVo;
import com.midea.pam.common.ctc.excelVo.WbsWorkingHourTemplateWbsExcelVo;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.DeleteFlagEnum;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.RoleType;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/6/3
 * @description
 */
public class OrgLaborCostTypeSetServiceImpl implements OrgLaborCostTypeSetService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private OrgLaborCostTypeSetMapper orgLaborCostTypeSetMapper;
    @Resource
    private OrgLaborCostTypeSetExtMapper orgLaborCostTypeSetExtMapper;
    @Resource
    private DiffCompanyLaborCostSetMapper diffCompanyLaborCostSetMapper;
    @Resource
    private OperatingUnitService operatingUnitService;
    @Resource
    private OrgLaborCostTypeSetDetailMapper orgLaborCostTypeSetDetailMapper;
    @Resource
    private CommonProperties commonProperties;
    @Resource
    private EmployeeInfoService employeeInfoService;
    @Resource
    private UnitService unitService;
    @Resource
    private WbsConstraintMapper wbsConstraintMapper;
    @Resource
    private WbsConstraintService wbConstraintService;
    @Resource
    private javax.validation.Validator validator;
    @Resource
    private LaborCostService laborCostService;
    @Resource
    private LaborExternalCostMapper laborExternalCostMapper;


    @Override
    public long countByExample(OrgLaborCostTypeSetExample example) {
        return orgLaborCostTypeSetMapper.countByExample(example);
    }

    @Override
    public int deleteByPrimaryKey(Long id) {
        return orgLaborCostTypeSetMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(OrgLaborCostTypeSet record) {
        return orgLaborCostTypeSetMapper.insert(record);
    }

    @Override
    public int insertSelective(OrgLaborCostTypeSet record) {
        return orgLaborCostTypeSetMapper.insertSelective(record);
    }

    @Override
    public List<OrgLaborCostTypeSet> selectByExample(OrgLaborCostTypeSetExample example) {
        return orgLaborCostTypeSetMapper.selectByExample(example);
    }

    @Override
    public OrgLaborCostTypeSet selectByPrimaryKey(Long id) {
        return orgLaborCostTypeSetMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(OrgLaborCostTypeSet record) {
        return orgLaborCostTypeSetMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(OrgLaborCostTypeSet record) {
        return orgLaborCostTypeSetMapper.updateByPrimaryKey(record);
    }

    @Override
    public List<OrgLaborCostTypeSetDTO> list(OrgLaborCostTypeSetDTO orgLaborCostTypeSetDTO) {
        buildParams(orgLaborCostTypeSetDTO);
        List<OrgLaborCostTypeSetDTO> orgLaborCostTypeSetDTOS = orgLaborCostTypeSetExtMapper.list(orgLaborCostTypeSetDTO);
        if (ListUtils.isEmpty(orgLaborCostTypeSetDTOS)) {
            return new ArrayList<>();
        }
        Long companyId = orgLaborCostTypeSetDTO.getCompanyId() != null ? orgLaborCostTypeSetDTO.getCompanyId() : SystemContext.getUnitId();
        List<OperatingUnitDto> operatingUnitDtos = operatingUnitService.queryCurrentUnitOu(companyId);
        for (OrgLaborCostTypeSetDTO laborCostTypeSetDTO : orgLaborCostTypeSetDTOS) {
            // 如果没有关联业务实体,接口补全返回页面
            List<OrgLaborCostTypeSetDetail> hardWorkingList = getHardWorkingList(operatingUnitDtos, laborCostTypeSetDTO.getId());
            laborCostTypeSetDTO.setHardWorkingList(hardWorkingList);
        }
        return orgLaborCostTypeSetDTOS;
    }

    @Override
    public List<DiffCompanyLaborCostSetDTO> listDiffCompanyLaborCostSet(DiffCompanyLaborCostSet diffCompanyLaborCostSet) {
        if (diffCompanyLaborCostSet.getProjectCompanyId() == null) {
            diffCompanyLaborCostSet.setProjectCompanyId(SystemContext.getUnitId());
        }
        List<DiffCompanyLaborCostSetDTO> companyLaborCostSetDTOList =
                orgLaborCostTypeSetExtMapper.listDiffCompanyLaborCostSet(diffCompanyLaborCostSet);
        if (ListUtils.isEmpty(companyLaborCostSetDTOList)) {
            return new ArrayList<>();
        }
        return companyLaborCostSetDTOList;
    }

    private List<OrgLaborCostTypeSetDetail> getHardWorkingList(List<OperatingUnitDto> operatingUnitDtos, Long orgLaborCostTypeSetId) {
        OrgLaborCostTypeSetDetailExample costTypeSetDetailExample = new OrgLaborCostTypeSetDetailExample();
        costTypeSetDetailExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE)
                .andOrgLaborCostTypeSetIdEqualTo(orgLaborCostTypeSetId);
        List<OrgLaborCostTypeSetDetail> costTypeSetDetails = orgLaborCostTypeSetDetailMapper.selectByExample(costTypeSetDetailExample);
        if (ListUtils.isNotEmpty(operatingUnitDtos)) {
            if (costTypeSetDetails == null) {
                costTypeSetDetails = new ArrayList<>();
            }
            // 自动补全当前单位下业务实体
            for (OperatingUnitDto operatingUnitDto : operatingUnitDtos) {
                Optional<OrgLaborCostTypeSetDetail> first = costTypeSetDetails.stream().
                        filter(e -> e.getOuId().equals(operatingUnitDto.getOperatingUnitId())).findFirst();
                if (!first.isPresent()) {
                    OrgLaborCostTypeSetDetail newSetDetail = new OrgLaborCostTypeSetDetail();
                    newSetDetail.setOuId(operatingUnitDto.getOperatingUnitId());
                    newSetDetail.setOuName(operatingUnitDto.getOperatingUnitName());
                    costTypeSetDetails.add(newSetDetail);
                }
            }
        }
        return costTypeSetDetails;
    }

    private void buildParams(OrgLaborCostTypeSetDTO orgLaborCostTypeSetDTO) {
        String laborCostTypeCodes = orgLaborCostTypeSetDTO.getLaborCostTypeCodes();
        if (StringUtils.isNotEmpty(laborCostTypeCodes)) {
            List<String> list = null;
            String[] laborCostTypeCodeArr = laborCostTypeCodes.split(",", -1);
            for (String code : laborCostTypeCodeArr) {
                if (Objects.equals(code, "null")) {
                    orgLaborCostTypeSetDTO.setLaborCostTypeCodeIsNull(Boolean.TRUE);
                } else {
                    if (list == null) {
                        list = new ArrayList<>();
                    }
                    list.add(code);
                }
            }
            orgLaborCostTypeSetDTO.setLaborCostTypeCodeList(list);

            // 为空，代表查询为null的数据
        } else if (laborCostTypeCodes != null && laborCostTypeCodes.equals("null")) {
            orgLaborCostTypeSetDTO.setLaborCostTypeCodeIsNull(Boolean.TRUE);
        }

        if (orgLaborCostTypeSetDTO.getLaborWbsCostIds() != null) {
            orgLaborCostTypeSetDTO.setLaborWbsCostIdList(Arrays.stream(orgLaborCostTypeSetDTO.getLaborWbsCostIds()
                    .split(",")).map(e -> "%" + e + "%").collect(Collectors.toList()));
        }

        String unitIds = orgLaborCostTypeSetDTO.getUnitIds();
        if (StringUtils.isNotEmpty(unitIds)) {
            List<Long> list = null;
            String[] unitIdArr = unitIds.split(",", -1);
            for (String s : unitIdArr) {
                if (Objects.equals(s, "null")) {
                    orgLaborCostTypeSetDTO.setUnitIdIsNull(Boolean.TRUE);
                } else {
                    if (list == null) {
                        list = new ArrayList<>();
                    }
                    list.add(Long.valueOf(s));
                }
            }
            orgLaborCostTypeSetDTO.setUnitIdList(list);

            // 为空，代表查询为null的数据
        } else if (unitIds != null && unitIds.equals("null")) {
            orgLaborCostTypeSetDTO.setUnitIdIsNull(Boolean.TRUE);
        }

        if (StringUtils.isNotEmpty(orgLaborCostTypeSetDTO.getRoleTypeStr())) {
            List<Integer> roleTypeList = new ArrayList<>();
            String[] arrStr = orgLaborCostTypeSetDTO.getRoleTypeStr().split(",");
            for (String s : arrStr) {
                if (org.apache.commons.lang.StringUtils.isNotEmpty(s)) {
                    roleTypeList.add(Integer.valueOf(s));
                }
            }
            orgLaborCostTypeSetDTO.setRoleTypeList(roleTypeList);
        }

        //add by ex_xuwj4 begin
        String laborCostIds = orgLaborCostTypeSetDTO.getLaborCostIds();
        if (StringUtils.isNotEmpty(laborCostIds)) {
            List<Long> list = null;
            String[] laborCostIdArr = laborCostIds.split(",", -1);
            for (String s : laborCostIdArr) {
                if (Objects.equals(s, "null")) {
                    orgLaborCostTypeSetDTO.setLaborCostIdIsNull(Boolean.TRUE);
                } else {
                    if (list == null) {
                        list = new ArrayList<>();
                    }
                    list.add(Long.valueOf(s));
                }
            }
            orgLaborCostTypeSetDTO.setLaborCostIdList(list);

            // 为空，代表查询为null的数据
        } else if (laborCostIds != null && laborCostIds.equals("null")) {
            orgLaborCostTypeSetDTO.setLaborCostIdIsNull(Boolean.TRUE);
        }
        //add by ex_xuwj4 end
    }

    @Override
    public PageInfo<OrgLaborCostTypeSetDTO> page(OrgLaborCostTypeSetDTO orgLaborCostTypeSetDTO, int pageSize, int pageNum) {
        PageHelper.startPage(pageNum, pageSize);
        List<OrgLaborCostTypeSetDTO> list = this.list(orgLaborCostTypeSetDTO);
        PageInfo<OrgLaborCostTypeSetDTO> pageInfo = BeanConverter.convertPage(list, OrgLaborCostTypeSetDTO.class);
        return pageInfo;
    }

    @Override
    public PageInfo<DiffCompanyLaborCostSetDTO> queryDiffCompanyLaborCostSet(DiffCompanyLaborCostSet diffCompanyLaborCostSet, int pageSize, int pageNum) {
        PageHelper.startPage(pageNum, pageSize);
        List<DiffCompanyLaborCostSetDTO> list = this.listDiffCompanyLaborCostSet(diffCompanyLaborCostSet);
        PageInfo<DiffCompanyLaborCostSetDTO> pageInfo = BeanConverter.convertPage(list, DiffCompanyLaborCostSetDTO.class);
        return pageInfo;
    }

    @Override
    public DiffCompanyLaborCostSetDTO getDiffCompanyLaborCostSet(DiffCompanyLaborCostSet diffCompanyLaborCostSet) {
        List<DiffCompanyLaborCostSetDTO> list = this.listDiffCompanyLaborCostSet(diffCompanyLaborCostSet);
        if (ListUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return new DiffCompanyLaborCostSetDTO();
    }

    @Override
    public Boolean deleteDiffCompanyLaborCostSet(Long id, String uid, Long userId) {
        Asserts.notEmpty(id, ErrorCode.ID_NOT_NULL);
        DiffCompanyLaborCostSet entity = new DiffCompanyLaborCostSet();
        entity.setId(id);
        entity.setUpdateBy(userId);
        entity.setDeletedFlag(Boolean.TRUE);
        return diffCompanyLaborCostSetMapper.updateByPrimaryKeySelective(entity) > 0 ? true : false;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int update(OrgLaborCostTypeSetDTO dto) {
        Long id = dto.getId();
        if (id == null) {
            throw new BizException(Code.ERROR, "ID不能为空");
        }

        OrgLaborCostTypeSet orgLaborCostTypeSet = this.selectByPrimaryKey(id);
        if (orgLaborCostTypeSet == null) {
            throw new BizException(Code.ERROR, "数据不存在");
        }

        orgLaborCostTypeSet.setLaborCostTypeCode(dto.getLaborCostTypeCode());
        orgLaborCostTypeSet.setUnitId(dto.getUnitId());
        orgLaborCostTypeSet.setLaborCostId(dto.getLaborCostId());
        orgLaborCostTypeSet.setLaborCostName(dto.getLaborCostName());
        orgLaborCostTypeSet.setLaborWbsCostIds(dto.getLaborWbsCostIds());
        orgLaborCostTypeSet.setLaborWbsCostNames(dto.getLaborWbsCostNames());
        orgLaborCostTypeSet.setRoleType(dto.getRoleType());

        // 约束唯一性校验
        if (ListUtils.isNotEmpty(dto.getWbsConstraintDtoList())) {
            Map<Long, List<WbsConstraintDto>> wbsConstraintGroup = dto.getWbsConstraintDtoList().stream()
                    .collect(Collectors.groupingBy(e -> e.getTemplateId() + e.getRuleId()));
            wbsConstraintGroup.forEach((k, v) -> {
                if (v.size() > 1) {
                    throw new BizException(Code.ERROR, String.format("存在相同的wbs约束：%s:%s",
                            v.get(0).getTemplateName(),
                            v.get(0).getRuleName()));
                }
            });
        }

        // hr部门标准工时费率
        orgLaborCostTypeSet.setStandardWorkingHourRate(dto.getStandardWorkingHourRate());
        int n = this.updateByPrimaryKey(orgLaborCostTypeSet);
        // 保存HR部门费率ou行科目设置
        dto.setCompanyId(orgLaborCostTypeSet.getCompanyId());
        saveHardWorkingDetail(dto);

        wbConstraintService.removeByOrgLaborId(id);

        if (ListUtils.isNotEmpty(dto.getWbsConstraintDtoList())) {
            dto.getWbsConstraintDtoList().forEach(e -> {
                e.setId(null);
                e.setOrgLaborId(id);
                if (e.getCreateBy() == null) {
                    e.setCreateBy(SystemContext.getUserId());
                }
                if (e.getCreateAt() == null) {
                    e.setCreateAt(new Date());
                }
                e.setUpdateBy(SystemContext.getUserId());
                e.setUpdateAt(new Date());
                e.setDeletedFlag(0);
            });
            List<WbsConstraint> wbsConstraints = BeanConverter.copy(dto.getWbsConstraintDtoList(), WbsConstraint.class);
            wbsConstraints.forEach(e -> wbsConstraintMapper.insert(e));
        }
        // 是否有变更，需要触发更新用户的信息
        employeeInfoService.updateLaborCostRole(orgLaborCostTypeSet, orgLaborCostTypeSet.getCompanyId());
        return n;
    }

    private void saveHardWorkingDetail(OrgLaborCostTypeSetDTO dto) {
        List<OrgLaborCostTypeSetDetail> hardWorkingList = dto.getHardWorkingList();
        if (ListUtils.isEmpty(hardWorkingList)) {
            return;
        }
        for (OrgLaborCostTypeSetDetail costTypeSetDetail : hardWorkingList) {
            if (StringUtils.isEmpty(costTypeSetDetail.getHardWorking())
                    || costTypeSetDetail.getOuId() == null) {
                continue;
            }
            String[] creditSubjectArray = costTypeSetDetail.getHardWorking().split("\\.");
            if (creditSubjectArray.length != 7) {
                throw new BizException(Code.ERROR, "工时入账结转科目未配置正确，请先维护");
            } else {
                String creditSubject0 = ArrayUtils.toString(creditSubjectArray[0]); // 第一段
                if (creditSubject0.length() != 6) {
                    throw new BizException(Code.ERROR, "第一段配置有误,长度必须为6！");
                }
                String creditSubject1 = ArrayUtils.toString(creditSubjectArray[1]); //第二段
                if (!Objects.equals("0", creditSubject1) && creditSubject1.length() != 11) {
                    throw new BizException(Code.ERROR, "第二段配置有误，不为'0'时，长度必须为11！");
                }
            }
            costTypeSetDetail.setOrgLaborCostTypeSetId(dto.getId());
            costTypeSetDetail.setCompanyId(dto.getCompanyId());
            if (costTypeSetDetail.getId() == null) {
                costTypeSetDetail.setDeletedFlag(Boolean.FALSE);
                orgLaborCostTypeSetDetailMapper.insert(costTypeSetDetail);
            } else {
                orgLaborCostTypeSetDetailMapper.updateByPrimaryKeySelective(costTypeSetDetail);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int sync() {
        // 新增、删除、更新
        Map<Long, Set<String>> unitOrgMap = employeeInfoService.getCompanyFilterOrgMap();
        if (unitOrgMap == null || unitOrgMap.size() <= 0) {
            return 0;
        }

        int sum = 0;
        Set<Map.Entry<Long, Set<String>>> entries = unitOrgMap.entrySet();
        for (Map.Entry<Long, Set<String>> entry : entries) {
            Long companyId = entry.getKey();
            Set<String> filterOrgName = entry.getValue();

            //昆山和机器人分开部门拉取
            for (String s : filterOrgName) {
                List<OrgLaborCostTypeSet> needAddList = orgLaborCostTypeSetExtMapper.getNeedAdd(companyId, s);
                if (ListUtils.isNotEmpty(needAddList)) {
                    needAddList.forEach(orgLaborCostTypeSet -> {
                        orgLaborCostTypeSet.setCompanyId(companyId);
                        orgLaborCostTypeSet.setDeletedFlag(Boolean.FALSE);

                        orgLaborCostTypeSetMapper.insert(orgLaborCostTypeSet);
                        logger.info("HR人力费率新增：{}", JSONObject.toJSONString(orgLaborCostTypeSet));
                    });

                    logger.info("HR人力费率更新：{}条记录", needAddList.size());
                    sum = sum + needAddList.size();
                }

                int updateNum = orgLaborCostTypeSetExtMapper.syncUpdate(companyId, s);
                logger.info("HR人力费率更新：{}条记录", updateNum);

                int removeNum = orgLaborCostTypeSetExtMapper.syncRemove(companyId, s);
                logger.info("HR人力费率删除：{}条记录", removeNum);

                sum = sum + updateNum + removeNum;
            }
        }
        return sum;
    }

    @Override
    public List<OrgLaborCostTypeSetDTO> findOrgLaborCostTypes(OrgLaborCostTypeSetDTO orgLaborCostTypeSetDTO) {
        List<OrgLaborCostTypeSetDTO> list = this.list(orgLaborCostTypeSetDTO);
        return list;
    }

    @Override
    public OrgLaborCostTypeSetDTO findOrgLaborCostTypeSetById(Long id, Long ouId) {
        logger.info("开始查询hr部门费率");
        OrgLaborCostTypeSet orgLaborCostTypeSet = orgLaborCostTypeSetMapper.selectByPrimaryKey(id);
        // 查询ou科目明细
        OrgLaborCostTypeSetDetailExample example = new OrgLaborCostTypeSetDetailExample();
        example.createCriteria().andOrgLaborCostTypeSetIdEqualTo(id).andDeletedFlagEqualTo(Boolean.FALSE);
        List<OrgLaborCostTypeSetDetail> orgLaborCostTypeSetDetailList = orgLaborCostTypeSetDetailMapper.selectByExample(example);
        if (ListUtils.isNotEmpty(orgLaborCostTypeSetDetailList) && ouId != null) {
            orgLaborCostTypeSetDetailList.stream().filter(e -> e.getOuId().equals(ouId)).forEach(e -> {
                orgLaborCostTypeSet.setHardWorking(e.getHardWorking());
            });
        }
        OrgLaborCostTypeSetDTO orgLaborCostTypeSetDTO = BeanConverter.copy(orgLaborCostTypeSet, OrgLaborCostTypeSetDTO.class);
        orgLaborCostTypeSetDTO.setHardWorkingList(orgLaborCostTypeSetDetailList);
        return orgLaborCostTypeSetDTO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<OrgLaborCostTypeSetExcelVo> importOrgLaborCostTypeSet(List<OrgLaborCostTypeSetExcelVo> excelVoList, Long unitId) {
        unitId = unitId != null ? unitId : SystemContext.getUnitId();
        boolean isPass = validOrgLaborCostTypeSetExcelVo(excelVoList, unitId);
        if (isPass) {
            for (OrgLaborCostTypeSetExcelVo vo : excelVoList) {
                OrgLaborCostTypeSet orgLaborCostTypeSet = BeanConverter.copy(vo, OrgLaborCostTypeSet.class);
                orgLaborCostTypeSet.setUpdateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                orgLaborCostTypeSet.setUpdateAt(new Date());
                orgLaborCostTypeSetMapper.updateByPrimaryKeySelective(orgLaborCostTypeSet);
            }
            return Collections.emptyList();
        }
        return excelVoList.stream().filter(e -> StringUtils.isNotEmpty(e.getErrMsg())).collect(Collectors.toList());
    }

    @Override
    public Boolean saveDiffCompanyLaborCostSet(List<DiffCompanyLaborCostSet> diffCompanyLaborCostSetList) {
        if (ListUtils.isEmpty(diffCompanyLaborCostSetList)) {
            return Boolean.FALSE;
        }
        for (DiffCompanyLaborCostSet diffCompanyLaborCostSet : diffCompanyLaborCostSetList) {
            diffCompanyLaborCostSet.setUserCompanyId(null); //字段弃用
            if (diffCompanyLaborCostSet.getId() == null) {
                diffCompanyLaborCostSet.setDeletedFlag(Boolean.FALSE);
                diffCompanyLaborCostSetMapper.insert(diffCompanyLaborCostSet);
            } else {
                diffCompanyLaborCostSetMapper.updateByPrimaryKeySelective(diffCompanyLaborCostSet);
            }
        }

        return Boolean.TRUE;
    }

    @Override
    public Map getTicketWorkingHourImportTemplateInfo(OrgLaborCostTypeSetDTO query) {
        Long companyId = query.getCompanyId() != null ? query.getCompanyId() : SystemContext.getUnitId();
        //查询角色费率
        LaborCostDto params = new LaborCostDto();
        params.setType(1);
        params.setBizUnitId(companyId);
        List<LaborCostDto> laborCostList = laborCostService.selectList(params);
        //查询人力点工费率
        LaborExternalCostExample externalExample = new LaborExternalCostExample();
        externalExample.createCriteria().andBizUnitIdEqualTo(companyId).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        List<LaborExternalCost> laborExternalCostList = laborExternalCostMapper.selectByExample(externalExample);

        List<TicketWorkingHourTemplateLaborCostExcelVo> laborCostVos = new ArrayList<>();
        List<TicketWorkingHourTemplateLaborExternalCostExcelVo> laborExternalCostExcelVos = new ArrayList<>();
        int index = 1;
        for (LaborCostDto item : laborCostList) {
            laborCostVos.add(new TicketWorkingHourTemplateLaborCostExcelVo(index++, item.getCostTypeName(), item.getName(), RoleType.getNameByCode(item.getRoleType())));
        }
        index = 1;
        for (LaborExternalCost item : laborExternalCostList) {
            laborExternalCostExcelVos.add(new TicketWorkingHourTemplateLaborExternalCostExcelVo(index++, item.getName(), item.getVendorName(), item.getVendorCode()));
        }

        Map resultMap = new HashMap<>();
        resultMap.put("laborCostVos", laborCostVos);
        resultMap.put("laborExternalCostExcelVos", laborExternalCostExcelVos);
        return resultMap;
    }

    @Override
    public Map getWbsWorkingHourImportTemplateInfo(OrgLaborCostTypeSetDTO query) {
        Map resultMap = new HashMap<>();
        //查询HR部门费率类型
        List<OrgLaborCostTypeSetDTO> orgLaborCostTypeSetList = this.list(query);
        if (CollectionUtils.isEmpty(orgLaborCostTypeSetList)) {
            return resultMap;
        }
        //查询HR部门费率类型关联的wbs约束
        Map<Long, List<WbsConstraint>> wbsConstraintMap = new HashMap<>();
        List<Long> orgLaborIdList = orgLaborCostTypeSetList.stream().map(OrgLaborCostTypeSetDTO::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(orgLaborIdList)) {
            WbsConstraintExample example = new WbsConstraintExample();
            example.createCriteria().andOrgLaborIdIn(orgLaborIdList).andDeletedFlagEqualTo(DeleteFlagEnum.NOT_DELETED.getIntValue());
            wbsConstraintMap = wbsConstraintMapper.selectByExample(example).stream().collect(Collectors.groupingBy(WbsConstraint::getOrgLaborId));
        }

        List<WbsWorkingHourTemplateDeptExcelVo> deptExcelVos = new ArrayList<>();
        List<WbsWorkingHourTemplateRoleExcelVo> roleExcelVos = new ArrayList<>();
        List<WbsWorkingHourTemplateWbsExcelVo> wbsExcelVos = new ArrayList<>();
        for (OrgLaborCostTypeSetDTO dto : orgLaborCostTypeSetList) {
            //部门
            String hrOrgName = dto.getHrOrgName();
            //角色集
            String laborWbsCostNames = dto.getLaborWbsCostNames();
            String laborWbsCostIds = dto.getLaborWbsCostIds();
            if (StringUtils.isNotEmpty(hrOrgName)) {
                deptExcelVos.add(new WbsWorkingHourTemplateDeptExcelVo(hrOrgName));

                if (StringUtils.isNotEmpty(laborWbsCostNames) && StringUtils.isNotEmpty(laborWbsCostIds)) {
                    String[] laborWbsCostNameArr = laborWbsCostNames.split(",");
                    String[] laborCostIdArr = laborWbsCostIds.split(",");
                    if (laborWbsCostNameArr.length == laborCostIdArr.length) {
                        for (int i = 0; i < laborWbsCostNameArr.length; i++) {
                            String laborWbsCostName = laborWbsCostNameArr[i];
                            String laborWbsCostId = laborCostIdArr[i];
                            if (StringUtils.isNotEmpty(laborWbsCostId)) {
                                roleExcelVos.add(new WbsWorkingHourTemplateRoleExcelVo(hrOrgName, laborWbsCostName, Long.valueOf(laborWbsCostId)));
                            }
                        }
                    }
                }
            }

            List<WbsConstraint> wbsConstraintList = wbsConstraintMap.get(dto.getId());
            if (CollectionUtils.isNotEmpty(wbsConstraintList)) {
                for (WbsConstraint wbsConstraint : wbsConstraintList) {
                    //模板
                    Long templateId = wbsConstraint.getTemplateId();
                    String templateName = wbsConstraint.getTemplateName();
                    //层级
                    Long ruleId = wbsConstraint.getRuleId();
                    String ruleName = wbsConstraint.getRuleName();
                    //wbs集
                    String constraints = wbsConstraint.getConstraints();
                    if (StringUtils.isNotEmpty(constraints)) {
                        String[] split = constraints.split(",");
                        for (String constraint : split) {
                            wbsExcelVos.add(new WbsWorkingHourTemplateWbsExcelVo(hrOrgName, templateName, ruleName, constraint, templateId, ruleId));
                        }
                    }
                }
            }
        }

        LaborCostExample laborCostExample = new LaborCostExample();
        laborCostExample.createCriteria().andBizUnitIdEqualTo(query.getCompanyId()).andTypeEqualTo(1).andDeletedFlagEqualTo((byte) 0);
        List<LaborCost> laborCosts = laborCostService.selectByExample(laborCostExample);

        resultMap.put("deptExcelVos", deptExcelVos);
        resultMap.put("roleExcelVos", roleExcelVos);
        resultMap.put("wbsExcelVos", wbsExcelVos);
        resultMap.put("laborCosts", laborCosts);
        return resultMap;
    }

    @Override
    public Boolean checkExist(Long userId) {
        userId = Optional.ofNullable(userId).orElse(SystemContext.getUserId());
        UserInfo userInfo = CacheDataUtils.findUserById(userId);
        if (userInfo == null) {
            userInfo = employeeInfoService.selectByPrimaryKey(userId);
        }
        Assert.notNull(userInfo, "用户信息获取失败");
        Long laborCostTypeSetId = userInfo.getLaborCostTypeSetId();
        if (Objects.isNull(laborCostTypeSetId)) {
            return Boolean.FALSE;
        }
        OrgLaborCostTypeSet orgLaborCostTypeSet = orgLaborCostTypeSetMapper.selectByPrimaryKey(laborCostTypeSetId);
        if (Objects.isNull(orgLaborCostTypeSet) || Boolean.TRUE.equals(orgLaborCostTypeSet.getDeletedFlag())) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private boolean validOrgLaborCostTypeSetExcelVo(List<OrgLaborCostTypeSetExcelVo> excelVoList, Long unitId) {
        excelVoList.forEach(c -> c.setErrMsg(validator.validate(c).stream()
                .map(ConstraintViolation::getMessage).collect(Collectors.joining(","))));

        if (excelVoList.stream().anyMatch(c -> org.apache.commons.lang.StringUtils.isNotEmpty(c.getErrMsg()))) {
            return false;
        }

        excelVoList.forEach(e -> e.setHrOrgName(e.getNamePath().substring(e.getNamePath().indexOf("/", 5) + 1)));

        OrgLaborCostTypeSetExample typeSetExample = new OrgLaborCostTypeSetExample();
        typeSetExample.createCriteria().andCompanyIdEqualTo(unitId).andDeletedFlagEqualTo(Boolean.FALSE);
        Map<String, OrgLaborCostTypeSet> orgLaborCostTypeSetMap = orgLaborCostTypeSetMapper.selectByExample(typeSetExample)
                .stream().collect(Collectors.toMap(OrgLaborCostTypeSet::getHrOrgName, e -> e));

//        List<String> namePaths = excelVoList.stream().map(OrgLaborCostTypeSetExcelVo::getNamePath).collect(Collectors.toList());
//        Map<String,Orginization> organizationMap = organizeMapper.selectByNamePaths(namePaths)
//                .stream().collect(Collectors.toMap(Orginization::getNamePath,e->e));

        Map<String, LaborCost> laborCostMap = laborCostService.getLaborCostByUnitId(unitId);

        excelVoList.forEach(vo -> {
            List<String> errMsgList = new ArrayList<>();

            OrgLaborCostTypeSet orgLaborCostTypeSet = orgLaborCostTypeSetMap.get(vo.getHrOrgName());
            if (orgLaborCostTypeSet == null) {
                errMsgList.add("部门费率类型设置不存在");
            } else {
                vo.setId(orgLaborCostTypeSet.getId());
            }

            if ("角色".equals(vo.getLaborCostTypeName())) {
                vo.setLaborCostTypeCode("role");
            } else if ("职级".equals(vo.getLaborCostTypeName())) {
                vo.setLaborCostTypeCode("position");
            } else {
                errMsgList.add("不支持的人力费率类型");
            }

//            Orginization organization = organizationMap.get(vo.getNamePath());
//            if(organization!=null){
//                vo.setOrgName(organization.getName());
//                vo.setOrgId(organization.getId());
//            }else{
//                errMsgList.add("hr部门不存在");
//            }

            if (StringUtils.isNotEmpty(vo.getLaborWbsCostNames())) {
                vo.setLaborWbsCostNames(vo.getLaborWbsCostNames().replaceAll("、", ","));
                String[] laborWbsCostNames = vo.getLaborWbsCostNames().split(",");
                List<String> laborCostIds = new ArrayList<>(laborWbsCostNames.length);
                for (String laborWbsCostName : laborWbsCostNames) {
                    LaborCost laborCost = laborCostMap.get(laborWbsCostName);
                    if (laborCost != null) {
                        laborCostIds.add(String.valueOf(laborCost.getId()));
                    } else {
                        errMsgList.add(String.format("【%s】角色不存在", laborWbsCostName));
                    }
                }
                vo.setLaborWbsCostIds(String.join(",", laborCostIds));
            }

            vo.setErrMsg(String.join("，", errMsgList));
        });

        return excelVoList.stream().allMatch(e -> StringUtils.isEmpty(e.getErrMsg()));
    }

}
