package com.midea.pam.basedata.service.impl;


import com.midea.pam.basedata.mapper.EsbMassQueryRecordMapper;
import com.midea.pam.basedata.service.EsbMassQueryRecordService;
import com.midea.pam.common.basedata.entity.EsbMassQueryRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.Date;

public class EsbMassQueryRecordServiceImpl implements EsbMassQueryRecordService {

    private static final Logger logger = LoggerFactory.getLogger(EsbMassQueryRecordServiceImpl.class);

    @Resource
    private EsbMassQueryRecordMapper esbMassQueryRecordMapper;

    @Override
    public int add(EsbMassQueryRecord esbMassQueryRecord) {
        esbMassQueryRecord.setCreateAt(new Date());
        return esbMassQueryRecordMapper.insertSelective(esbMassQueryRecord);
    }

    @Override
    public int updateByPrimaryKeySelective(EsbMassQueryRecord esbMassQueryRecord) {
        esbMassQueryRecord.setUpdateAt(new Date());
        return esbMassQueryRecordMapper.updateByPrimaryKeySelective(esbMassQueryRecord);
    }

    @Override
    public int updateByPrimaryKey(EsbMassQueryRecord esbMassQueryRecord) {
        return esbMassQueryRecordMapper.updateByPrimaryKey(esbMassQueryRecord);
    }

    @Override
    public int delete(Long id) {
        EsbMassQueryRecord esbMassQueryRecord = new EsbMassQueryRecord();
        esbMassQueryRecord.setId(id);
        esbMassQueryRecord.setUpdateAt(new Date());
        return esbMassQueryRecordMapper.updateByPrimaryKeySelective(esbMassQueryRecord);
    }

}
