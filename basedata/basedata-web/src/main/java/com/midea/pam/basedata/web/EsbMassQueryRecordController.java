package com.midea.pam.basedata.web;

import com.midea.pam.basedata.common.utils.JsonUtils;
import com.midea.pam.basedata.service.EsbMassQueryRecordService;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.entity.EsbMassQueryRecord;
import com.midea.pam.common.enums.Code;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api("接口日志")
@RestController
@RequestMapping("esbMassQueryRecord")
public class EsbMassQueryRecordController {

    private final static Logger LOGGER = LoggerFactory.getLogger(EsbMassQueryRecordController.class);

    @Resource
    private EsbMassQueryRecordService esbMassQueryRecordService;

    @ApiOperation(value = "新增接口日志")
    @PostMapping("add")
    public Response add(@RequestBody EsbMassQueryRecord queryRecord) {
        DataResponse<Integer> response = Response.dataResponse();
        try {
            response.setData(esbMassQueryRecordService.add(queryRecord));
        } catch (Exception e) {
            LOGGER.error("ERP通用查询接口日志保存, 流水号:{}", queryRecord.getEsbSerialNo(), e.getMessage());
            response.setCode(Code.ERROR);
            response.setMsg(e.getMessage());
        }
        return response;
    }

    @ApiOperation(value = "修改接口日志")
    @PostMapping("update")
    public String update(@RequestBody EsbMassQueryRecord queryRecord) {
        DataResponse<Integer> response = Response.dataResponse();
        try {
            response.setData(esbMassQueryRecordService.updateByPrimaryKey(queryRecord));
        } catch (Exception e) {
            LOGGER.error("ERP通用查询接口日志更新, 流水号:{}", queryRecord.getEsbSerialNo(), e.getMessage());
            response.setCode(Code.ERROR);
            response.setMsg(e.getMessage());
        }
        return JsonUtils.toString(response);
    }

    @ApiOperation(value = "删除接口日志")
    @GetMapping({"delete"})
    public String delete(@RequestParam Long id) {
        return JsonUtils.toString(esbMassQueryRecordService.delete(id));
    }

}
