package com.midea.pam.basedata.mapper;

import com.meicloud.light.mapper.Mapper;
import com.midea.pam.common.basedata.dto.DiffCompanyLaborCostSetDTO;
import com.midea.pam.common.basedata.dto.OrgLaborCostTypeSetDTO;
import com.midea.pam.common.basedata.entity.DiffCompanyLaborCostSet;
import com.midea.pam.common.basedata.entity.OrgLaborCostTypeSet;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OrgLaborCostTypeSetExtMapper extends Mapper {

    /**
     * 查询列表
     * @param orgLaborCostTypeSetDTO
     * @return
     */
    List<OrgLaborCostTypeSetDTO> list(OrgLaborCostTypeSetDTO orgLaborCostTypeSetDTO);

    List<OrgLaborCostTypeSet> getNeedAdd(@Param("companyId") Long companyId, @Param("hrOrgName") String hrOrgName);

    int syncUpdate(@Param("companyId") Long companyId, @Param("hrOrgName") String hrOrgName);

    int syncRemove(@Param("companyId") Long companyId, @Param("hrOrgName") String hrOrgName);

    List<DiffCompanyLaborCostSetDTO> listDiffCompanyLaborCostSet(DiffCompanyLaborCostSet diffCompanyLaborCostSet);
}