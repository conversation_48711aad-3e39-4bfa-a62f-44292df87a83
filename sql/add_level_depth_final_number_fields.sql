-- 为milepost_design_plan_detail表新增层级深度和最终number字段
-- 执行时间：2025-01-XX
-- 执行人：系统管理员

USE pam_ctc;

-- 新增层级深度字段
ALTER TABLE milepost_design_plan_detail 
ADD COLUMN level_depth INT(11) COMMENT '层级深度：根节点=1，第二层=2，以此类推' AFTER level;

-- 新增最终number字段  
ALTER TABLE milepost_design_plan_detail 
ADD COLUMN final_number DECIMAL(20,6) COMMENT '最终number：当前number与所有上级number的乘积' AFTER number;

-- 为新字段添加索引以优化查询性能
CREATE INDEX idx_level_depth ON milepost_design_plan_detail(level_depth);
CREATE INDEX idx_final_number ON milepost_design_plan_detail(final_number);

-- 数据初始化：使用递归CTE计算并更新现有数据
WITH RECURSIVE hierarchy_calc AS (
    -- 根节点：level_depth = 1, final_number = number
    SELECT 
        id, 
        parent_id, 
        number,
        1 as level_depth,
        CAST(number AS DECIMAL(20,6)) as final_number
    FROM milepost_design_plan_detail 
    WHERE parent_id = -1
      AND (deleted_flag = 0 OR deleted_flag IS NULL)
    
    UNION ALL
    
    -- 子节点：level_depth = 父级level_depth + 1, final_number = 父级final_number * 当前number
    SELECT 
        c.id,
        c.parent_id,
        c.number,
        h.level_depth + 1 as level_depth,
        h.final_number * CAST(c.number AS DECIMAL(20,6)) as final_number
    FROM milepost_design_plan_detail c
    INNER JOIN hierarchy_calc h ON c.parent_id = h.id
    WHERE (c.deleted_flag = 0 OR c.deleted_flag IS NULL)
)
UPDATE milepost_design_plan_detail t
INNER JOIN hierarchy_calc h ON t.id = h.id
SET 
    t.level_depth = h.level_depth,
    t.final_number = h.final_number,
    t.update_at = NOW();

-- 验证数据初始化结果
SELECT 
    level_depth,
    COUNT(*) as count,
    MIN(final_number) as min_final_number,
    MAX(final_number) as max_final_number,
    AVG(final_number) as avg_final_number
FROM milepost_design_plan_detail 
WHERE (deleted_flag = 0 OR deleted_flag IS NULL)
  AND level_depth IS NOT NULL
GROUP BY level_depth 
ORDER BY level_depth;
