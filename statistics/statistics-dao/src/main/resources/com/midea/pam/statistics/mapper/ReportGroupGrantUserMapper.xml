<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.statistics.mapper.ReportGroupGrantUserMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.statistics.entity.ReportGroupGrantUser">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="report_group_id" jdbcType="BIGINT" property="reportGroupId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="start_date" jdbcType="DATE" property="startDate" />
    <result column="end_date" jdbcType="DATE" property="endDate" />
    <result column="deleted_flag" jdbcType="BIT" property="deletedFlag" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, report_group_id, user_id, start_date, end_date, deleted_flag, create_by, create_at, 
    update_by, update_at
  </sql>
  <select id="selectByExample" parameterType="com.midea.pam.common.statistics.entity.ReportGroupGrantUserExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from report_group_grant_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from report_group_grant_user
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from report_group_grant_user
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.midea.pam.common.statistics.entity.ReportGroupGrantUser">
    insert into report_group_grant_user (id, report_group_id, user_id, 
      start_date, end_date, deleted_flag, 
      create_by, create_at, update_by, 
      update_at)
    values (#{id,jdbcType=BIGINT}, #{reportGroupId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, 
      #{startDate,jdbcType=DATE}, #{endDate,jdbcType=DATE}, #{deletedFlag,jdbcType=BIT}, 
      #{createBy,jdbcType=BIGINT}, #{createAt,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=BIGINT}, 
      #{updateAt,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.midea.pam.common.statistics.entity.ReportGroupGrantUser">
    insert into report_group_grant_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="reportGroupId != null">
        report_group_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="deletedFlag != null">
        deleted_flag,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="reportGroupId != null">
        #{reportGroupId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=DATE},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=DATE},
      </if>
      <if test="deletedFlag != null">
        #{deletedFlag,jdbcType=BIT},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.midea.pam.common.statistics.entity.ReportGroupGrantUserExample" resultType="java.lang.Long">
    select count(*) from report_group_grant_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.midea.pam.common.statistics.entity.ReportGroupGrantUser">
    update report_group_grant_user
    <set>
      <if test="reportGroupId != null">
        report_group_id = #{reportGroupId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=DATE},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=DATE},
      </if>
      <if test="deletedFlag != null">
        deleted_flag = #{deletedFlag,jdbcType=BIT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.midea.pam.common.statistics.entity.ReportGroupGrantUser">
    update report_group_grant_user
    set report_group_id = #{reportGroupId,jdbcType=BIGINT},
      user_id = #{userId,jdbcType=BIGINT},
      start_date = #{startDate,jdbcType=DATE},
      end_date = #{endDate,jdbcType=DATE},
      deleted_flag = #{deletedFlag,jdbcType=BIT},
      create_by = #{createBy,jdbcType=BIGINT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_at = #{updateAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="findIdByQuery" parameterType="com.midea.pam.common.statistics.entity.ReportGroupGrantUser" resultType="java.lang.Long">
    SELECT
        id
    FROM
        report_group_grant_user
    where 1=1
      <if test="reportGroupId != null">
        and report_group_id=#{reportGroupId,jdbcType=BIGINT}
      </if>
      <if test="userId != null">
        and user_id=#{userId,jdbcType=BIGINT}
      </if>
  </select>
</mapper>