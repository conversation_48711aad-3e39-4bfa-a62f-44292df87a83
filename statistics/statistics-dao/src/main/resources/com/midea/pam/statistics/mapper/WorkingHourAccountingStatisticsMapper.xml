<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.statistics.mapper.WorkingHourAccountingStatisticsMapper">

    <select id="findlist" resultType="com.midea.pam.common.ctc.dto.WorkingHourAccountingDto">
       SELECT DISTINCT
         h.id,
         h.ou_id AS ouId,
         h.ou_name AS ouName,
         h.code,
         h.total_working_hour AS totalWorkingHour,
         ROUND(h.total_labor_cost,2) AS totalLaborCost,
         h.gl_period AS glPeriod,
         h.gl_date AS glDate,
         h.apply_month AS applyMonth,
         h.approve_month AS approveMonth,
         h.currency,
         h.daily_batch_num AS dailyBatchNum,
         h.daily_batch_name AS dailyBatchName,
         h.debit_subject AS debitSubject,
         h.credit_subject AS creditSubject,
         h.remark,
         h.erp_status AS erpStatus,
         h.erp_message AS erpMessage,
         h.create_by AS createBy,
         h.create_at AS createAt,
         h.update_by AS updateBy,
         h.update_at AS updateAt,
         h.deleted_flag AS deletedFlag,
         h.write_off_reason as writeOffReason,
         h.write_off_file as writeOffFile,
         h.write_off_user as writeOffUser,
         h.write_off_time as writeOffTime,
         h.status,
         u.name as createByName,
         ROUND(t.innerWorkingHour,2) AS innerWorkingHour,
         ROUND(t.outerWorkingHour,2) AS outerWorkingHour,
         ROUND(t.innerLaborCost,2) AS innerLaborCost,
         ROUND(t.outerLaborCost,2) AS outerLaborCost,
         ROUND(t.workingHourTotal,2) AS workingHourTotal,
         h.write_off_status AS writeOffStatus
       FROM pam_ctc.working_hour_accounting h
       LEFT JOIN (
          SELECT
             hd.`working_hour_accounting_id`,
             IFNULL(SUM(hd.inner_working_hour), 0) innerWorkingHour,
             IFNULL(SUM(hd.outer_working_hour), 0) outerWorkingHour,
             IFNULL(SUM(hd.inner_working_hour), 0) + IFNULL(SUM(hd.outer_working_hour), 0) workingHourTotal,
             IFNULL(SUM(hd.inner_labor_cost), 0) innerLaborCost,
             IFNULL(SUM(hd.outer_labor_cost), 0) outerLaborCost
          FROM pam_ctc.working_hour_accounting_detail hd
          GROUP BY hd.`working_hour_accounting_id`
       ) t ON h.id = t.working_hour_accounting_id
       LEFT JOIN pam_basedata.ltc_user_info u ON h.create_by = u.id AND u.status = 'Y'
       WHERE h.deleted_flag != 1
       <if test="code != null and code !=''">
           AND h.code like concat('%', #{code}, '%')
       </if>
        <if test="applyMonth != null and applyMonth !=''">
            AND h.apply_month like concat('%', #{applyMonth}, '%')
        </if>
        <if test="approveMonth != null and approveMonth !=''">
            AND h.approve_month like concat('%', #{approveMonth}, '%')
        </if>
        <if test="glPeriod != null and glPeriod !=''">
            AND h.gl_period like concat('%', #{glPeriod}, '%')
        </if>
        <if test="glDateBegin != null">
            and h.gl_date <![CDATA[>= ]]> #{glDateBegin}
        </if>
        <if test="glDateEnd != null">
            and h.gl_date <![CDATA[<= ]]> #{glDateEnd}
        </if>
        <if test="currency != null and currency !=''">
            AND h.currency like concat('%', #{currency}, '%')
        </if>
        <if test="erpStatusList != null">
            AND h.erp_status in
            <foreach collection="erpStatusList" item="erpStatus" index="index" open="(" separator="," close=")">
                #{erpStatus}
            </foreach>
        </if>
        <if test="statusList != null">
            AND h.status in
            <foreach collection="statusList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="ouNameList != null">
            AND h.ou_name in
            <foreach collection="ouNameList" item="ouName" index="index" open="(" separator="," close=")">
                #{ouName}
            </foreach>
        </if>
        <if test="ouList != null">
            and h.ou_id in
            <foreach collection="ouList" item="ouId" index="index" open="(" separator="," close=")">
                #{ouId}
            </foreach>
        </if>
        <if test="workingHourAccountingIdList != null">
            and h.id in
            <foreach collection="workingHourAccountingIdList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="writeOffStatusArr != null">
            and h.write_off_status in
            <foreach collection="writeOffStatusArr" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY h.gl_date DESC,h.code DESC
    </select>
  <select id="findDetail" resultType="com.midea.pam.common.ctc.dto.WorkingHourAccountingDetailDto">
      SELECT
      cd.id,
      cd.working_hour_accounting_id AS workingHourAccountingId,
      p.id AS projectId,
      p.code AS projectCode,
      p.name AS projectName,
      p.type AS projectType,
      ROUND(
      (
      IFNULL(cd.cost_total,0)
      ),
      2
      ) AS costTotal,
      ROUND(
      (
      IFNULL(cd.actual_working_hours,0)
      ),
      2
      ) AS actualWorkingHours,
      cd.user_name AS userName,
      u.username AS mip,
      cd.project_role AS projectRole,
      cd.apply_date AS applyDate,
      cd.fill_in_date AS fillInDate,
      cd.system_apply_date AS systemApplyDate,
      cd.cost_money AS costMoney,
      cd.hard_working AS hardWorking,
      h.currency AS currency,
      h.code AS CODE,
      un.unit_name AS unitName,
      h.gl_period AS glPeriod,
      h.ou_name AS ouName,
      wh.apply_org AS deptName,
      cd.user_type AS userType
      FROM
      pam_ctc.labor_cost_detail cd
      LEFT JOIN pam_ctc.working_hour_accounting h
      ON cd.`working_hour_accounting_id` = h.id
      AND h.`deleted_flag` != 1
      LEFT JOIN pam_basedata.ltc_user_info u
      ON cd.user_id = u.id
      AND u.`status` = 'Y'
      LEFT JOIN pam_ctc.`project` p
      ON cd.`project_id` = p.`id`
      AND p.`deleted_flag` != 1
      LEFT JOIN pam_basedata.`unit` un
      ON p.`unit_id` = un.`id`
      AND un.`delete_flag` != 1
      left join pam_ctc.working_hour wh on cd.working_hour_id = wh.id
      WHERE cd.deleted_flag = 0
      and cd.working_hour_accounting_id in
      <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
          #{id}
      </foreach>
  </select>

    <select id="findWorkingAccountWriteOff" resultType="com.midea.pam.common.ctc.dto.LaborCostDetailDto">
        select
        cd.working_hour_accounting_id workingHourAccountingId,
        cd.project_id projectId,
        cd.user_id userId,
        p.code                        projectCode,
        p.name                        projectName,
        cd.user_name                  userName,
        u.username                    mipName,
        sum(ROUND(ifnull(cd.cost_money,0), 2))       costMoney,
        sum(ROUND(ifnull(h.total_working_hour,0),2)) totalWorkingHour,
        sum(ROUND(ifnull(h.total_labor_cost,0), 2))  totalLaborCost,
        cd.user_type                  userType,
        sum(ROUND(ifnull(cd.cost_total,0), 2))       costTotal,
        cd.fill_in_date               fillInDate,
        ifnull(cc.carry_status,0)               carryStatus
        from pam_ctc.labor_cost_detail cd
        inner join pam_ctc.working_hour w on cd.working_hour_id = w.id and w.delete_flag !=1
        LEFT JOIN pam_ctc.working_hour_accounting h ON cd.`working_hour_accounting_id` = h.id AND h.`deleted_flag` != 1
        LEFT JOIN pam_basedata.ltc_user_info u ON cd.user_id = u.id AND u.`status` = 'Y'
        LEFT JOIN pam_ctc.`project` p ON cd.`project_id` = p.`id` AND p.`deleted_flag` != 1
        LEFT JOIN pam_ctc.cost_collection cc ON cd.cost_collection_id = cc.id
        WHERE cd.deleted_flag = 0 and cd.working_hour_accounting_id is not null
        <if test="projectCode != null">
            and p.code = #{projectCode}
        </if>
        <if test="userIds != null">
            and u.id in
            <foreach collection="userIds" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="fillInDateStart != null">
            and cd.fill_in_date <![CDATA[>= ]]>  #{fillInDateStart}
        </if>
        <if test="fillInDateEnd != null">
            and cd.fill_in_date <![CDATA[<= ]]> #{fillInDateEnd}
        </if>
        group by p.code,p.name,u.username,cc.carry_status,cd.working_hour_accounting_id
    </select>

    <select id="workingHourAccountingDoRemind" resultType="java.lang.Long">
      SELECT h.id
          FROM pam_ctc.working_hour_accounting h
          WHERE h.deleted_flag != 1
            AND h.erp_status in ('4')
            AND h.status in ('0')
            <if test="ouIds != null">
                and h.ou_id in
                <foreach collection="ouIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
          union
          SELECT h.id
          FROM pam_ctc.working_hour_accounting h
          WHERE h.deleted_flag != 1
            AND ((h.erp_status = 2 and TIMESTAMPDIFF(HOUR, h.create_at, now()) > 2))
            AND h.status in ('0')
            <if test="ouIds != null">
                and h.ou_id in
                <foreach collection="ouIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        union
        SELECT h.id
        FROM pam_ctc.working_hour_accounting h
        left join pam_ctc.agency_syn_info asy on h.id = asy.apply_no and asy.deleted_flag = 0
        WHERE h.deleted_flag != 1
        AND (h.erp_status = 3 and TIMESTAMPDIFF(HOUR, asy.syn_start_time, now()) > 2)
        AND h.status in ('0')
        <if test="ouIds != null">
            and h.ou_id in
            <foreach collection="ouIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="findDetailInfo" resultType="com.midea.pam.common.ctc.excelVo.WorkingHourAccountingDetailInfoExcelVo">
        select
            wha.code code,
            wha.status status,
            whad.project_code projectCode,
            whad.project_name projectName,
            ROUND(ifnull(whad.inner_labor_cost,0), 2) innerLaborCost,
            ROUND(ifnull(whad.outer_labor_cost,0), 2) outerLaborCost,
            ROUND(ifnull(whad.inner_working_hour,0), 2)  innerWorkingHour,
            ROUND(ifnull(whad.outer_working_hour,0), 2)  outerWorkingHour
        from (
            select
                working_hour_accounting_id,
                project_code,
                project_name,
                inner_labor_cost,
                outer_labor_cost,
                inner_working_hour,
                outer_working_hour
            from pam_ctc.working_hour_accounting_detail
            where deleted_flag = 0
            and working_hour_accounting_id in
            <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        ) whad left join (
            select id, code, status, gl_date
            from pam_ctc.working_hour_accounting
        ) wha on whad.working_hour_accounting_id = wha.id
        order by wha.gl_date desc, wha.code desc
    </select>
</mapper>