<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.statistics.mapper.CostCollectionStatisticsExtMapper">
    <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.dto.CostCollectionDto">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="project_milepost_id" jdbcType="BIGINT" property="projectMilepostId"/>
        <result column="collection_date" jdbcType="DATE" property="collectionDate"/>
        <result column="cost_date" jdbcType="DATE" property="costDate"/>
        <result column="type" jdbcType="TINYINT" property="type"/>
        <result column="ou_id" jdbcType="BIGINT" property="ouId"/>
        <result column="ou_name" jdbcType="VARCHAR" property="ouName"/>
        <result column="project_id" jdbcType="BIGINT" property="projectId"/>
        <result column="project_code" jdbcType="VARCHAR" property="projectCode"/>
        <result column="project_name" jdbcType="VARCHAR" property="projectName"/>
        <result column="project_type" jdbcType="VARCHAR" property="projectType"/>
        <result column="cost_method_main" jdbcType="VARCHAR" property="costMethodMain"/>
        <result column="currency" jdbcType="VARCHAR" property="currency"/>
        <result column="material_actual_cost" jdbcType="DECIMAL" property="materialActualCost"/>
        <result column="material_outsource_cost" jdbcType="DECIMAL" property="materialOutsourceCost"/>
        <result column="material_difference_cost" jdbcType="DECIMAL" property="materialDifferenceCost"/>
        <result column="inner_labor_cost" jdbcType="DECIMAL" property="innerLaborCost"/>
        <result column="outer_labor_cost" jdbcType="DECIMAL" property="outerLaborCost"/>
        <result column="fee_cost" jdbcType="DECIMAL" property="feeCost"/>
        <result column="carry_status" jdbcType="BIT" property="carryStatus"/>
        <result column="carry_date" jdbcType="TIMESTAMP" property="carryDate"/>
        <result column="gl_period" jdbcType="VARCHAR" property="glPeriod"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt"/>
        <result column="deleted_flag" jdbcType="BIT" property="deletedFlag"/>
        <result column="inner_working_hour" jdbcType="DECIMAL" property="innerWorkingHour"/>
        <result column="outer_working_hour" jdbcType="DECIMAL" property="outerWorkingHour"/>
        <result column="apply_month" jdbcType="VARCHAR" property="applyMonth"/>
        <result column="approve_month" jdbcType="VARCHAR" property="approveMonth"/>
        <result column="carryover_bill_id" jdbcType="BIGINT" property="carryoverBillId"/>
        <result column="bill_num" jdbcType="VARCHAR" property="billNum"/>
        <result column="type_name" jdbcType="VARCHAR" property="typeName"/>
        <result column="hard_working" jdbcType="VARCHAR" property="hardWorking"/>
    </resultMap>

    <resultMap id="LaborCostDto" type="com.midea.pam.common.ctc.dto.LaborCostDetailDto">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="cost_collection_id" jdbcType="BIGINT" property="costCollectionId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="user_type" jdbcType="VARCHAR" property="userType"/>
        <result column="project_role" jdbcType="VARCHAR" property="projectRole"/>
        <result column="apply_date" jdbcType="TIMESTAMP" property="applyDate"/>
        <result column="system_apply_date" jdbcType="TIMESTAMP" property="systemApplyDate"/>
        <result column="actual_working_hours" jdbcType="DECIMAL" property="actualWorkingHours"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt"/>
        <result column="deleted_flag" jdbcType="BIT" property="deletedFlag"/>
        <result column="cost_money" jdbcType="DECIMAL" property="costMoney"/>
        <result column="cost_total" jdbcType="DECIMAL" property="costTotal"/>
        <result column="project_id" jdbcType="BIGINT" property="projectId"/>
        <result column="fill_in_date" jdbcType="DATE" property="fillInDate"/>
        <result column="carryover_bill_id" jdbcType="BIGINT" property="carryoverBillId"/>
        <result column="carryover_gl_period" jdbcType="VARCHAR" property="carryoverGlPeriod"/>
        <result column="apply_month" jdbcType="VARCHAR" property="applyMonth"/>
        <result column="approve_month" jdbcType="VARCHAR" property="approveMonth"/>

        <result column="project_code" jdbcType="VARCHAR" property="projectCode"/>
        <result column="project_name" jdbcType="VARCHAR" property="projectName"/>
        <result column="project_type" jdbcType="VARCHAR" property="projectType"/>
        <result column="ou_name" jdbcType="VARCHAR" property="ouName"/>
        <result column="currency" jdbcType="VARCHAR" property="currency"/>
        <result column="type_name" jdbcType="VARCHAR" property="typeName"/>
        <result column="organization_name" jdbcType="VARCHAR" property="organizationName"/>
        <result column="hard_working" jdbcType="VARCHAR" property="hardWorking"/>
        <result column="working_hour_id" jdbcType="BIGINT" property="workingHourId"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, project_milepost_id, collection_date, cost_date, type, ou_id, ou_name, project_id,
        project_code, project_name, project_type, cost_method_main, currency, material_actual_cost,
        material_outsource_cost, material_difference_cost, inner_labor_cost, outer_labor_cost,
        fee_cost, carry_status, carry_date, gl_period, create_by, create_at, update_by, update_at,
        deleted_flag
    </sql>

    <sql id="query_condition">
        and (col.material_actual_cost != 0 or
             col.material_penalty_cost != 0 or
             col.material_outsource_cost != 0 or
             col.inner_labor_cost != 0 or
             col.outer_labor_cost != 0 or
             col.material_difference_cost != 0 or
             col.fee_cost != 0 or
             col.asset_deprn_cost != 0
            )
        and col.deleted_flag = 0
        <if test="id !=null">
            and col.id = #{id}
        </if>
        <if test="projectCode != null">
            and p.code like concat('%', #{projectCode}, '%')
        </if>
        <if test="projectName != null">
            and p.name like concat('%', #{projectName}, '%')
        </if>
        <if test="costMethodMain != null">
            and col.cost_method_main = #{costMethodMain}
        </if>
        <if test="ouIds != null">
            and col.ou_id in
            <foreach collection="ouIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="startTime != null">
            and col.collection_date <![CDATA[>= ]]> #{startTime}
        </if>
        <if test="endTime != null">
            and col.collection_date <![CDATA[<= ]]> #{endTime}
        </if>
        <if test="costDateStart != null">
            and col.cost_date <![CDATA[>= ]]> #{costDateStart}
        </if>
        <if test="costDateEnd != null">
            and col.cost_date <![CDATA[<= ]]> #{costDateEnd}
        </if>
        <if test="carryStatus != null">
            and col.carry_status = #{carryStatus}
        </if>
        <if test="glPeriod != null">
            and col.gl_period like concat('%',#{glPeriod},'%')
        </if>
        <if test="projectTypes != null">
            and col.project_type in
            <foreach collection="projectTypes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="currency != null">
            and col.currency like concat('%',#{currency},'%')
        </if>
    </sql>

    <select id="page" parameterType="com.midea.pam.common.ctc.dto.CostCollectionDto"
            resultType="com.midea.pam.common.ctc.dto.CostCollectionDto">
        select
            col.id,
            col.collection_date collectionDate,
            col.cost_date costDate,
            col.carry_status carryStatus,
            col.gl_period glPeriod,
            col.project_id projectId,
            col.project_code projectCode,
            p.name projectName,
            p.unit_id unitId,
            col.project_type projectType,
            truncate(col.material_actual_cost, 2) materialActualCost,
            truncate(col.material_outsource_cost, 2) materialOutsourceCost,
            truncate(col.material_penalty_cost, 2) materialPenaltyCost,
            truncate(col.material_difference_cost, 2) materialDifferenceCost,
            truncate(col.inner_labor_cost, 2) innerLaborCost,
            truncate(col.outer_labor_cost, 2) outerLaborCost,
            truncate(col.fee_cost, 2) feeCost,
            truncate(col.asset_deprn_cost, 2) assetDeprnCost,
            col.currency,
            col.ou_name ouName
        from
            pam_ctc.cost_collection col
        left join pam_ctc.project p on
            col.project_id = p.id
        where
            1 = 1
        <include refid="query_condition" />
        order by col.collection_date desc
    </select>

    <select id="queryCollectionSummaryExcludeSumWorkingHour" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        detail.id,
        col.ou_id,
        col.ou_name,
        col.project_id,
        col.project_code,
        col.project_name,
        col.project_type,
        col.currency,
        ifnull(detail.status,0) as costCollectionStatus,
        DATE_FORMAT(detail.apply_date,'%Y-%m') apply_month,
        DATE_FORMAT(detail.system_apply_date,'%Y-%m') approve_month,
        pt.name type_name,
        detail.hard_working,
        case when detail.hard_working is null or detail.hard_working = '' then 0  else 1 end as hardWorkingStatus
        FROM
        pam_ctc.cost_collection col
        INNER JOIN pam_ctc.labor_cost_detail detail ON col.id = detail.cost_collection_id
        inner join pam_ctc.project p on col.project_id = p.id
        inner join pam_ctc.project_type pt on p.type = pt.id
        inner join pam_ctc.project_profit pp on pp.project_id = p.id and pp.deleted_flag = 0 and pp.cost_method_main is not null and pp.cost_method_main != ''
        where
        col.deleted_flag = 0
        and detail.deleted_flag = 0
        and detail.working_hour_accounting_id is NULL
        and detail.user_type != 2
        <if test="ouId != null">
            and col.ou_id in
            <foreach collection="ouId" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="projectId != null">
            and col.project_id = #{projectId}
        </if>
        <if test="status != null">
            and ifnull(detail.status,0) in
            <foreach collection="status" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="projectCode != null">
            and col.project_code like concat('%', #{projectCode}, '%')
        </if>
        <if test="projectName != null">
            and p.name like concat('%', #{projectName}, '%')
        </if>
        <if test="applyMonth != null">
            and detail.apply_date like concat('%', #{applyMonth}, '%')
        </if>
        <if test="approveMonth != null">
            and detail.system_apply_date like concat('%', #{approveMonth}, '%')
        </if>
        <if test="projectFuzzyLike != null">
            and (
            col.project_name like concat('%', #{projectFuzzyLike}, '%')
            or col.project_code like concat('%', #{projectFuzzyLike}, '%')
            )
        </if>
        <if test="projectTypes != null ">
            and col.project_type in
            <foreach collection="projectTypes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="currency !=null">
            and col.currency like concat('%', #{currency}, '%')
        </if>
        GROUP BY
        col.project_id, costCollectionStatus, DATE_FORMAT(detail.apply_date, '%Y-%m'), DATE_FORMAT(detail.system_apply_date, '%Y-%m')
        ORDER BY
        DATE_FORMAT(detail.system_apply_date,'%Y-%m') DESC, col.project_id ASC
    </select>

    <select id="sumProjectCostGroupByMonthAndUserType" resultType="com.midea.pam.common.ctc.dto.LaborCostDetailDto">
        SELECT
        project_id as projectId,
        user_type as userType,
        sum(cost_total) as costTotal,
        sum(actual_working_hours) as actualWorkingHours,
        DATE_FORMAT(apply_date,'%Y-%m') as applyMonth,
        DATE_FORMAT(system_apply_date,'%Y-%m') as approveMonth
        FROM pam_ctc.labor_cost_detail
        WHERE
          deleted_flag = 0
          AND working_hour_accounting_id is NULL
        <if test="projectIds != null">
            AND project_id IN
            <foreach collection="projectIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY project_id, user_type, DATE_FORMAT(system_apply_date,'%Y-%m'), DATE_FORMAT(apply_date,'%Y-%m')
    </select>

    <select id="queryLaborSummaryDetail" parameterType="java.util.Map" resultMap="LaborCostDto">
        SELECT
        col.ou_name,
        col.project_code,
        col.project_name,
        col.project_type,
        col.currency,
        p.unit_id type_name,
        detail.*,
        col.gl_period AS carryover_gl_period,
        DATE_FORMAT(detail.apply_date, '%Y-%m') as apply_month,
        DATE_FORMAT(detail.system_apply_date, '%Y-%m') as approve_month,
        lo.name_path organization_name
        FROM
        pam_ctc.cost_collection col
        INNER JOIN pam_ctc.labor_cost_detail detail ON col.id = detail.cost_collection_id
        inner join pam_ctc.project p on col.project_id = p.id
        inner join pam_ctc.project_type pt on p.type = pt.id
        inner join pam_basedata.ltc_org_user lou on detail.user_id = lou.user_id AND lou.status='Y' AND lou.type=1
        inner join pam_basedata.ltc_organization lo on lou.org_id = lo.id
        where 1=1
        <if test="projectId != null">
            and col.project_id = #{projectId}
        </if>
        <if test="workingHourAccountingId != null">
            and detail.working_hour_accounting_id = #{workingHourAccountingId}
        </if>
        <if test="accountingFlag != null">
            and detail.working_hour_accounting_id is null
        </if>
        <if test="applyMonth != null">
            and DATE_FORMAT(detail.apply_date, '%Y-%m') = #{applyMonth}
        </if>
        <if test="approveMonth != null">
            and DATE_FORMAT(detail.system_apply_date, '%Y-%m') = #{approveMonth}
        </if>
        <if test="projectIds != null">
            and col.project_id in
            <foreach collection="projectIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getMaterialActualCostDetail" parameterType="com.midea.pam.common.ctc.dto.CostCollectionDto"
            resultType="com.midea.pam.common.ctc.dto.MaterialActualCostDetailDto">
        select
            distinct macd.id,
            macd.type,
            col.collection_date collectionDate,
            col.cost_date costDate,
            col.carry_status carryStatus,
            col.gl_period glPeriod,
            col.project_code projectCode,
            p.name projectName,
            macd.code,
            macd.actual_date actualDate,
            macd.material_code materialCode,
            m.item_info itemInfo,
            truncate(macd.actual_amount, 2) actualAmount,
            macd.unit,
            truncate(macd.actual_cost, 2) actualCost,
            truncate(ifnull(macd.actual_cost, 0)* ifnull(macd.actual_amount, 0), 2) totalAmount,
            col.currency,
            col.ou_name ouName,
            (case
            when macd.`type` = 1 then mgd.wbs_summary_code
            when macd.`type` = 2 then mrd.wbs_summary_code
            else ''
            end) as wbsSummaryCode,
            (case
            when macd.`type` = 1 then mgd.activity_code
            when macd.`type` = 2 then mrd.activity_code
            else ''
            end) as activityCode
        from
            pam_ctc.material_actual_cost_detail macd
        inner join pam_ctc.cost_collection col on
            macd.cost_collection_id = col.id
        inner join pam_ctc.project p on
            col.project_id = p.id
        inner join pam_ctc.project_type pt on
            p.type = pt.id
        inner join pam_basedata.material m on
            macd.material_code = m.item_code
            and (m.delete_flag = 0 or m.delete_flag is null)
        left join pam_ctc.material_get_detail mgd on
            macd.material_form_id = mgd.id and macd.`type` = 1 and mgd.deleted_flag = 0
        left join pam_ctc.material_return_detail mrd on
            macd.material_form_id = mrd.id and macd.`type` = 2 and mrd.deleted_flag = 0
        where
            macd.deleted_flag = 0
        <include refid="query_condition" />
        order by col.project_id desc, col.collection_date desc
    </select>

    <select id="getCarryoverBillOutsourcingCostCollection"
            parameterType="com.midea.pam.common.ctc.dto.CostCollectionDto"
            resultType="com.midea.pam.common.ctc.dto.CarryoverBillOutsourcingCostCollectionDto">
        select
            distinct abcca.id,
            abcca.collection_date as collectionDate,
            abcca.cost_date as costDate,
            abcca.carry_status as carryStatus,
            abcca.gl_period as glPeriod,
            abcca.project_code as projectCode,
            abcca.project_name as projectName,
            pc.code as purchaseContractCode,
            pc.name as purchaseContractName,
            pc.currency as purchaseContractCurrency,
            pc.vendor_name as vendorName,
            pc.vendor_site_code as vendorSiteCode,
            TRUNCATE(pc.excluding_tax_amount, 2) as excludingTaxAmount,
            TRUNCATE(cbocc.cost_ratio_config_detail_cost_ratio, 2) as costRatioConfigDetailCostRatio,
            abcca.currency,
            TRUNCATE(cbocc.current_outsourcing_contract_cost, 2) as currentOutsourcingContractCost,
            abcca.ou_name as ouName,
            TRUNCATE(mocd.execute_amount, 2) as executeAmount,
            mocd.progress_end_time as progressEndTime,
            GROUP_CONCAT(distinct pcb.wbs_summary_code separator ',') as wbsSummaryCode,
            GROUP_CONCAT(distinct pcb.activity_code separator ',') as activityCode
        from
            (
            select
                col.id,
                cb.id as carryBillId,
                cb.bill_num,
                col.collection_date,
                col.cost_date,
                col.carry_status,
                col.gl_period,
                col.project_code,
                p.name as project_name,
                col.currency,
                col.ou_name,
                cbccr.cost_collection_id
            from
                pam_ctc.cost_collection col
                inner join pam_ctc.project p on
                col.project_id = p.id
                inner join pam_ctc.project_type pt on
                p.type = pt.id
                left join pam_ctc.carryover_bill_cost_collection_rel cbccr on
                cbccr.cost_collection_id = col.id
                and cbccr.deleted_flag = 0
                left join pam_ctc.carryover_bill cb on
                cbccr.carryover_bill_id = cb.id
                and cbccr.carryover_batch_num = cb.carryover_batch_num
                and cb.status = 1
                and cb.reverse_status = 1
                and cb.deleted_flag = 0
            where
                1 = 1
                <include refid="query_condition" />
                order by col.project_id desc, col.collection_date desc
            ) abcca
        inner join pam_ctc.material_outsource_cost_detail mocd on
            mocd.cost_collection_id = abcca.id
        inner join pam_ctc.purchase_contract pc on
            mocd.purchase_contract_code = pc.code
        left join pam_ctc.purchase_contract_budget pcb on
            pcb.purchase_contract_id = pc.id
            and pcb.deleted_flag = 0
        left join pam_ctc.carryover_bill_outsourcing_cost_collection cbocc on
            cbocc.carryover_bill_id = abcca.carryBillId
            and cbocc.deleted_flag = 0
        group by abcca.id
    </select>

    <select id="getLaborCostDetail" parameterType="com.midea.pam.common.ctc.dto.CostCollectionDto"
            resultType="com.midea.pam.common.ctc.dto.LaborCostDetailDto">
        select
            distinct lcd.id,
            col.collection_date collectionDate,
            col.cost_date costDate,
            col.carry_status carryStatus,
            col.gl_period glPeriod,
            col.project_code projectCode,
            p.name projectName,
            lcd.user_type userType,
            lcd.user_name userName,
            lui.username mipName,
            lcd.project_role projectRole,
            lcd.fill_in_date fillInDate,
            lcd.apply_date applyDate,
            lcd.system_apply_date systemApplyDate,
            lcd.actual_working_hours actualWorkingHours,
            lcd.cost_money costMoney,
            lcd.cost_total costTotal,
            col.currency,
            pt.name typeName,
            col.ou_name ouName,
            concat(p.code, '-', wh.wbs_budget_code) as wbsSummaryCode,
            wh.project_activity_code as activityCode
        from
            pam_ctc.labor_cost_detail lcd
        inner join pam_ctc.cost_collection col on
            lcd.cost_collection_id = col.id
        inner join pam_ctc.project p on
            col.project_id = p.id
        inner join pam_ctc.project_type pt on
            p.type = pt.id
        left join pam_basedata.ltc_user_info lui on
            lcd.user_id = lui.id
        left join pam_ctc.working_hour wh on
            wh.id = lcd.working_hour_id and wh.delete_flag  = 0
        where
            lcd.deleted_flag = 0
        <include refid="query_condition" />
        order by col.project_id desc, col.collection_date desc
    </select>

    <select id="getFeeCostDetail" parameterType="com.midea.pam.common.ctc.dto.CostCollectionDto"
            resultType="com.midea.pam.common.ctc.dto.FeeCostDetailDto">
        select
            distinct fcd.id,
            col.collection_date collectionDate,
            col.cost_date costDate,
            col.carry_status carryStatus,
            col.gl_period glPeriod,
            col.project_code projectCode,
            p.name projectName,
            fcd.type,
            fcd.order_code orderCode,
            fcd.gl_date glDate,
            fcd.vendor_name vendorName,
            fcd.vendor_site_code vendorSiteCode,
            fcd.invoice_currency invoiceCurrency,
            truncate(fcd.invoice_amount, 2) invoiceAmount,
            truncate(fcd.local_currency_amount, 2) localCurrencyAmount,
            fcd.local_currency localCurrency,
            pt.name typeName,
            col.ou_name ouName,
            concat(pwb.project_code, '-', pwb.wbs_full_code) as wbsSummaryCode,
            pwb.activity_code as activityCode
        from
            pam_ctc.fee_cost_detail fcd
        inner join pam_ctc.cost_collection col on
            fcd.cost_collection_id = col.id
        inner join pam_ctc.project p on
            col.project_id = p.id
        inner join pam_ctc.project_type pt on
            p.type = pt.id
        left join  pam_ctc.ems_pam_fee_detail epfd on
            epfd.order_id  = fcd.order_line_id and epfd.import_erp_status = 'SUCCESS'
        left join pam_ctc.project_fee_collection pfc on
            pfc.ems_budget_id = epfd.budget_node_id and pfc.deleted_flag = 0
        left join pam_ctc.project_wbs_budget pwb on
            pwb.id = pfc.source_id and pwb.deleted_flag = 0
        where
            fcd.deleted_flag = 0
        <include refid="query_condition" />
        order by col.project_id desc, col.collection_date desc
    </select>

    <select id="getDifferenceShareResultDetail"
            resultType="com.midea.pam.common.ctc.dto.DifferenceShareResultDetailDto">
        select distinct
          col.collection_date as collectionDate,
          col.carry_status as carryStatus,
          col.gl_period as carryGlPeriod,
          col.project_code as projectCode,
          col.currency as currency,
          p.name as projectName,
          col.ou_name ouName,
          dsrd.id as id,
          dsrd.segment3 as segment3,
          dsrd.account_code as accountCode,
          dsrd.cost_date as costDate,
          dsrd.project_id as projectId,
          dsrd.share_radio as shareRadio,
          dsrd.amount as amount,
          dsrd.status as status,
          dsrd.cost_collection_id as costCollectionId,
          dsrd.difference_share_account_id as differenceShareAccountId,
          dsrd.difference_type as differenceType,
          dsrd.deleted_flag as deletedFlag,
          dsrd.create_by as createBy,
          dsrd.create_at as createAt,
          dsrd.update_by as updateBy,
          dsrd.update_at as updateAt,
          dsa.gl_period as glPeriod,
          dsa.account_num as accountNum,
          lui.name as createByName
        from pam_ctc.difference_share_result_detail dsrd
        inner join pam_ctc.difference_share_account dsa on dsrd.difference_share_account_id = dsa.id
        inner join pam_ctc.cost_collection col on
            dsrd.cost_collection_id = col.id
        inner join pam_ctc.project p on
            col.project_id = p.id
        inner join pam_ctc.project_type pt on
            p.type = pt.id
        left join pam_basedata.ltc_user_info lui
            on dsrd.create_by = lui.id
        where
            dsrd.deleted_flag = 0
        <include refid="query_condition" />
        order by col.project_id desc, col.collection_date desc
    </select>

    <select id="getVendorPenaltyCostDetail" resultType="com.midea.pam.common.ctc.dto.VendorPenaltyCostDetailDto" parameterType="com.midea.pam.common.ctc.dto.CostCollectionDto">
        select
            fcd.penalty_code as penaltyCode,fcd.vendor_code as vendorCode,
            fcd.vendor_name as vendorName,fcd.currency,
            fcd.conversion_rate as conversionRate,fcd.amount,fcd.project_cost as projectCost,
            col.collection_date as collectionDate,col.cost_date as costDate,
            col.project_code as projectCode,col.project_name as projectName,
            case when col.carry_status='1' then '已结转' when col.carry_status='2' then '无需入账' else '未结转' end as carryStatus,
            col.gl_period as glPeriod,
            col.ou_name as ouName,
            vpd.wbs_code as wbsSummaryCode,
            vpd.project_activity_code as activityCode
        from
            pam_ctc.vendor_penalty_cost_detail fcd
            inner join pam_ctc.cost_collection col on
            fcd.collection_id = col.id
            inner join pam_ctc.project p on
            col.project_id = p.id
            left join pam_ctc.vendor_penalty_detail vpd on
            fcd.penalty_detail_id = vpd.id
        where
            fcd.deleted_flag = 0
            <include refid="query_condition" />
            order by col.project_id desc, col.collection_date desc
    </select>

    <select id="queryProjectAssetDeprnCostDetail" resultType="com.midea.pam.common.ctc.dto.ProjectAssetDeprnCostDetailDto">
        select
            col.collection_date collectionDate,
            col.cost_date costDate,
            col.carry_status carryStatus,
            col.gl_period glPeriod,
            col.project_code projectCode,
            col.project_name projectName,
            col.ou_name ouName,
            padcd.asset_number as assetNumber,
            padcd.description,
            padcd.period_name as periodName,
            padcd.deprn_amount as deprnAmount
        from
            pam_ctc.project_asset_deprn_cost_detail padcd
        inner join pam_ctc.cost_collection col on
            padcd.cost_collection_id = col.id
        inner join pam_ctc.project p on
            col.project_id = p.id
        where
            padcd.deleted_flag = 0
        <include refid="query_condition" />
        order by col.project_id desc, col.collection_date desc
    </select>
</mapper>