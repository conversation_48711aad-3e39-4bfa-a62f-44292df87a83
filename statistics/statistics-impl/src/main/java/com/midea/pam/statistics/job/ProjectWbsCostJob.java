package com.midea.pam.statistics.job;

import com.midea.pam.common.enums.ProjectCostExecuteStatus;
import com.midea.pam.common.statistics.entity.ProjectCostExecuteRecord;
import com.midea.pam.statistics.mapper.ProjectWbsExecuteDetailExtMapper;
import com.midea.pam.statistics.project.service.ProjectCostExecuteRecordService;
import com.midea.pam.statistics.project.service.ProjectWbsCostSummaryService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/07/19
 * @description 项目成本定时任务
 */
@Component
public class ProjectWbsCostJob {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private ProjectWbsCostSummaryService projectWbsCostSummaryService;

    @Resource
    private ProjectCostExecuteRecordService projectCostExecuteRecordService;

    @Resource
    private ProjectWbsExecuteDetailExtMapper projectWbsExecuteDetailExtMapper;

    @XxlJob("projectWbsCostJob")
    public ReturnT<String> execute(String s) throws Exception {
        logger.info("---------------- 项目成本wbs统计定时任务开始 --------------");
        ProjectCostExecuteRecord projectCostExecuteRecord = projectWbsCostSummaryService.generateExecuteRecord();
        Long executeId = projectCostExecuteRecord.getId();
        long totalStart = System.currentTimeMillis();
        try {
            // 初始化数据，数据分组
            List<Long> list = projectWbsCostSummaryService.queryAllProjectForJob();
            int num = list.size();
            logger.info("本次处理的数据大小:{}", num);
            projectCostExecuteRecord.setNum(num);
            //根据项目ID查询数据并入库,返回执行条数-多线程处理
            projectWbsCostSummaryService.handleList(list, executeId);
            long totalEnd = System.currentTimeMillis();
            long totalM = totalEnd - totalStart;
            logger.info("项目成本总耗时:{} ms", totalM);

            projectCostExecuteRecord.setEndTime(new Date());
            projectCostExecuteRecord.setCostTime(totalM);
            projectCostExecuteRecord.setOrNotWbs(1);
            projectCostExecuteRecord.setStatus(ProjectCostExecuteStatus.SUCCESS.getCode());
            projectCostExecuteRecordService.updateByPrimaryKeySelective(projectCostExecuteRecord);
        } catch (Exception e) {
            long totalEnd = System.currentTimeMillis();
            long totalM = totalEnd - totalStart;
            projectCostExecuteRecord.setEndTime(new Date());
            projectCostExecuteRecord.setCostTime(totalM);
            projectCostExecuteRecord.setOrNotWbs(1);
            projectCostExecuteRecord.setStatus(ProjectCostExecuteStatus.FAILURE.getCode());
            projectCostExecuteRecordService.updateByPrimaryKeySelective(projectCostExecuteRecord);
            logger.error("项目WBS成本统计执行失败：{}", executeId, e);
            throw e;
        }
        logger.info("---------------- 项目成本wbs统计定时任务结束 --------------");
        return ReturnT.SUCCESS;
    }

}
