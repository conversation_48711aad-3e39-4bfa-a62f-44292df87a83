package com.midea.pam.statistics.project.service.impl;

import com.midea.pam.common.basedata.entity.Unit;
import com.midea.pam.common.constants.WbsBudgetFieldConstant;
import com.midea.pam.common.ctc.cache.WbsTemplateRuleCache;
import com.midea.pam.common.ctc.dto.ProjectWbsBudgetDto;
import com.midea.pam.common.ctc.dto.ProjectWbsBudgetSummaryDto;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectActivity;
import com.midea.pam.common.ctc.entity.ProjectActivityExample;
import com.midea.pam.common.ctc.entity.ProjectWbsBudget;
import com.midea.pam.common.ctc.entity.ProjectWbsBudgetDynamic;
import com.midea.pam.common.ctc.entity.ProjectWbsBudgetExample;
import com.midea.pam.common.ctc.entity.ProjectWbsBudgetSummary;
import com.midea.pam.common.enums.ProjectWbsBudgetSummarySummaryTypeEnums;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.ProjectWbsBudgetUtils;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.statistics.mapper.ProjectActivityMapper;
import com.midea.pam.statistics.mapper.ProjectMapper;
import com.midea.pam.statistics.mapper.ProjectWbsBudgetDynamicMapper;
import com.midea.pam.statistics.mapper.ProjectWbsBudgetMapper;
import com.midea.pam.statistics.mapper.ProjectWbsBudgetSummaryMapper;
import com.midea.pam.statistics.mapper.ProjectWbsCostSummaryExtMapper;
import com.midea.pam.statistics.project.service.ProjectWbsBudgetSummaryService;
import com.midea.pam.statistics.project.service.ProjectWbsExecuteDetailService;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

public class ProjectWbsBudgetSummaryServiceImpl implements ProjectWbsBudgetSummaryService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private ProjectWbsBudgetMapper projectWbsBudgetMapper;
    @Resource
    private ProjectActivityMapper projectActivityMapper;
    @Resource
    private ProjectWbsBudgetSummaryMapper projectWbsBudgetSummaryMapper;
    @Resource
    private ProjectWbsCostSummaryExtMapper projectWbsCostSummaryExtMapper;
    @Resource
    private ProjectWbsBudgetDynamicMapper projectWbsBudgetDynamicMapper;
    @Resource
    private ProjectWbsExecuteDetailService projectWbsExecuteDetailService;

    /**
     * 更新project_wbs_budget，生成System记录
     *
     * @param projectWbsBudgetDtos
     * @description 更新project_wbs_budget
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void updateBatchAll(List<ProjectWbsBudgetDto> projectWbsBudgetDtos, List<Long> projectIds) {
        logger.info("项目成本wbs统计定时任务，异步更新wbs预算正式表开始：{}", StringUtils.join(projectIds, ","));

        // 项目System预算
        List<ProjectWbsBudgetDto> insertList = projectWbsBudgetDtos.stream().filter(entity -> Objects.isNull(entity.getId())).collect(Collectors.toList());
        // 项目wbs预算（不包含System记录）
        List<ProjectWbsBudgetDto> updateList = projectWbsBudgetDtos.stream().filter(entity -> !Objects.isNull(entity.getId())).collect(Collectors.toList());

        /* 更新 */
        if (CollectionUtils.isNotEmpty(updateList)) {
            List<List<ProjectWbsBudgetDto>> lists = ListUtils.splistList(updateList, 1000);
            for (List<ProjectWbsBudgetDto> list : lists) {
                projectWbsCostSummaryExtMapper.updateBatchProjectWbsBudget(list);
            }
        }

        /* 插入 */
        if (CollectionUtils.isNotEmpty(insertList)) {
            List<ProjectWbsBudgetDynamic> dynamicList = new ArrayList<>();
            for (ProjectWbsBudgetDto system : insertList) {
                if (null == system.getId()) {
                    projectWbsBudgetMapper.insertSelective(system);
                    if (StringUtils.isNotBlank(system.getDynamicFields()) && StringUtils.isNotBlank(system.getDynamicValues()) && StringUtils.isNotBlank(system.getDynamicWbsTemplateRuleIds())) {
                        String[] dynamicFieldArr = system.getDynamicFields().split(",");
                        String[] dynamicValueArr = system.getDynamicValues().split(",");
                        String[] dynamicWbsTemplateRuleIdsArr = system.getDynamicWbsTemplateRuleIds().split(",");
                        // 保存动态列
                        for (int i = 0; i < dynamicFieldArr.length; i++) {
                            ProjectWbsBudgetDynamic projectWbsBudgetDynamic = new ProjectWbsBudgetDynamic();
                            projectWbsBudgetDynamic.setProjectId(system.getProjectId());
                            projectWbsBudgetDynamic.setProjectWbsBudgetId(system.getId());
                            projectWbsBudgetDynamic.setFieldName(dynamicFieldArr[i]);
                            projectWbsBudgetDynamic.setWbsTemplateRuleId(Long.parseLong(dynamicWbsTemplateRuleIdsArr[i]));
                            projectWbsBudgetDynamic.setWbsTemplateRuleDetailCode(dynamicValueArr[i]);
                            projectWbsBudgetDynamic.setDeletedFlag(false);
                            projectWbsBudgetDynamic.setVersion(1L);
                            dynamicList.add(projectWbsBudgetDynamic);
                        }
                    }
                }
            }
            // 批量插入动态列
            if (CollectionUtils.isNotEmpty(dynamicList)) {
                projectWbsBudgetDynamicMapper.batchInsert(dynamicList);
            }
        }

        for (Long projectId : projectIds) {
            // 保存汇总数据（wbs、activity）
            saveWbsBudgetSummary(projectId);
        }
        logger.info("项目成本wbs统计定时任务，异步更新wbs预算正式表结束：{}", StringUtils.join(projectIds, ","));
    }

    /**
     * 保存汇总数据（wbs、activity）
     *
     * @param projectId
     * @return price 汇总预算
     */
    private void saveWbsBudgetSummary(Long projectId) {
        Project project = projectMapper.selectByPrimaryKey(projectId);
        if (!Boolean.TRUE.equals(project.getWbsEnabled()) || Objects.isNull(project.getWbsTemplateInfoId())) {
            throw new ApplicationBizException("项目类型对应wbs模板未启用或不存在");
        }
        ProjectWbsBudgetExample projectWbsBudgetExample = new ProjectWbsBudgetExample();
        projectWbsBudgetExample.createCriteria().andDeletedFlagEqualTo(false).andProjectIdEqualTo(projectId);
        List<ProjectWbsBudget> projectWbsBudgetList = projectWbsBudgetMapper.selectByExample(projectWbsBudgetExample);

        if (CollectionUtils.isEmpty(projectWbsBudgetList)) {
            throw new ApplicationBizException("项目wbs预算不存在");
        }
        // 删除历史记录
        projectWbsBudgetSummaryMapper.deleteByProjectId(projectId);

        List<Map<String, Object>> dataList = ProjectWbsBudgetDto.entity2MapBatch(projectWbsBudgetList);

        // wbs汇总：项目层
        ProjectWbsBudgetSummary topSummary = new ProjectWbsBudgetSummary();
        sumCost(topSummary, dataList);
        topSummary.setProjectId(project.getId());
        topSummary.setParentId(-1L);
        topSummary.setSummaryCode(project.getCode());
        topSummary.setActivityName(null);
        topSummary.setSummaryType(ProjectWbsBudgetSummarySummaryTypeEnums.WBS.getType());
        topSummary.setProjectDetailSelectFlag(false);
        topSummary.setDeletedFlag(false);
        topSummary.setVersion(1L);
        topSummary.setWbsLayer("项目");
        projectWbsBudgetSummaryMapper.insert(topSummary);

        // 保存wbs汇总
        saveWbsSummary(topSummary, dataList, project.getWbsTemplateInfoId());

        // 保存activity汇总
        saveActivitySummary(project, dataList);
        // 更新缓存
//        saveCache(projectId);
//        return topSummary.getPrice();
    }

    /**
     * 汇总金额
     *
     * @param entity
     * @param childs
     */
    private void sumCost(ProjectWbsBudgetSummary entity, List<Map<String, Object>> childs) {
        BigDecimal sumPrice = BigDecimal.ZERO;
        BigDecimal sumBaselineCost = BigDecimal.ZERO;
        BigDecimal sumDemandCost = BigDecimal.ZERO;
        BigDecimal sumOnTheWayCost = BigDecimal.ZERO;
        BigDecimal sumIncurredCost = BigDecimal.ZERO;
        BigDecimal sumRemainingCost = BigDecimal.ZERO;
        BigDecimal sumChangeAccumulateCost = BigDecimal.ZERO;
        for (int i = 0; i < childs.size(); i++) {
            Map child = childs.get(i);
            if (child.containsKey(WbsBudgetFieldConstant.PRICE)) {
                sumPrice = sumPrice.add(new BigDecimal(child.get(WbsBudgetFieldConstant.PRICE) + ""));
            }
            if (child.containsKey(WbsBudgetFieldConstant.BASELINE_COST)) {
                sumBaselineCost = sumBaselineCost.add(new BigDecimal(child.get(WbsBudgetFieldConstant.BASELINE_COST) + ""));
            }
            if (child.containsKey(WbsBudgetFieldConstant.DEMAND_COST)) {
                sumDemandCost = sumDemandCost.add(new BigDecimal(child.get(WbsBudgetFieldConstant.DEMAND_COST) + ""));
            }
            if (child.containsKey(WbsBudgetFieldConstant.ON_THE_WAY_COST)) {
                sumOnTheWayCost = sumOnTheWayCost.add(new BigDecimal(child.get(WbsBudgetFieldConstant.ON_THE_WAY_COST) + ""));
            }
            if (child.containsKey(WbsBudgetFieldConstant.INCURRED_COST)) {
                sumIncurredCost = sumIncurredCost.add(new BigDecimal(child.get(WbsBudgetFieldConstant.INCURRED_COST) + ""));
            }
            if (child.containsKey(WbsBudgetFieldConstant.REMAINING_COST)) {
                sumRemainingCost = sumRemainingCost.add(new BigDecimal(child.get(WbsBudgetFieldConstant.REMAINING_COST) + ""));
            }
            if (child.containsKey(WbsBudgetFieldConstant.CHANGE_ACCUMULATE_COST)) {
                sumChangeAccumulateCost = sumChangeAccumulateCost.add(new BigDecimal(child.get(WbsBudgetFieldConstant.CHANGE_ACCUMULATE_COST) + ""));
            }
        }
        entity.setPrice(sumPrice);
        entity.setBaselineCost(sumBaselineCost);
        entity.setDemandCost(sumDemandCost);
        entity.setOnTheWayCost(sumOnTheWayCost);
        entity.setIncurredCost(sumIncurredCost);
        entity.setRemainingCost(sumRemainingCost);
        entity.setChangeAccumulateCost(sumChangeAccumulateCost);
    }

    /**
     * 保存wbs汇总
     *
     * @param topSummary
     * @param dataList
     * @param wbsTemplateInfoId
     */
    private void saveWbsSummary(ProjectWbsBudgetSummary topSummary, List<Map<String, Object>> dataList, Long wbsTemplateInfoId) {
        // 动态列参数组装
        List<WbsTemplateRuleCache> dynamicFields = ProjectWbsBudgetUtils.getEligibilityWbsCache(wbsTemplateInfoId);
        saveWbsSummary(topSummary, 0, dynamicFields, dataList);
    }

    /**
     * 保存wbs汇总
     *
     * @param parentSummary 汇总
     * @param index         动态列序号
     * @param dynamicFields 动态列
     * @param dataList
     * @return
     */
    private void saveWbsSummary(ProjectWbsBudgetSummary parentSummary, int index, List<WbsTemplateRuleCache> dynamicFields, List<Map<String, Object>> dataList) {

        // 动态列
        WbsTemplateRuleCache dynamicField = dynamicFields.get(index);
        // Map<动态列field , List<Data>
        Map<String, List<Map<String, Object>>> groupMap = dataList.stream().collect(Collectors.groupingBy(a -> a.get(dynamicField.getKey()) + ""));
        // Map<动态列field , SummaryCode>
        Map<String, String> summaryCodeMap = new HashMap<>();
        // 批量新增汇总
        List<ProjectWbsBudgetSummary> batchInsertWbsBudgetSummary = new ArrayList<>();

        ProjectWbsBudget wbsBudget;
        for (String k : groupMap.keySet()) {
            List<Map<String, Object>> v = groupMap.get(k);

            ProjectWbsBudgetSummary entity = new ProjectWbsBudgetSummary();
            // 汇总
            if (!CollectionUtils.isEmpty(v)) {
                sumCost(entity, v);
            }
            entity.setProjectId(parentSummary.getProjectId());
            entity.setParentId(parentSummary.getId());
            entity.setSummaryCode(parentSummary.getSummaryCode() + "-" + k);
            entity.setActivityName(null);
            entity.setSummaryType(ProjectWbsBudgetSummarySummaryTypeEnums.WBS.getType());
            entity.setDeletedFlag(false);
            entity.setVersion(1L);
            entity.setWbsLayer(dynamicField.getLable());
            entity.setWbsTemplateRuleId(dynamicField.getId());

            summaryCodeMap.put(k, entity.getSummaryCode());

            // 最后一层动态列
            if (index == dynamicFields.size() - 1) {
                entity.setProjectDetailSelectFlag(true);
                if (!CollectionUtils.isEmpty(v)) {
                    // 设置描述（wbs描述去重，2000长度拦截）
                    Set<String> desSet = new HashSet<>();
                    StringBuilder desString = new StringBuilder();
                    for (Map map : v) {
                        String des = MapUtils.getString(map, WbsBudgetFieldConstant.DESCRIPTION);
                        if (StringUtils.isNotBlank(des) && !desSet.contains(des)) {
                            desSet.add(des);
                            if (desString.length() == 0) {
                                desString.append(des);
                            } else {
                                desString.append("," + des);
                            }
                        }
                    }
                    entity.setDescription(desString.length() > 2000 ? desString.substring(0, 1999) : desString.toString());
                }
                batchInsertWbsBudgetSummary.add(entity);
            } else {
                entity.setProjectDetailSelectFlag(false);
                batchInsertWbsBudgetSummary.add(entity);
            }
        }

        // 批量插入汇总
        if (CollectionUtils.isNotEmpty(batchInsertWbsBudgetSummary)) {
            projectWbsBudgetSummaryMapper.batchInsert(batchInsertWbsBudgetSummary);
        }

        // 批量更新预算
        List<ProjectWbsBudget> batchUpdateWbsBudget = new ArrayList<>();

        for (String k : groupMap.keySet()) {
            List<Map<String, Object>> v = groupMap.get(k);
            String summaryCode = summaryCodeMap.get(k);
            /* 最后一层动态列 批量更新预算 */
            if (index == dynamicFields.size() - 1) {
                ProjectWbsBudgetSummary summary = batchInsertWbsBudgetSummary.stream().filter(a -> a.getSummaryCode().equals(summaryCode)).findFirst().orElse(null);
                if (null != summary) {
                    // 重新赋值汇总的parentId
                    for (Map<String, Object> map : v) {
                        wbsBudget = new ProjectWbsBudgetDto();
                        wbsBudget.setId(MapUtils.getLong(map, "id"));
                        wbsBudget.setParentWbsId(summary.getId());
                        batchUpdateWbsBudget.add(wbsBudget);
                    }
                }
            }
            /* 非最后一层动态列 递归 */
            else {
                ProjectWbsBudgetSummary summary = batchInsertWbsBudgetSummary.stream().filter(a -> a.getSummaryCode().equals(summaryCode)).findFirst().orElse(null);
                if (null != summary) {
                    // 递归
                    saveWbsSummary(summary, index + 1, dynamicFields, v);
                }
            }
        }

        // 批量更新预算
        if (CollectionUtils.isNotEmpty(batchUpdateWbsBudget)) {
            // 批量更新parentWbsId
            projectWbsBudgetMapper.batchUpdate(batchUpdateWbsBudget);
        }
    }

    /**
     * 保存activity汇总
     *
     * @param
     */
    private void saveActivitySummary(Project project, List<Map<String, Object>> dataList) {
        // 获取data中唯一的orderNo
        List<String> groupOrderNo = dataList.stream()
                .map(a -> a.get(WbsBudgetFieldConstant.ACTIVITY_ORDER_NO) + "")
                .distinct()
                .collect(Collectors.toList());

        // 上级order集合 <orderNo,下级>
        Map<String, HashSet<String>> parentOrderMap = new HashMap<>();

        // 第一层orderNo
        HashSet<String> firstOrderNoSet = new HashSet<>();

        for (String orderNo : groupOrderNo) {
            // 组装父层orderNo
            setParentOrderNo(orderNo, parentOrderMap, firstOrderNoSet);
        }
        // Map orderNo 分组 <orderNo, data>
        Map<String, List<Map<String, Object>>> groupMap = dataList.stream()
                .collect(Collectors.groupingBy(a -> MapUtils.getString(a, WbsBudgetFieldConstant.ACTIVITY_ORDER_NO)));

        Unit unit = CacheDataUtils.findUnitById(project.getUnitId());
        Long parentUnitId = unit != null ? unit.getParentId() : null;
        // parentOrderMap获取活动事项列表 <orderNo, ProjectActivity>
        Map<String, ProjectActivity> parentActivity = getOrderMapActivity(parentUnitId, parentOrderMap);

        // 批量更新预算
        List<ProjectWbsBudget> batchUpdateWbsBudget = new ArrayList<>();
        for (String firstOrderNo : firstOrderNoSet) {
            saveActivitySummary(firstOrderNo, project.getCode(), project.getId(), -1L, parentActivity, parentOrderMap, groupMap, batchUpdateWbsBudget);
        }
        // 批量更新预算
        if (CollectionUtils.isNotEmpty(batchUpdateWbsBudget)) {
            // 批量更新parentActivityId
            projectWbsBudgetMapper.batchUpdate(batchUpdateWbsBudget);
        }
    }

    /**
     * 保存activity汇总
     *
     * @param
     */
    private ProjectWbsBudgetSummary saveActivitySummary(String parentOrderNo,
                                                        String projectCode,
                                                        Long projectId,
                                                        Long parentId,
                                                        Map<String, ProjectActivity> parentActivity,
                                                        Map<String, HashSet<String>> parentOrderMap,
                                                        Map<String, List<Map<String, Object>>> groupMap,
                                                        List<ProjectWbsBudget> batchUpdateWbsBudget) {

        ProjectWbsBudgetSummary entity = new ProjectWbsBudgetSummary();
        entity.setSummaryType(ProjectWbsBudgetSummarySummaryTypeEnums.ACTIVITY.getType());
        entity.setDeletedFlag(false);
        entity.setVersion(1L);
        entity.setProjectId(projectId);
        entity.setParentId(parentId);
        entity.setProjectDetailSelectFlag(false);

        // groupMap能匹配到就是最底层
        if (groupMap.containsKey(parentOrderNo)) {
            List<Map<String, Object>> childs = groupMap.get(parentOrderNo);
            if (CollectionUtils.isEmpty(childs)) {
                return entity;
            }
            // 汇总金额
            sumCost(entity, childs);
            entity.setActivityName(MapUtils.getString(childs.get(0), WbsBudgetFieldConstant.ACTIVITY_NAME));
            entity.setActivityType(MapUtils.getString(childs.get(0), WbsBudgetFieldConstant.ACTIVITY_TYPE));
            entity.setActivityCode(MapUtils.getString(childs.get(0), WbsBudgetFieldConstant.ACTIVITY_CODE));
            entity.setActivityOrderNo(MapUtils.getString(childs.get(0), WbsBudgetFieldConstant.ACTIVITY_ORDER_NO));
            entity.setProjectDetailSelectFlag(true);
            entity.setSummaryCode(projectCode + "." + MapUtils.getString(childs.get(0), WbsBudgetFieldConstant.ACTIVITY_CODE));
            projectWbsBudgetSummaryMapper.insert(entity);
            ProjectWbsBudgetDto wbsBudget;
            // 重新赋值汇总的parentId
            for (Map<String, Object> map : childs) {
                wbsBudget = new ProjectWbsBudgetDto();
                wbsBudget.setId(MapUtils.getLong(map, "id"));
                wbsBudget.setParentActivityId(entity.getId());
                // 添加批量更新
                batchUpdateWbsBudget.add(wbsBudget);
            }
        } else {
            ProjectActivity currentActivity = parentActivity.get(parentOrderNo);
            entity.setActivityName(currentActivity.getName());
            entity.setActivityType(currentActivity.getType());
            entity.setActivityCode(currentActivity.getCode());
            entity.setActivityOrderNo(currentActivity.getOrderNo());
            entity.setSummaryCode(projectCode + "." + currentActivity.getCode());
            // 初始化一个0金额
            sumCost(entity, new ArrayList<>());
            projectWbsBudgetSummaryMapper.insert(entity);

            HashSet<String> childOrder = parentOrderMap.get(parentOrderNo);
            List<ProjectWbsBudgetSummary> childs = new ArrayList<>();
            for (String order : childOrder) {
                ProjectWbsBudgetSummary child = saveActivitySummary(order, entity.getSummaryCode(), projectId, entity.getId(), parentActivity, parentOrderMap, groupMap, batchUpdateWbsBudget);
                childs.add(child);
            }
            if (!CollectionUtils.isEmpty(childs)) {
                List<Map<String, Object>> childMapList = ProjectWbsBudgetSummaryDto.entity2MapBatch(childs);
                sumCost(entity, childMapList);
                projectWbsBudgetSummaryMapper.updateByPrimaryKey(entity);
            }
        }
        return entity;
    }

    /**
     * 拼接父层orderNo
     *
     * @param orderNo
     * @param parentOrderMap
     * @param firstOrderNoSet
     */
    private void setParentOrderNo(String orderNo,
                                  Map<String, HashSet<String>> parentOrderMap,
                                  Set<String> firstOrderNoSet) {
        if (orderNo.contains(".")) {
            // 获取父orderNo，将当前orderNo放到 parentOrderMap里
            if (parentOrderMap.containsKey(StringUtils.substringBeforeLast(orderNo, "."))) {
                parentOrderMap.get(StringUtils.substringBeforeLast(orderNo, ".")).add(orderNo);
            } else {
                HashSet<String> set = new HashSet();
                set.add(orderNo);
                parentOrderMap.put(StringUtils.substringBeforeLast(orderNo, "."), set);
            }
            setParentOrderNo(StringUtils.substringBeforeLast(orderNo, "."), parentOrderMap, firstOrderNoSet);
        } else {
            // 保存第一层orderNo
            firstOrderNoSet.add(orderNo);
        }
    }

    /**
     * parentOrderMap获取活动事项列表
     *
     * @param parentOrderMap
     * @return <orderNo, ProjectActivity>
     */
    private Map<String, ProjectActivity> getOrderMapActivity(Long unitId, Map<String, HashSet<String>> parentOrderMap) {
        if (parentOrderMap.isEmpty()) {
            return new HashMap<>();
        }
        List<String> orderNoList = new ArrayList<>(parentOrderMap.keySet());
        ProjectActivityExample example = new ProjectActivityExample();
        // SystemContext.getUnitId()
        example.createCriteria().andUnitIdEqualTo(unitId != null ? unitId : SystemContext.getUnitId()).andOrderNoIn(orderNoList);
        List<ProjectActivity> list = projectActivityMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            return new HashMap<>();
        }
        return list.stream().collect(Collectors.toMap(ProjectActivity::getOrderNo, Function.identity(), (a1, a2) -> a1));
    }


}
