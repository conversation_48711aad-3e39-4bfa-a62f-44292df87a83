package com.midea.pam.statistics.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.entity.OperatingUnit;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.ctc.dto.InvoiceApplyDetailSplitDTO;
import com.midea.pam.common.ctc.dto.InvoiceReceivableDto;
import com.midea.pam.common.ctc.dto.InvoiceRelDto;
import com.midea.pam.common.ctc.entity.InvoiceReceivable;
import com.midea.pam.common.ctc.entity.ResendExecute;
import com.midea.pam.common.ctc.entity.WriteOffInvoiceRel;
import com.midea.pam.common.enums.CommonStatus;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.statistics.mapper.InvoiceReceivableStatisticsExtMapper;
import com.midea.pam.statistics.service.InvoiceReceivableStatisticsService;
import com.midea.pam.support.utils.BeanConverter;
import com.midea.pam.system.SystemContext;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @program: common-module
 * @description: 应收发票service
 * @author:zhongpeng
 * @create:2020-03-19 10:50
 **/
public class InvoiceReceivableStatisticsServiceImpl implements InvoiceReceivableStatisticsService {
    @Resource
    private InvoiceReceivableStatisticsExtMapper invoiceReceivableStatisticsExtMapper;

    /**
     * 一个方法提取两种dto,并将dto根据相应的code进行分组储存
     *
     * @param dtoList
     * @param str     注意：InvoiceCode:提取InvoiceCode的dto，其它值则为提取OldInvoiceCode的dto.
     * @return 返回Map<String, List < InvoiceReceivable>>
     */
    private Map<String, List<InvoiceReceivable>> getInvoiceReceivableMap(List<InvoiceReceivableDto> dtoList, String str) {
        InvoiceReceivable query = new InvoiceReceivable();
        List<String> codeList = new ArrayList<>();
        //差异代码（这行代码不同，其它行代码相同）
        List<InvoiceReceivable> dtolist = null;
        if ("InvoiceCode".equals(str)) {
            dtoList.stream().forEach(dto -> codeList.add(dto.getInvoiceCode()));
            dtolist = invoiceReceivableStatisticsExtMapper.selectByQueryInCode(codeList, 1);
        } else {
            dtoList.stream().forEach(dto -> codeList.add(dto.getOldInvoiceCode()));
            dtolist = invoiceReceivableStatisticsExtMapper.selectByQueryInCode(codeList, 0);
        }

        /**将上面的list进行分组储存。*/
        Map<String, List<InvoiceReceivable>> map = new HashMap<>();
        //差异代码
        if ("InvoiceCode".equals(str)) {
            dtolist.stream().forEach(dto -> {
                //差异代码（这行代码不同，其它行代码相同）
                String code = dto.getInvoiceCode();
                if (map.containsKey(code)) {
                    map.get(code).add(dto);
                } else {
                    List<InvoiceReceivable> dtos = new ArrayList<>();
                    dtos.add(dto);
                    map.put(code, dtos);
                }
            });
        } else {
            dtolist.stream().forEach(dto -> {
                //差异代码（这行代码不同，其它行代码相同）
                String code = dto.getOldInvoiceCode();
                if (map.containsKey(code)) {
                    map.get(code).add(dto);
                } else {
                    List<InvoiceReceivable> dtos = new ArrayList<>();
                    dtos.add(dto);
                    map.put(code, dtos);
                }
            });
        }
        return map;
    }

    @Override
    public InvoiceReceivableDto queryExtDetail(Long id,Long unitId) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        if(Objects.isNull(unitId)){
            param.put("unitId", SystemContext.getUnitId());
        }else{
            param.put("unitId", unitId);
        }
        List<InvoiceReceivableDto> invoiceReceivableDtoList = invoiceReceivableStatisticsExtMapper.list(param);
        if (ListUtils.isEmpty(invoiceReceivableDtoList)) return invoiceReceivableDtoList.get(0);
        InvoiceReceivableDto dto = invoiceReceivableDtoList.get(0);
        // 查询开票申请行拆分金额明细
        List<InvoiceApplyDetailSplitDTO> invoiceApplyDetailSplitList = invoiceReceivableStatisticsExtMapper.selectApplySplit(dto);
        invoiceApplyDetailSplitList.forEach(s -> s.setQuantityStr(BigDecimalUtils.stripTrailingZerosString(s.getQuantity())));
        dto.setApplyDetailSplitList(invoiceApplyDetailSplitList);
        OperatingUnit ou = CacheDataUtils.findOuById(dto.getOuId());
        if (ou != null) dto.setOuName(ou.getOperatingUnitName());
        if (dto.getQuantity() != null) {
            BigDecimal bigDecimal = new BigDecimal(dto.getQuantity() + "");
            dto.setQuantityStr(bigDecimal.stripTrailingZeros().toPlainString());
        }
        //冲销明细 / 原发票信息
        InvoiceReceivable query = new InvoiceReceivable();
        if (dto.getTaxIncludedPrice().floatValue() > 0) {
            query.setOldInvoiceCode(dto.getInvoiceCode());
            List<InvoiceReceivable> invoiceReceivableList = invoiceReceivableStatisticsExtMapper.selectByQuery(query);
            if (ListUtils.isNotEmpty(invoiceReceivableList)) {
                List<InvoiceReceivableDto> reverseDtoList = BeanConverter.copy(invoiceReceivableList, InvoiceReceivableDto.class);
                if (ListUtils.isNotEmpty(reverseDtoList)) {
                    for (InvoiceReceivableDto invoiceReceivableDto : reverseDtoList) {
                        if (invoiceReceivableDto.getCreateBy() != null) {
                            UserInfo userInfo = CacheDataUtils.findUserById(invoiceReceivableDto.getCreateBy());
                            if (userInfo != null) {
                                invoiceReceivableDto.setCreateUserName(userInfo.getName());
                            }
                        }
                    }
                }
                dto.setReverseDtoList(reverseDtoList);
            }
        } else {
            query.setInvoiceCode(dto.getOldInvoiceCode());
            List<InvoiceReceivable> oldInvoiceReceivableList = invoiceReceivableStatisticsExtMapper.selectByQuery(query);
            if (ListUtils.isNotEmpty(oldInvoiceReceivableList)) {
                InvoiceReceivableDto oldInvoiceReceivable = BeanConverter.copy(oldInvoiceReceivableList.get(0), InvoiceReceivableDto.class);
                dto.setOldInvoiceReceivable(oldInvoiceReceivable);
            }
        }
        //----------------------------------------------

        //====================新增关联核销信息==========================
        //2）	正数发票与负数发票的核销
        Boolean flag = null;
        //注意：不会有金额==0情况；
        List<InvoiceRelDto> woirList = null;
        if (dto.getTaxIncludedPrice().floatValue() > 0) {
            //正数查负数的核销
            woirList = invoiceReceivableStatisticsExtMapper.woirList01(dto.getId());
        }
        if (dto.getTaxIncludedPrice().floatValue() < 0) {
            //负数查正数的核销
            woirList = invoiceReceivableStatisticsExtMapper.woirList02(dto.getId());
        }
        //3）	回款与发票的核销
        List<InvoiceRelDto> rcirList = invoiceReceivableStatisticsExtMapper.rcirList(dto.getId());
        //上面两个list合并,同时把单据类型直接设置
        List<InvoiceRelDto> list = new ArrayList<>();
        if (woirList != null) {
            woirList.stream().forEach(element -> {
                element.setType("发票");
                list.add(element);
            });
        }
        rcirList.stream().forEach(element -> {
            element.setType("回款");
            list.add(element);
        });
        dto.setInvoiceRelDtoList(list);
        //----------------------------------------------

        return dto;
    }


    @Override
    public PageInfo<InvoiceReceivableDto> queryEXt(Map<String, Object> map) {
        Integer pageNum = (Integer) map.get("pageNum");
        Integer pageSize = (Integer) map.get("pageSize");
        PageHelper.startPage(pageNum, pageSize);
        List<InvoiceReceivableDto> invoiceReceivableDtos = invoiceReceivableStatisticsExtMapper.list(map);
        PageInfo<InvoiceReceivableDto> pageInfo = BeanConverter.convertPage(invoiceReceivableDtos, InvoiceReceivableDto.class);
        return pageInfo;
    }


    @Override
    public PageInfo<InvoiceReceivableDto> query(Map<String, Object> map) {
        InvoiceReceivable query = new InvoiceReceivable();
        Integer pageNum = (Integer) map.get("pageNum");
        Integer pageSize = (Integer) map.get("pageSize");
        PageHelper.startPage(pageNum, pageSize);
        List<InvoiceReceivableDto> invoiceReceivableDtos = invoiceReceivableStatisticsExtMapper.list(map);
        PageInfo<InvoiceReceivableDto> pageInfo = BeanConverter.convertPage(invoiceReceivableDtos, InvoiceReceivableDto.class);
        if (ListUtils.isEmpty(pageInfo.getList())) return pageInfo;
        OperatingUnit ou;
        /**
         for (InvoiceReceivableDto dto : pageInfo.getList()) {
         ou = CacheDataUtils.findOuById(dto.getOuId());
         if (ou != null) dto.setOuName(ou.getOperatingUnitName());
         if (dto.getQuantity() != null){
         BigDecimal bigDecimal = new BigDecimal(dto.getQuantity()+"");
         dto.setQuantityStr(bigDecimal.stripTrailingZeros().toPlainString());
         }
         //冲销明细 / 原发票信息
         if (dto.getTaxIncludedPrice().floatValue()>0){
         query.setOldInvoiceCode(dto.getInvoiceCode());
         List<InvoiceReceivable> invoiceReceivableList = invoiceReceivableStatisticsExtMapper.selectByQuery(query);
         if (ListUtils.isNotEmpty(invoiceReceivableList)){
         List<InvoiceReceivableDto> reverseDtoList = BeanConverter.copy(invoiceReceivableList,InvoiceReceivableDto.class);
         if (ListUtils.isNotEmpty(reverseDtoList)){
         for (InvoiceReceivableDto invoiceReceivableDto : reverseDtoList) {
         if (invoiceReceivableDto.getCreateBy()!=null){
         UserInfo userInfo = CacheDataUtils.findUserById(invoiceReceivableDto.getCreateBy());
         if(userInfo!=null) {
         invoiceReceivableDto.setCreateUserName(userInfo.getName());
         }
         }
         }
         }
         dto.setReverseDtoList(reverseDtoList);
         }
         }else {
         query.setInvoiceCode(dto.getOldInvoiceCode());
         List<InvoiceReceivable> oldInvoiceReceivableList = invoiceReceivableStatisticsExtMapper.selectByQuery(query);
         if (ListUtils.isNotEmpty(oldInvoiceReceivableList)){
         InvoiceReceivableDto oldInvoiceReceivable = BeanConverter.copy(oldInvoiceReceivableList.get(0),InvoiceReceivableDto.class);
         dto.setOldInvoiceReceivable(oldInvoiceReceivable);
         }
         }
         }
         */
        //此处只是为了将系统型能提升。
        //如果有关应收发票的功能有数据异常，可以将下面两行代码和for循环整体删除并还原上面注释
        //获取新的 ，注意最后一个参数。它是为了区别不同code的dto。
        Map<String, List<InvoiceReceivable>> invoiceReceivableMap = getInvoiceReceivableMap(pageInfo.getList(), "InvoiceCode");
        //获取旧的
        Map<String, List<InvoiceReceivable>> oldInvoiceReceivableMap = getInvoiceReceivableMap(pageInfo.getList(), "OldInvoiceCode");
        for (InvoiceReceivableDto dto : pageInfo.getList()) {
            ou = CacheDataUtils.findOuById(dto.getOuId());
            if (ou != null) dto.setOuName(ou.getOperatingUnitName());
            if (dto.getQuantity() != null) {
                BigDecimal bigDecimal = new BigDecimal(dto.getQuantity() + "");
                dto.setQuantityStr(bigDecimal.stripTrailingZeros().toPlainString());
            }
            //冲销明细 / 原发票信息
            if (dto.getTaxIncludedPrice().floatValue() > 0) {
                query.setOldInvoiceCode(dto.getInvoiceCode());
                //新的
                List<InvoiceReceivable> invoiceReceivableList = invoiceReceivableMap.get(dto.getInvoiceCode());
                if (ListUtils.isNotEmpty(invoiceReceivableList)) {
                    List<InvoiceReceivableDto> reverseDtoList = BeanConverter.copy(invoiceReceivableList, InvoiceReceivableDto.class);
                    if (ListUtils.isNotEmpty(reverseDtoList)) {
                        for (InvoiceReceivableDto invoiceReceivableDto : reverseDtoList) {
                            if (invoiceReceivableDto.getCreateBy() != null) {
                                UserInfo userInfo = CacheDataUtils.findUserById(invoiceReceivableDto.getCreateBy());
                                if (userInfo != null) {
                                    invoiceReceivableDto.setCreateUserName(userInfo.getName());
                                }
                            }
                        }
                    }
                    dto.setReverseDtoList(reverseDtoList);
                }
            } else {
                query.setInvoiceCode(dto.getOldInvoiceCode());

                //旧的
                List<InvoiceReceivable> oldInvoiceReceivableList = oldInvoiceReceivableMap.get(dto.getOldInvoiceCode());
                if (ListUtils.isNotEmpty(oldInvoiceReceivableList)) {
                    InvoiceReceivableDto oldInvoiceReceivable = BeanConverter.copy(oldInvoiceReceivableList.get(0), InvoiceReceivableDto.class);
                    dto.setOldInvoiceReceivable(oldInvoiceReceivable);
                }
            }
        }
        return pageInfo;
    }


    @Override
    public Map<String, Object> listExport(Map<String, Object> map) {
        Map<String, Object> responseMap = new HashMap<>();
        List<InvoiceReceivableDto> invoiceReceivableDtos = invoiceReceivableStatisticsExtMapper.export(map);
        if (ListUtils.isNotEmpty(invoiceReceivableDtos)) {
            OperatingUnit ou;
            for (InvoiceReceivableDto invoiceReceivableDto : invoiceReceivableDtos) {
                ou = null;
                ou = CacheDataUtils.findOuById(invoiceReceivableDto.getOuId());
                if (ou != null) invoiceReceivableDto.setOuName(ou.getOperatingUnitName());
            }
            responseMap.put("invoiceReceivableDtos", invoiceReceivableDtos);
        }
        return responseMap;
    }

    @Override
    public InvoiceReceivableDto findById(Long id) {
        Map<String, Object> param = new HashMap<>();
        InvoiceReceivable query = new InvoiceReceivable();
        param.put("id", id);
        param.put("unitId", SystemContext.getUnitId());
        List<InvoiceReceivableDto> invoiceReceivableDtoList = invoiceReceivableStatisticsExtMapper.list(param);
        if (ListUtils.isEmpty(invoiceReceivableDtoList)) return invoiceReceivableDtoList.get(0);
        InvoiceReceivableDto receivableDto = invoiceReceivableDtoList.get(0);
        OperatingUnit ou = CacheDataUtils.findOuById(receivableDto.getOuId());
        if (ou != null) receivableDto.setOuName(ou.getOperatingUnitName());
        //冲销明细 / 原发票信息
        if (receivableDto.getTaxIncludedPrice().floatValue() > 0) {
            query.setOldInvoiceCode(receivableDto.getInvoiceCode());
            List<InvoiceReceivable> invoiceReceivableList = invoiceReceivableStatisticsExtMapper.selectByQuery(query);
            if (ListUtils.isNotEmpty(invoiceReceivableList)) {
                List<InvoiceReceivableDto> reverseDtoList = BeanConverter.copy(invoiceReceivableList, InvoiceReceivableDto.class);
                if (ListUtils.isNotEmpty(reverseDtoList)) {
                    for (InvoiceReceivableDto invoiceReceivableDto : reverseDtoList) {
                        if (invoiceReceivableDto.getCreateBy() != null) {
                            invoiceReceivableDto.setCreateUserName(CacheDataUtils.findUserById(invoiceReceivableDto.getCreateBy()).getName());
                        }
                    }
                }
                receivableDto.setReverseDtoList(reverseDtoList);
            }
        } else {
            query.setInvoiceCode(receivableDto.getOldInvoiceCode());
            List<InvoiceReceivable> oldInvoiceReceivableList = invoiceReceivableStatisticsExtMapper.selectByQuery(query);
            if (ListUtils.isNotEmpty(oldInvoiceReceivableList)) {
                InvoiceReceivableDto oldInvoiceReceivable = BeanConverter.copy(oldInvoiceReceivableList.get(0), InvoiceReceivableDto.class);
                receivableDto.setOldInvoiceReceivable(oldInvoiceReceivable);
            }
        }
        return receivableDto;
    }

    @Override
    public List<InvoiceReceivableDto> select(List<Long> ouIds) {
        Map<String, Object> map = new HashMap<>();
        map.put("resouce", "resouce");
        map.put("ouId", ouIds);
        map.put("unitId", SystemContext.getUnitId());
        return invoiceReceivableStatisticsExtMapper.list(map);
    }

    @Override
    public List<String> getInvoiceTypeList() {
        Map<String, Object> map = new HashMap<>();
        map.put("ouId", SystemContext.getOus());
        return invoiceReceivableStatisticsExtMapper.getInvoiceTypeList(map);
    }

    @Override
    public Boolean resend(Long originalInvoiceId) {
        List<InvoiceRelDto> invoiceRelDtos = invoiceReceivableStatisticsExtMapper.woirList01(originalInvoiceId);
        Assert.notNull(invoiceRelDtos, "关联核销信息不存在!");
        InvoiceRelDto invoiceRelDto = invoiceRelDtos.get(0);
        //仅当核销状态类型不为已核销且ERP同步状态不为推送成功，方可重新推送，单据类型前端控制
        if (Objects.nonNull(invoiceRelDto) && !Objects.equals("3", invoiceRelDto.getErpStatus())) {
            ResendExecute resendExecute = invoiceReceivableStatisticsExtMapper.getResendExecute(invoiceRelDto.getWoirId().toString());
            Assert.notNull(resendExecute, "接口发送记录不存在!");
            resendExecute.setStatus(CommonStatus.TODO.getCode());
            resendExecute.setResponCode(null);
            resendExecute.setResponMsg(null);
            resendExecute.setBatch(Boolean.FALSE);
            resendExecute.setDeletedFlag(Boolean.FALSE);
            invoiceReceivableStatisticsExtMapper.updateResendExecute(resendExecute);
            //更新write_off_invoice_rel
            WriteOffInvoiceRel writeOffInvoiceRel = new WriteOffInvoiceRel();
            writeOffInvoiceRel.setId(invoiceRelDto.getWoirId());
            writeOffInvoiceRel.setErpStatus(2);
            writeOffInvoiceRel.setErpMessage(null);
            invoiceReceivableStatisticsExtMapper.updateWriteOffInvoiceRel(writeOffInvoiceRel);
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

}
