package com.midea.pam.statistics.service.impl;

import com.midea.pam.common.enums.ReportExecuteStatus;
import com.midea.pam.common.statistics.dto.ExportResponse;
import com.midea.pam.common.statistics.entity.ReportExecuteRecord;
import com.midea.pam.common.statistics.entity.ReportInterfaceParam;
import com.midea.pam.common.statistics.entity.ReportInterfaceParamExample;
import com.midea.pam.common.util.ApplicationContextProvider;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.statistics.mapper.ReportExecuteRecordMapper;
import com.midea.pam.statistics.service.ActuatorService;
import com.midea.pam.statistics.service.CommonReportService;
import com.midea.pam.statistics.service.ReportInterfaceParamService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

public class ActuatorServiceImpl implements ActuatorService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ActuatorServiceImpl.class);

    @Resource
    private ReportInterfaceParamService reportInterfaceParamService;

    @Resource
    private ReportExecuteRecordMapper reportExecuteRecordMapper;

    @Async
    public void asyncExecute(CommonReportService commonReportService, ReportExecuteRecord record) {
        long start = System.currentTimeMillis();
        try {
            final Boolean response = commonReportService.execute(record);
            record.setStatus((response != null && response) ? ReportExecuteStatus.DONE.getCode() : ReportExecuteStatus.ERROR.getCode());
            record.setEndTime(new Date());
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            record.setStatus(ReportExecuteStatus.ERROR.getCode());
            String errorMsg = e.toString();
            if(StringUtils.isNotEmpty(errorMsg) && errorMsg.length() > 200){
                errorMsg = errorMsg.substring(0, 200);
            }
            record.setErrorMsg(errorMsg);
            LOGGER.error("报表编号为:{}的单据发送失败，单据编号为:{}", record.getReportCode(), record.getCode(), e);
        } finally {
            long end = System.currentTimeMillis();
            record.setCostTime((end - start)/1000);
            reportExecuteRecordMapper.updateByPrimaryKeySelective(record);
        }
    }

    @Override
    public ExportResponse export(ReportExecuteRecord record) {
        CommonReportService commonReportService = null;
        final ReportInterfaceParamExample paramExample = new ReportInterfaceParamExample();
        final ReportInterfaceParamExample.Criteria criteria = paramExample.createCriteria();
        criteria.andDeletedFlagEqualTo(false).andBusinessTypeEqualTo(record.getReportCode());

        final List<ReportInterfaceParam> params = reportInterfaceParamService.selectByExample(paramExample);
        if (ListUtils.isEmpty(params)) {
            LOGGER.error("报表编号为:{}的接口，参数未配置,单据id为:{}", record.getReportCode(), record.getCode());
            record.setStatus(ReportExecuteStatus.ERROR.getCode());
            return null;
        }
        commonReportService = (CommonReportService) ApplicationContextProvider.getBean(params.get(0).getComponent());
        if (null == commonReportService) {
            LOGGER.error("报表编号为:{}的接口，beanId={}不存在,单据id为:{}", record.getReportCode(), record.getId(), record.getCode());
            record.setStatus(ReportExecuteStatus.ERROR.getCode());
            return null;
        }
        ExportResponse response = commonReportService.export(record);
        response.setFileName(params.get(0).getBusinessName() + "_" + record.getCode() + ".xls");
        return response;
    }

}
