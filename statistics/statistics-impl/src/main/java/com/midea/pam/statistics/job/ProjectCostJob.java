package com.midea.pam.statistics.job;

import com.midea.pam.common.enums.ProjectCostExecuteStatus;
import com.midea.pam.common.statistics.entity.ProjectCostExecuteRecord;
import com.midea.pam.statistics.project.service.ProjectCostExecuteRecordService;
import com.midea.pam.statistics.project.service.ProjectCostService;
import com.midea.pam.statistics.project.service.ProjectCostSummaryRecordService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/3/9
 * @description
 */
//@JobHandler("projectCostJob")
@Component
public class ProjectCostJob {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private ProjectCostService projectCostService;

    @Resource
    private ProjectCostExecuteRecordService projectCostExecuteRecordService;

    @Resource
    private ProjectCostSummaryRecordService projectCostSummaryRecordService;

    @XxlJob("projectCostJob")
    public ReturnT<String> execute(String s) throws Exception {
        logger.info("---------------- 项目成本统计开始 --------------");

        long startM = System.currentTimeMillis();
        ProjectCostExecuteRecord projectCostExecuteRecord = projectCostService.generateExecuteRecord();
        Long executeId = projectCostExecuteRecord.getId();

        try {
            projectCostService.clearHistory(28);
            long endClear = System.currentTimeMillis();
            long costClear = endClear - startM;
            logger.info("项目成本清除历史数据耗时:{} ms", costClear);

            int num = projectCostService.generateProjectCost(executeId);

            long endM = System.currentTimeMillis();
            long costM = endM - startM;
            projectCostExecuteRecord.setNum(num);
            projectCostExecuteRecord.setEndTime(new Date());
            projectCostExecuteRecord.setCostTime(costM);
            projectCostExecuteRecord.setStatus(ProjectCostExecuteStatus.SUCCESS.getCode());

            projectCostExecuteRecordService.updateByPrimaryKeySelective(projectCostExecuteRecord);
        } catch (Exception e) {
            long endM = System.currentTimeMillis();
            long costM = endM - startM;
            projectCostExecuteRecord.setEndTime(new Date());
            projectCostExecuteRecord.setCostTime(costM);
            projectCostExecuteRecord.setStatus(ProjectCostExecuteStatus.FAILURE.getCode());
            projectCostExecuteRecordService.updateByPrimaryKeySelective(projectCostExecuteRecord);
            projectCostSummaryRecordService.updateStatusByExecuteId(executeId,1);
            logger.error("项目成本统计执行失败：{}", executeId, e);
            throw e;
        }
        projectCostSummaryRecordService.updateStatusByExecuteId(executeId,0);
        logger.info("---------------- 项目成本统计结束 --------------");
        return ReturnT.SUCCESS;
    }

}
