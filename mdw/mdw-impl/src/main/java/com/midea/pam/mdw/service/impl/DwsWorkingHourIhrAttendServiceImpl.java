package com.midea.pam.mdw.service.impl;

import com.midea.pam.common.base.IdEntity;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.entity.OrgLaborCostTypeSet;
import com.midea.pam.common.basedata.entity.Unit;
import com.midea.pam.common.mdw.dto.DwsUserOrgUnitNameDto;
import com.midea.pam.common.mdw.dto.DwsWorkingHourIhrAttendDto;
import com.midea.pam.common.mdw.entity.DwdLtcOrganization;
import com.midea.pam.common.mdw.entity.DwdLtcUserInfo;
import com.midea.pam.common.mdw.entity.DwdWorkingHourExample;
import com.midea.pam.common.mdw.entity.DwsWorkingHourIhrAttend;
import com.midea.pam.common.mdw.entity.DwsWorkingHourIhrAttendExample;
import com.midea.pam.common.statistics.entity.WorkingHourIhrAttend;
import com.midea.pam.common.util.DateUtil;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.mdw.mapper.DwdLtcUserInfoMapper;
import com.midea.pam.mdw.mapper.DwdWorkingHourMapper;
import com.midea.pam.mdw.mapper.DwsWorkingHourIhrAttendExtMapper;
import com.midea.pam.mdw.mapper.DwsWorkingHourIhrAttendMapper;
import com.midea.pam.mdw.service.BaseDataExtService;
import com.midea.pam.mdw.service.DwsWorkingHourIhrAttendService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.midea.pam.common.util.DateUtil.DATE_INTEGER_PATTERN;

/**
 * @PackageClassName: com.midea.pam.mdw.service.impl.DwsWorkingHourIhrAttendServiceImpl
 * @Description: 项目工时与考勤工时汇总业务
 * @Author: JerryH
 * @Date: 2023-03-21, 0021 下午 03:30
 */
public class DwsWorkingHourIhrAttendServiceImpl implements DwsWorkingHourIhrAttendService {

    @Resource
    private DwsWorkingHourIhrAttendExtMapper dwsWorkingHourIhrAttendExtMapper;
    @Resource
    private DwsWorkingHourIhrAttendMapper dwsWorkingHourIhrAttendMapper;
    @Resource
    private BaseDataExtService baseDataExtService;
    @Resource
    private DwdLtcUserInfoMapper dwdLtcUserInfoMapper;
    @Resource
    private DwdWorkingHourMapper dwdWorkingHourMapper;


    @Override
    public Response statisticsWorkingHourIhrAttend(Boolean isPullAll, Date pullDate, Integer pullDay, Long parentUnitId) {
        // 查询所有部门的标准费率，没有配置，默认为100
        Map<Long, BigDecimal> standardWorkingHourRateMap = getStandardWorkingHourRateMap();
        if (isPullAll != null && isPullAll) {
            AllPull(parentUnitId, standardWorkingHourRateMap);
        } else {
            incrementPull(pullDate, pullDay, parentUnitId, standardWorkingHourRateMap);
        }
        return Response.response();
    }

    @Override
    public List<WorkingHourIhrAttend> reportWorkingHourIhrAttends(List<Long> orgIds, String attendanceStartDate, String attendanceEndDate, Long companyId) {
        return dwsWorkingHourIhrAttendExtMapper.reportWorkingHourIhrAttends(orgIds, attendanceStartDate, attendanceEndDate, companyId);
    }


    private void incrementPull(Date pullDate, Integer pullDay, Long parentUnitId, Map<Long, BigDecimal> standardWorkingHourRateMap) {
        // 判断是否全量拉数
        if (pullDate == null) pullDate = new Date();
        // 默认不填写拉取7天数据
        if (pullDay == null || pullDay < 0) pullDay = 7;

        // 日期分批获取，最大值只能在7天之内
        int intervalDay = 7;
        int count = 0;
        int balanceDay = pullDay % intervalDay;
        if (pullDay >= 7) count = pullDay / intervalDay;

        // 拉取项目工时
        pullWorkingHourByCreateUpdateDate(count, balanceDay, pullDate, intervalDay, parentUnitId, standardWorkingHourRateMap);
        // 拉取hr工时
        pullIhrByCreateUpdateDate(count, balanceDay, pullDate, intervalDay, parentUnitId, standardWorkingHourRateMap);
    }

    private void pullWorkingHourByCreateUpdateDate(int count, int balanceDay, Date pullDate, Integer intervalDay, Long parentUnitId,
                                                   Map<Long, BigDecimal> standardWorkingHourRateMap) {
        for (int i = 0; i < count; i++) {
            Date pullStartAt = DateUtil.addDay(pullDate, -intervalDay);
            // 拉取数据
            pullWorkingHourIncrementData(parentUnitId, pullStartAt, pullDate, standardWorkingHourRateMap);
            pullDate = pullStartAt;
        }

        // 处理剩余天数
        if (balanceDay > 0) {
            pullWorkingHourIncrementData(parentUnitId, DateUtil.addDay(pullDate, -balanceDay), pullDate, standardWorkingHourRateMap);
        }
    }

    private void pullWorkingHourIncrementData(Long parentUnitId, Date pullStartAt, Date pullEndAt,
                                              Map<Long, BigDecimal> standardWorkingHourRateMap) {
        int pullCount = 3000;
        // 拉取数据量进行计算，超过3000条分批处理
        int totalPullCount = dwsWorkingHourIhrAttendExtMapper.selectCountWorkingHourIhrAttendsByWorkingHourCreateUpdateDate(parentUnitId, pullStartAt, pullEndAt);
        if (totalPullCount > 3000) {
            splitBatchHandleWorkingHourPullData(totalPullCount, pullCount, parentUnitId, pullStartAt, pullEndAt, standardWorkingHourRateMap);
        } else if (totalPullCount > 0) {
            // 获取需要拉取的数据
            List<DwsWorkingHourIhrAttendDto> workingHourIhrAttendDtos =
                    dwsWorkingHourIhrAttendExtMapper.selectWorkingHourIhrAttendsByWorkingHourCreateUpdateDate(parentUnitId, pullStartAt, pullEndAt, null, null);
            if (CollectionUtils.isEmpty(workingHourIhrAttendDtos)) return;
            // 修正数据
            List<DwsWorkingHourIhrAttend> workingHourIhrAttends = correct(workingHourIhrAttendDtos);
            // 处理数据
            handleStandardWorkingHourRateMapForWorkingHour(workingHourIhrAttends, standardWorkingHourRateMap);
            // 进行插入或更新到详情表
            saveOrUpdateDetail(workingHourIhrAttends, true);
        }
    }

    private void splitBatchHandleWorkingHourPullData(int totalPullCount, int pullCount, Long parentUnitId, Date pullStartAt, Date pullEndAt,
                                                     Map<Long, BigDecimal> standardWorkingHourRateMap) {
        // 页码
        int pageNum = totalPullCount / pullCount + 1;
        for (int i = 1; i <= pageNum; i++) {
            // 获取需要拉取的数据
            List<DwsWorkingHourIhrAttendDto> workingHourIhrAttendDtos =
                    dwsWorkingHourIhrAttendExtMapper.selectPageWorkingHourIhrAttendsByWorkingHourCreateUpdateDate(
                            parentUnitId, pullStartAt, pullEndAt, (i - 1) * pullCount, pullCount);
            if (CollectionUtils.isEmpty(workingHourIhrAttendDtos)) return;
            // 修正数据
            List<DwsWorkingHourIhrAttend> workingHourIhrAttends = correct(workingHourIhrAttendDtos);
            // 处理数据
            handleStandardWorkingHourRateMapForWorkingHour(workingHourIhrAttends, standardWorkingHourRateMap);
            // 进行插入或更新到详情表
            saveOrUpdateDetail(workingHourIhrAttends, true);

        }
    }

    private void pullIhrByCreateUpdateDate(int count, int balanceDay, Date pullDate, Integer intervalDay, Long parentUnitId,
                                           Map<Long, BigDecimal> standardWorkingHourRateMap) {
        for (int i = 0; i < count; i++) {
            Date pullStartAt = DateUtil.addDay(pullDate, -intervalDay);
            // 拉取数据
            pullIhrIncrementData(parentUnitId, pullStartAt, pullDate, standardWorkingHourRateMap);
            pullDate = pullStartAt;
        }

        // 处理剩余天数
        if (balanceDay > 0) {
            pullIhrIncrementData(parentUnitId, DateUtil.addDay(pullDate, -balanceDay), pullDate, standardWorkingHourRateMap);
        }
    }

    private void pullIhrIncrementData(Long parentUnitId, Date pullStartAt, Date pullEndAt,
                                      Map<Long, BigDecimal> standardWorkingHourRateMap) {
        int pullCount = 3000;
        // 拉取数据量进行计算，超过3000条分批处理
        int totalPullCount = dwsWorkingHourIhrAttendExtMapper.selectCountWorkingHourIhrAttendsByIhrCreateUpdateDate(parentUnitId, pullStartAt, pullEndAt);
        if (totalPullCount > 3000) {
            splitBatchHandleIhrPullData(totalPullCount, pullCount, parentUnitId, pullStartAt, pullEndAt, standardWorkingHourRateMap);
        } else if (totalPullCount > 0) {
            // 获取需要拉取的数据
            List<DwsWorkingHourIhrAttend> workingHourIhrAttends = dwsWorkingHourIhrAttendExtMapper.selectWorkingHourIhrAttendsByIhrCreateUpdateDate(parentUnitId, pullStartAt, pullEndAt);
            if (CollectionUtils.isEmpty(workingHourIhrAttends)) return;
            // 处理数据
            handleStandardWorkingHourRateMapForIhr(workingHourIhrAttends, standardWorkingHourRateMap);
            // 进行插入或更新到详情表
            saveOrUpdateDetail(workingHourIhrAttends, false);
        }
    }

    private void splitBatchHandleIhrPullData(int totalPullCount, int pullCount, Long parentUnitId, Date pullStartAt, Date pullEndAt,
                                             Map<Long, BigDecimal> standardWorkingHourRateMap) {
        // 页码
        int pageNum = totalPullCount / pullCount + 1;
        for (int i = 1; i <= pageNum; i++) {
            // 获取需要拉取的数据
            List<DwsWorkingHourIhrAttend> workingHourIhrAttends =
                    dwsWorkingHourIhrAttendExtMapper.selectPageWorkingHourIhrAttendsByIhrCreateUpdateDate(
                            parentUnitId, pullStartAt, pullEndAt, (i - 1) * pullCount, pullCount);
            if (CollectionUtils.isEmpty(workingHourIhrAttends)) return;
            // 处理数据
            handleStandardWorkingHourRateMapForIhr(workingHourIhrAttends, standardWorkingHourRateMap);
            // 进行插入或更新到详情表
            saveOrUpdateDetail(workingHourIhrAttends, false);

        }
    }

    private Map<Long, BigDecimal> getStandardWorkingHourRateMap() {
        Map<Long, BigDecimal> standardWorkingHourRateMap = new HashMap<>();
        List<OrgLaborCostTypeSet> orgLaborCostTypeSetList = baseDataExtService.selectStandardWorkingHourRate();
        if (CollectionUtils.isNotEmpty(orgLaborCostTypeSetList)) {
            standardWorkingHourRateMap = orgLaborCostTypeSetList.stream()
                    .collect(Collectors.toMap(OrgLaborCostTypeSet::getOrgId, OrgLaborCostTypeSet::getStandardWorkingHourRate));
        }
        return standardWorkingHourRateMap;
    }

    private void AllPull(Long parentUnitId, Map<Long, BigDecimal> standardWorkingHourRateMap) {
        // 拉取工时表
        allPullWorkingHour(parentUnitId, standardWorkingHourRateMap);
        // 拉取考勤表
        allPullIhr(parentUnitId, standardWorkingHourRateMap);
    }

    private void allPullWorkingHour(Long parentUnitId, Map<Long, BigDecimal> standardWorkingHourRateMap) {
        // 获取工时表现有申请日期最小与最大日期
        Map<String, Date> maxAndMinApplyDate = dwsWorkingHourIhrAttendExtMapper.selectDwdWorkingHourMaxAndMinApplyDate();
        Date maxApplyDate = maxAndMinApplyDate.get("maxApplyDate");
        Date minApplyDate = maxAndMinApplyDate.get("minApplyDate");


        while (minApplyDate.before(DateUtil.addDay(maxApplyDate, 30))) {
            // 间隔三十天拉取 项目工时数据
            pullWorkingHourByApplyDate(minApplyDate, parentUnitId, standardWorkingHourRateMap);

            minApplyDate = DateUtil.addDay(minApplyDate, 30);
        }
    }

    private void pullWorkingHourByApplyDate(Date minApplyDate, Long parentUnitId, Map<Long, BigDecimal> standardWorkingHourRateMap) {
        // 判断是否全量拉数, 间隔31天进行扫描表数据，dwd_working_hour进行统计
        if (minApplyDate == null) minApplyDate = new Date();
        // 默认不填写拉取31天数据
        Date pullStartAt = DateUtil.addDay(minApplyDate, -30);

        // 获取需要拉取的数据
        List<DwsWorkingHourIhrAttend> workingHourIhrAttends =
                dwsWorkingHourIhrAttendExtMapper.selectWorkingHourIhrAttendsByWorkingHourApplyDate(parentUnitId, pullStartAt, minApplyDate);
        if (CollectionUtils.isEmpty(workingHourIhrAttends)) return;
        // 处理数据
        handleStandardWorkingHourRateMapForWorkingHour(workingHourIhrAttends, standardWorkingHourRateMap);
        // 进行插入或更新到详情表
        saveOrUpdateDetail(workingHourIhrAttends, true);
    }

    private void handleStandardWorkingHourRateMapForWorkingHour(List<DwsWorkingHourIhrAttend> workingHourIhrAttends,
                                                                Map<Long, BigDecimal> standardWorkingHourRateMap) {
        // 获取部门名称
        Set<Long> orgIds = new HashSet<>();
        // 获取用户名称
        Set<Long> userIds = new HashSet<>();
        for (DwsWorkingHourIhrAttend workingHourIhrAttend : workingHourIhrAttends) {
            if (workingHourIhrAttend.getOrgId() != null) {
                orgIds.add(workingHourIhrAttend.getOrgId());
            }
            userIds.add(workingHourIhrAttend.getUserId());
        }
        Map<Long, String> orgMap = getOrgMapByIds(orgIds);
        Map<Long, List<DwsUserOrgUnitNameDto>> userOrgUnitNameMap = getUserOrgUnitNameDtos(userIds);
        for (DwsWorkingHourIhrAttend workingHourIhrAttend : workingHourIhrAttends) {
            setWorkingHourIhrAttend(workingHourIhrAttend, userOrgUnitNameMap, orgMap, standardWorkingHourRateMap);

            workingHourIhrAttend.setIhrAttendHours(BigDecimal.ZERO);
            workingHourIhrAttend.setIhrOrgStandardWorkingHours(BigDecimal.ZERO);
        }
    }

    private void setWorkingHourIhrAttend(DwsWorkingHourIhrAttend workingHourIhrAttend, Map<Long, List<DwsUserOrgUnitNameDto>> userOrgUnitNameMap,
                                         Map<Long, String> orgMap, Map<Long, BigDecimal> standardWorkingHourRateMap) {
        List<DwsUserOrgUnitNameDto> dwsUserOrgUnitNameDtos = userOrgUnitNameMap.get(workingHourIhrAttend.getUserId());
        String parentUnitName = "";
        String name = "";
        if (CollectionUtils.isNotEmpty(dwsUserOrgUnitNameDtos)) {
            Map<Long, String> parentUnitNameMap =
                    dwsUserOrgUnitNameDtos.stream().collect(Collectors.toMap(DwsUserOrgUnitNameDto::getParentUnitId, DwsUserOrgUnitNameDto::getParentUnitName));
            parentUnitName = parentUnitNameMap.get(workingHourIhrAttend.getParentUnitId());
            name = dwsUserOrgUnitNameDtos.get(0).getName();
        }

        if (StringUtils.isBlank(parentUnitName) && workingHourIhrAttend.getParentUnitId() != null) {
            Unit unit = baseDataExtService.selectParentUnItName(workingHourIhrAttend.getParentUnitId());
            parentUnitName = unit != null ? unit.getUnitName() : null;
        }
        workingHourIhrAttend.setParentUnitName(parentUnitName);

        if (StringUtils.isBlank(name) && workingHourIhrAttend.getUserId() != null) {
            DwdLtcUserInfo dwdLtcUserInfo = dwdLtcUserInfoMapper.selectByPrimaryKey(workingHourIhrAttend.getUserId());
            name = dwdLtcUserInfo != null ? dwdLtcUserInfo.getName() : null;
        }
        workingHourIhrAttend.setUserMipName(name);

        workingHourIhrAttend.setOrgName(workingHourIhrAttend.getOrgId() != null ? orgMap.get(workingHourIhrAttend.getOrgId()) : null);
        BigDecimal standardWorkingHourRate = workingHourIhrAttend.getOrgId() != null && standardWorkingHourRateMap.get(workingHourIhrAttend.getOrgId()) != null ?
                standardWorkingHourRateMap.get(workingHourIhrAttend.getOrgId()) : new BigDecimal(100);
        workingHourIhrAttend.setIhrStandardWorkingHoursRate(standardWorkingHourRate);

        workingHourIhrAttend.setIhrOrgStandardWorkingHours(BigDecimal.ZERO);
    }

    private Map<Long, List<DwsUserOrgUnitNameDto>> getUserOrgUnitNameDtos(Set<Long> userIds) {
        Map<Long, List<DwsUserOrgUnitNameDto>> userOrgUnitNameMap = new HashMap<>();
        if (CollectionUtils.isEmpty(userIds)) return userOrgUnitNameMap;

        List<Long> userIdsList = new ArrayList<>(userIds);
        List<List<Long>> userIdsSplitList = ListUtils.splistList(userIdsList, 300);
        userIdsSplitList.forEach(userIdList -> {
            List<DwsUserOrgUnitNameDto> userOrgUnitNameDtos = dwsWorkingHourIhrAttendExtMapper.selectNamesByIds(userIdList);
            if (CollectionUtils.isNotEmpty(userOrgUnitNameDtos)) {
                userOrgUnitNameMap.putAll(userOrgUnitNameDtos.stream().collect(Collectors.groupingBy(DwsUserOrgUnitNameDto::getUserId)));
            }
        });
        return userOrgUnitNameMap;
    }

    private Map<Long, String> getOrgMapByIds(Set<Long> orgIds) {
        Map<Long, String> orgMap = new HashMap<>();
        if (CollectionUtils.isEmpty(orgIds)) return orgMap;

        List<Long> orgIdsList = new ArrayList<>(orgIds);
        List<List<Long>> orgIdsSplitList = ListUtils.splistList(orgIdsList, 300);
        orgIdsSplitList.forEach(orgIdList -> {
            List<DwdLtcOrganization> organizationList = dwsWorkingHourIhrAttendExtMapper.selectOrgNamesByIds(orgIdList);
            if (CollectionUtils.isNotEmpty(organizationList)) {
                orgMap.putAll(organizationList.stream().collect(Collectors.toMap(IdEntity::getId, DwdLtcOrganization::getName)));
            }
        });
        return orgMap;
    }

    private void allPullIhr(Long parentUnitId, Map<Long, BigDecimal> standardWorkingHourRateMap) {
        // 获取考勤表现有申请日期最小与最大日期
        Map<String, Date> maxAndMinAttendDate = dwsWorkingHourIhrAttendExtMapper.selectIhrMaxAndMinAttendDate();
        Date maxAttendDate = maxAndMinAttendDate.get("maxAttendDate");
        Date minAttendDate = maxAndMinAttendDate.get("minAttendDate");
        while (minAttendDate.before(DateUtil.addDay(maxAttendDate, 7))) {
            // 间隔七天拉取 项目工时数据
            pullIhrByAttendDate(minAttendDate, parentUnitId, standardWorkingHourRateMap);

            minAttendDate = DateUtil.addDay(minAttendDate, 7);
        }

    }

    private void pullIhrByAttendDate(Date minAttendDate,
                                     Long parentUnitId, Map<Long, BigDecimal> standardWorkingHourRateMap) {
        // 判断是否全量拉数, 间隔7天进行扫描表数据，dwd_ihr_attend_detail有角色 进行统计
        if (minAttendDate == null) minAttendDate = new Date();
        // 默认不填写拉取7天数据
        Date pullStartAt = DateUtil.addDay(minAttendDate, -7);

        // 获取需要拉取的数据
        List<DwsWorkingHourIhrAttend> workingHourIhrAttends = dwsWorkingHourIhrAttendExtMapper.selectWorkingHourIhrAttendsByIhrAttendDate(parentUnitId, pullStartAt, minAttendDate);
        if (CollectionUtils.isEmpty(workingHourIhrAttends)) return;
        // 处理数据
        handleStandardWorkingHourRateMapForIhr(workingHourIhrAttends, standardWorkingHourRateMap);
        // 进行插入或更新到详情表
        saveOrUpdateDetail(workingHourIhrAttends, false);
    }

    private void handleStandardWorkingHourRateMapForIhr(List<DwsWorkingHourIhrAttend> workingHourIhrAttends, Map<Long, BigDecimal> standardWorkingHourRateMap) {
        // 获取部门名称
        Set<Long> orgIds = new HashSet<>();
        // 获取用户名称
        Set<Long> userIds = new HashSet<>();
        for (DwsWorkingHourIhrAttend workingHourIhrAttend : workingHourIhrAttends) {
            if (workingHourIhrAttend.getOrgId() != null) {
                orgIds.add(workingHourIhrAttend.getOrgId());
            }
            userIds.add(workingHourIhrAttend.getUserId());
        }
        Map<Long, String> orgMap = getOrgMapByIds(orgIds);
        Map<Long, List<DwsUserOrgUnitNameDto>> userOrgUnitNameMap = getUserOrgUnitNameDtos(userIds);

        for (DwsWorkingHourIhrAttend workingHourIhrAttend : workingHourIhrAttends) {
            setWorkingHourIhrAttend(workingHourIhrAttend, userOrgUnitNameMap, orgMap, standardWorkingHourRateMap);

            workingHourIhrAttend.setActualWorkingHours(BigDecimal.ZERO);
            workingHourIhrAttend.setTotalWorkingHoursCost(BigDecimal.ZERO);
            workingHourIhrAttend.setPendingWorkingHours(BigDecimal.ZERO);
        }
    }

    /**
     * @param workingHourIhrAttends 需要更新的Model
     * @param flag                  true:项目工时更新，false:考勤更新
     * <AUTHOR>
     * @Date 2023-03-22, 0022 下午 03:55
     */
    private void saveOrUpdateDetail(List<DwsWorkingHourIhrAttend> workingHourIhrAttends, boolean flag) {
        Date now = new Date();
        BigDecimal rateBD = new BigDecimal("0.01");
        List<List<DwsWorkingHourIhrAttend>> workingHourIhrAttendLists = ListUtils.splistList(workingHourIhrAttends, 1000);
        for (List<DwsWorkingHourIhrAttend> workingHourIhrAttendList : workingHourIhrAttendLists) {
            List<DwsWorkingHourIhrAttend> saveDetailDtoList = new ArrayList<>();
            for (DwsWorkingHourIhrAttend detailDto : workingHourIhrAttendList) {
                // 项目工时，维度（使用单位 + 申请日期 + 用户mip）
                // 考勤工时，维度（申请日期 + 用户mip）
                DwsWorkingHourIhrAttendExample example = new DwsWorkingHourIhrAttendExample();
                Long applyTimeDimensionId = detailDto.getApplyTimeDimensionId();
                if (applyTimeDimensionId == null && detailDto.getApplyDate() != null) {
                    String dateString = DateUtil.format(detailDto.getApplyDate(), DATE_INTEGER_PATTERN);
                    applyTimeDimensionId = Long.parseLong(dateString);
                }
                if (applyTimeDimensionId == null) continue;
                DwsWorkingHourIhrAttendExample.Criteria criteria = example.createCriteria();
                if (flag) {
                    criteria.andParentUnitIdEqualTo(detailDto.getParentUnitId());
                }
                criteria.andApplyTimeDimensionIdEqualTo(applyTimeDimensionId)
                        .andUserMipEqualTo(detailDto.getUserMip())
                        .andDeletedFlagEqualTo(Boolean.FALSE);
                List<DwsWorkingHourIhrAttend> oldDwsWorkingHourIhrAttends = dwsWorkingHourIhrAttendMapper.selectByExample(example);
                if (CollectionUtils.isEmpty(oldDwsWorkingHourIhrAttends)) {
                    detailDto.setDeletedFlag(Boolean.FALSE);
                    if (!flag) {
                        BigDecimal ihrStandardWorkingHoursRate = detailDto.getIhrStandardWorkingHoursRate();
                        if (ihrStandardWorkingHoursRate == null) ihrStandardWorkingHoursRate = new BigDecimal(100);
                        detailDto.setIhrOrgStandardWorkingHours(rateBD.multiply(ihrStandardWorkingHoursRate.multiply(detailDto.getIhrAttendHours())));
                    }
                    saveDetailDtoList.add(detailDto);
                    continue;
                }
                if (flag) {
                    // 防止项目工时表数据更新，ihr没有更新数据时，保留原来的数据，无需重新统计，只需更改项目工时信息
                    detailDto.setIhrAttendHours(null);
                    detailDto.setIhrOrgStandardWorkingHours(null);
                    detailDto.setId(oldDwsWorkingHourIhrAttends.get(0).getId());
                    detailDto.setCreateAt(oldDwsWorkingHourIhrAttends.get(0).getCreateAt());
                    detailDto.setCreateBy(oldDwsWorkingHourIhrAttends.get(0).getCreateBy());
                    dwsWorkingHourIhrAttendMapper.updateByPrimaryKeySelective(detailDto);
                } else {
                    //项目工时，多了维度之后，如果有多个单位，跑出来会有多条数据，每条数据都有考勤数据。所以考勤工时通过唯一性条件（申请日期 + 用户mip）会查出多条数据，要for循环去更新
                    for (DwsWorkingHourIhrAttend oldDwsWorkingHourIhrAttend : oldDwsWorkingHourIhrAttends) {
                        BigDecimal ihrStandardWorkingHoursRateBD = oldDwsWorkingHourIhrAttend.getIhrStandardWorkingHoursRate();
                        // 更新数据,HR部门ID优先以dwd_working_hour表中的ID为准，如果IHR有工时，但是dwd_working_hour无工时，则取dwd_ihr_attend_detail的部门ID
                        if (oldDwsWorkingHourIhrAttend.getActualWorkingHours() == null
                                || oldDwsWorkingHourIhrAttend.getActualWorkingHours().compareTo(BigDecimal.ZERO) == 0) {
                            oldDwsWorkingHourIhrAttend.setOrgId(detailDto.getOrgId());
                            oldDwsWorkingHourIhrAttend.setOrgName(detailDto.getOrgName());
                            ihrStandardWorkingHoursRateBD = detailDto.getIhrStandardWorkingHoursRate();
                        }
//                    oldDwsWorkingHourIhrAttend.setParentUnitId(detailDto.getParentUnitId());
//                    oldDwsWorkingHourIhrAttend.setParentUnitName(detailDto.getParentUnitName());
                        oldDwsWorkingHourIhrAttend.setUserMipName(detailDto.getUserMipName());
                        oldDwsWorkingHourIhrAttend.setIhrStandardWorkingHoursRate(ihrStandardWorkingHoursRateBD);
                        oldDwsWorkingHourIhrAttend.setIhrOrgStandardWorkingHours(rateBD.multiply(ihrStandardWorkingHoursRateBD.multiply(detailDto.getIhrAttendHours())));
                        oldDwsWorkingHourIhrAttend.setIhrAttendHours(detailDto.getIhrAttendHours());
                        oldDwsWorkingHourIhrAttend.setUpdateAt(now);
                        oldDwsWorkingHourIhrAttend.setUpdateBy(oldDwsWorkingHourIhrAttend.getCreateBy());
                        dwsWorkingHourIhrAttendMapper.updateByPrimaryKeySelective(oldDwsWorkingHourIhrAttend);
                    }
                }
            }

            // 批量插入数据
            if (saveDetailDtoList.size() > 0) {
                List<List<DwsWorkingHourIhrAttend>> detailSplitList = ListUtils.splistList(saveDetailDtoList, 500);
                detailSplitList.forEach(insertDetails -> dwsWorkingHourIhrAttendExtMapper.batchInsertDetail(insertDetails));
            }
        }
    }

    /**
     * 由于定时任务的分页模式，有可能导致 同一parentUnitId+同一userMip+同一applyDate 的数据没分到同一页，不同页的数据 统计的时候分开计算，再去更新 dwd_ihr_attend_detail 同一条数据，产生数据异常，此处作逻辑修正
     *
     * @param workingHourIhrAttends
     * @return
     */
    private List<DwsWorkingHourIhrAttend> correct(List<DwsWorkingHourIhrAttendDto> workingHourIhrAttends) {
        List<DwsWorkingHourIhrAttend> workingHourIhrAttendsNew = new ArrayList<>();
        if (CollectionUtils.isEmpty(workingHourIhrAttends)) {
            return workingHourIhrAttendsNew;
        }
        for (DwsWorkingHourIhrAttendDto detailDto : workingHourIhrAttends) {
            DwdWorkingHourExample example = new DwdWorkingHourExample();
            example.createCriteria().andParentUnitIdEqualTo(detailDto.getParentUnitId())
                    .andApplyTimeDimensionIdEqualTo(detailDto.getApplyTimeDimensionId())
                    .andUserMipEqualTo(detailDto.getUserMip())
                    .andDeletedFlagEqualTo(Boolean.FALSE);
            long actualNum = dwdWorkingHourMapper.countByExample(example);
            if (detailDto.getNum() != actualNum) {
                List<DwsWorkingHourIhrAttendDto> workingHourIhrAttendsCorrect = dwsWorkingHourIhrAttendExtMapper.selectWorkingHourIhrAttendsByWorkingHourCreateUpdateDate(detailDto.getParentUnitId(), null, null, detailDto.getApplyTimeDimensionId(), detailDto.getUserMip());
                if (CollectionUtils.isNotEmpty(workingHourIhrAttendsCorrect)) {
                    workingHourIhrAttendsNew.add(workingHourIhrAttendsCorrect.get(0));
                } else {
                    workingHourIhrAttendsNew.add(detailDto);
                }
            } else {
                workingHourIhrAttendsNew.add(detailDto);
            }
        }
        return workingHourIhrAttendsNew;
    }

}
