package com.midea.pam.ctc.web;

import com.github.pagehelper.PageInfo;
import com.midea.mcomponent.core.util.Assert;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.CostCollectionDto;
import com.midea.pam.common.ctc.dto.LaborCostDetailDto;
import com.midea.pam.common.ctc.excelVo.CostCollectionExportExcelVo;
import com.midea.pam.common.enums.CodePrefix;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.service.CarryoverBillAccountingService;
import com.midea.pam.ctc.service.CostCollectionService;
import com.midea.pam.ctc.service.LaborCostDetailService;
import com.midea.pam.ctc.service.MaterialActualCostDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @program: pam
 * @description: CostCollectionController
 * @author: gaojh1
 * @create: 2019-3-12 10:51
 **/
@Api("成本归集")
@RestController
@RequestMapping("ctc/costCollection")
public class CostCollectionController {

    @Resource
    CostCollectionService costCollectionService;
    @Resource
    LaborCostDetailService laborCostDetailService;
    @Resource
    MaterialActualCostDetailService materialActualCostDetailService;
    @Resource
    CarryoverBillAccountingService carryoverBillAccountingService;


    @Deprecated
    @ApiOperation(value = "成本归集分页查询")
    @GetMapping("page")
    public Response page(@RequestParam(required = false) final Long id,
                          @RequestParam(required = false) String projectCode,
                          @RequestParam(required = false) Long ouId,
                          @RequestParam(required = false) String costMethodMain,
                          @RequestParam(required = false) String projectName,
                          @RequestParam(required = false) String startTime,
                          @RequestParam(required = false) String endTime,
                         @RequestParam(required = true, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = true, defaultValue = "10")final Integer pageSize) {

        CostCollectionDto query = new CostCollectionDto();
        query.setId(id);
        query.setProjectCode(projectCode);
        query.setOuId(ouId);
        query.setProjectName(projectName);
        query.setCostMethodMain(costMethodMain);
        if (startTime != null) {
            query.setStartTime(DateUtil.parseDate(startTime,DateUtil.DATE_PATTERN));
        }
        if (endTime != null) {
            query.setEndTime(DateUtil.parseDate(endTime,DateUtil.DATE_PATTERN));
        }
        query.setPageNum(pageNum);
        query.setPageSize(pageSize);
        DataResponse<PageInfo<CostCollectionDto>> response = Response.dataResponse();
        return response.setData(costCollectionService.page(query));
    }

    @ApiOperation(value = "成本归集")
    @GetMapping("collection")
    public Response collection(@RequestParam(required = false) final Long ouId,
                         @RequestParam(required = true) final String collectionDate) {

        DateUtils.parse(collectionDate, DateUtils.getDate());
        costCollectionService.collectionByOu(ouId, DateUtils.parse(collectionDate, DateUtils.getDate()));
        DataResponse<String> response = Response.dataResponse();
        return response.setData("0");
    }

    @ApiOperation(value = "自动校正未入账的工时科目")
    @GetMapping("updateLaborCostHardWorking")
    public Response updateLaborCostHardWorking(@RequestParam(required = true) @ApiParam(value = "ouId") final Long ouId,
                                               @RequestParam(required = false) @ApiParam(value = "项目id") final Long projectId,
                                               @RequestParam(required = false) @ApiParam(value = "考勤用户id") final Long userId,
                                               @RequestParam(required = false) @ApiParam(value = "填报人部门") final String applyOrg,
                                               @RequestParam(required = false) @ApiParam(value = "考勤日期开始") final String applyDateStart,
                                               @RequestParam(required = false) @ApiParam(value = "考勤日期结束") final String applyDateEnd) {
        LaborCostDetailDto queryDto = new LaborCostDetailDto();
        queryDto.setOuId(ouId);
        queryDto.setProjectId(projectId);
        queryDto.setUserId(userId);
        queryDto.setApplyOrg(applyOrg);
        if (StringUtils.isNotEmpty(applyDateStart)) {
            queryDto.setApplyDateStart(DateUtils.parse(applyDateStart, DateUtils.getDate()));
        }
        if (StringUtils.isNotEmpty(applyDateEnd)) {
            queryDto.setApplyDateEnd(DateUtils.parse(applyDateEnd, DateUtils.getDate()));
        }
        laborCostDetailService.updateLaborCostHardWorking(queryDto);
        DataResponse<String> response = Response.dataResponse();
        return response.setData("0");
    }

    @ApiOperation(value = "成本归集检查和更新")
    @GetMapping("updateCollection")
    public Response updateCollection(@RequestParam(required = true) final Long projectId,
                         @RequestParam(required = true) final String collectionDateStart,
                         @RequestParam(required = true) final String collectionDateEnd) {
        costCollectionService.updateCollection(projectId,
                DateUtils.parse(collectionDateStart, DateUtils.getDate()), DateUtils.parse(collectionDateEnd, DateUtils.getDate()));
        DataResponse<String> response = Response.dataResponse();
        return response.setData("0");
    }

    @ApiOperation(value = "成本归集详情")
    @GetMapping("detail")
    public Response detail(@RequestParam(required = true) final Long id){
        DataResponse<CostCollectionDto> response = Response.dataResponse();
        CostCollectionDto costCollectionDto = costCollectionService.queryCostCollectionById(id);
        return response.setData(costCollectionDto);
    }

    @ApiOperation(value = "成本信息导出")
    @GetMapping("/export")
    public void export(HttpServletResponse response,
                               @RequestParam(required = false) String projectCode,
                               @RequestParam(required = false) Long ouId,
                               @RequestParam(required = false) String costMethodMain,
                               @RequestParam(required = false) String projectName,
                               @RequestParam(required = false) String startTime,
                               @RequestParam(required = false) String endTime)  {
        CostCollectionDto query = new CostCollectionDto();
        query.setProjectCode(projectCode);
        query.setOuId(ouId);
        query.setProjectName(projectName);
        query.setCostMethodMain(costMethodMain);
        if (startTime != null) {
            query.setStartTime(DateUtil.parseDate(startTime,DateUtil.DATE_PATTERN));
        }
        if (endTime != null) {
            query.setEndTime(DateUtil.parseDate(endTime,DateUtil.DATE_PATTERN));
        }
        query.setPageNum(1);
        query.setPageSize(Integer.MAX_VALUE);
        PageInfo<CostCollectionDto> page = costCollectionService.page(query);
        Assert.notEmpty(page.getList(), "无数据");
        List<CostCollectionExportExcelVo> excelVos = new ArrayList<>();
        Integer index = 1;
        for (CostCollectionDto dto : page.getList()) {
            CostCollectionExportExcelVo excelVo = new CostCollectionExportExcelVo();
            excelVo.setIndex(index);

            BeanUtils.copyProperties(dto, excelVo);
            //归集日期
            if (dto.getCollectionDate() != null) {
                excelVo.setCollectionDate(DateUtil.format(dto.getCollectionDate(),DateUtil.DATE_PATTERN));
            }

            /*if(null != dto.getCarryStatus()){
                if (1 == dto.getCarryStatus()) {
                    excelVo.setCarryStatus("未结转");
                }else if (1 == dto.getCarryStatus()) {
                    excelVo.setCarryStatus("已结转");
                }else if (2 == dto.getCarryStatus()) {
                    excelVo.setCarryStatus("无需入账");
                }
            }*/

            index++;
            excelVos.add(excelVo);
        }
        String fileName = "成本归集信息导出"
                + CacheDataUtils.generateSequence(2, CodePrefix.COST_COLLECTION.code())+".xls";
        //导出操作
        ExportExcelUtil.exportExcel(excelVos, null, "Sheet1", CostCollectionExportExcelVo.class, fileName, response);
    }

    @ApiOperation(value = "计算本次追溯调整金额")
    @GetMapping("/calcAdjustmentAmount")
    public Response calcAdjustmentAmount(@RequestParam(required = false) final Long carryoverId) {
        BigDecimal amount = materialActualCostDetailService.updateTraceFlagAndCalcAdjustmentAmount(carryoverId);
        DataResponse<String> response = Response.dataResponse();
        return response.setData(String.valueOf(amount));
    }

    @ApiOperation(value = "incomeErpPushTest")
    @GetMapping("/incomeErpPushTest")
    public Response incomeErpPushTest(@RequestParam(required = false) final Long id) {
        carryoverBillAccountingService.incomePushErpTest(id);
        DataResponse<String> response = Response.dataResponse();
        return response.setData("0");
    }

    @ApiOperation(value = "costPushErpTest")
    @GetMapping("/costPushErpTest")
    public Response costPushErpTest(@RequestParam(required = false) final Long id) {
        carryoverBillAccountingService.costPushErpTest(id);
        DataResponse<String> response = Response.dataResponse();
        return response.setData("0");
    }

    @ApiOperation(value = "listWithDetail")
    @GetMapping("/listWithDetail")
    public Response listWithDetail(@RequestParam Long projectId,@RequestParam Date startTime,@RequestParam Date endTime) {
        DataResponse<List<CostCollectionDto>> response = Response.dataResponse();
        CostCollectionDto query = new CostCollectionDto();
        query.setProjectId(projectId);
        query.setCollectionDateStart(startTime);
        query.setCollectionDateEnd(endTime);
        return response.setData(costCollectionService.listWithDetail(query));
    }

    @GetMapping(value = "/syncLaborCostHardWorking/test")
    public Response test(@RequestParam String s) {
        laborCostDetailService.syncLaborCostHardWorking(s);
        return new DataResponse<>();
    }
}
