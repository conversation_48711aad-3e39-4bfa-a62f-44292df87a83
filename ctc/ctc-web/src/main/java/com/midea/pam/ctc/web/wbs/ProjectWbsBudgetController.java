package com.midea.pam.ctc.web.wbs;

import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.ProjectWbsBudgetDto;
import com.midea.pam.common.ctc.dto.ProjectWbsBudgetObjectDto;
import com.midea.pam.common.ctc.excelVo.ProjectWbsBudgetImportResponseExcelVO;
import com.midea.pam.ctc.excel.service.impl.ProjectWbsBudgetFlexibleImportService;
import com.midea.pam.ctc.wbs.service.ProjectWbsBudgetService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Api("项目wbs预算")
@RestController
@RequestMapping("projectWbsBudget")
public class ProjectWbsBudgetController {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private ProjectWbsBudgetService projectWbsBudgetService;
    @Resource
    private ProjectWbsBudgetFlexibleImportService projectWbsBudgetFlexibleImportService;

    /**
     * json: {
     * - wbsTemplateInfoId : wbs模板信息id
     * - projectCode: 项目编码
     * - data : [{
     * - - projectCode : 项目编码
     * - - field_1 : 动态列
     * - - field_2 : 动态列
     * - - field_1 : 动态列
     * - - description : 描述
     * - - activityCode : 活动事项编码
     * - - activityName : 活动类别名称
     * - - activityType : 活动类别属性
     * - - price : 预算金额
     * - }]
     * }
     */
    @ApiOperation(value = "新建项目-wbs预算汇总")
    @PostMapping("summaryWbsBudget")
    public Response summaryWbsBudget(@RequestBody Map map) {
        DataResponse<List<Map>> response = Response.dataResponse();
        return response.setData(projectWbsBudgetService.summaryWbsBudget(map));
    }

    @ApiOperation(value = "新建项目-项目活动预算汇总")
    @PostMapping("summaryProjectActivityBudget")
    public Response summaryProjectActivityBudget(@RequestBody Map map) {
        DataResponse<List<Map>> response = Response.dataResponse();
        return response.setData(projectWbsBudgetService.summaryProjectActivityBudget(map));
    }

    @ApiOperation(value = "导入wbs预算检查、导入wbs基线检查", notes = "版本v2")
    @PostMapping("v2/checkWbsBudgetFromExcel/{batchCode}/{wbsTemplateInfoId}")
    public Response checkWbsBudgetFromExcel(@RequestBody ProjectWbsBudgetImportResponseExcelVO importResponseExcelVO,
                                                  @ApiParam("批次号") @PathVariable String batchCode,
                                                  @ApiParam("wbs模板id") @PathVariable Long wbsTemplateInfoId) {
        DataResponse<ProjectWbsBudgetImportResponseExcelVO> response = Response.dataResponse();
        response.setData(projectWbsBudgetService.checkWbsBudgetFromExcel(importResponseExcelVO, batchCode, wbsTemplateInfoId));
        return response;
    }

    @ApiOperation(value = "WBS预算批量导入 ", notes = "版本v2")
    @PostMapping("v2/importWbsBudgetFromExcel")
    public Response importWbsBudgetFromExcel(@RequestBody ProjectWbsBudgetImportResponseExcelVO importResponseExcelVO) {
        DataResponse<ProjectWbsBudgetImportResponseExcelVO> response = Response.dataResponse();
        response.setData(projectWbsBudgetService.importWbsBudgetFromExcel(importResponseExcelVO));
        return response;
    }

    @ApiOperation(value = "查询wbs详情列表 - Excel导出")
    @GetMapping("findDetailWbsBudget")
    public Response findDetailWbsBudget(@ApiParam("项目id") @RequestParam Long projectId) {
        DataResponse<List<ProjectWbsBudgetDto>> response = Response.dataResponse();
        return response.setData(projectWbsBudgetService.findDetailWbsBudget(projectId));
    }

    @ApiOperation(value = "获取wbs预算关联信息", notes = "1、项目立项暂存后查看；2、")
    @GetMapping("findWbsBudgetInfo")
    public Response findWbsBudgetInfo(@RequestParam Long projectId){
        DataResponse<ProjectWbsBudgetObjectDto> response = Response.dataResponse();
        return response.setData( projectWbsBudgetService.findWbsBudgetInfoByWebfront(projectId));
    }

    @ApiOperation(value = "获取用户可选的wbs")
    @GetMapping("findUserWbsBudget")
    public Response findUserWbsBudget(@RequestParam("projectId") Long projectId){
        DataResponse<List<ProjectWbsBudgetDto>> response = Response.dataResponse();
        return response.setData( projectWbsBudgetService.findUserWbsBudget(projectId));
    }

    @ApiOperation(value = "数据初始化-项目wbs预算与基线导入")
    @PostMapping("importWbsBudgetAndBaselineFlexible")
    public Response importWbsBudgetAndBaselineFlexible(@ApiParam("wbs模板id") @RequestParam("wbsTemplateInfoId") Long wbsTemplateInfoId,
                                                       @ApiParam("是否同步第三方系统") @RequestParam(value = "resendFlag", required = false, defaultValue = "true") Boolean resendFlag,
                                                       @ApiParam("excel列表") @RequestBody List<Map> excelVoList) {
        logger.info("数据初始化-项目wbs预算与基线导入开始：wbsTemplateInfoId={}", wbsTemplateInfoId);
        return projectWbsBudgetFlexibleImportService.importData(excelVoList, wbsTemplateInfoId, resendFlag);
    }

    @ApiOperation(value = "获取项目下所有wbs编码")
    @GetMapping("findWbsByProjectId")
    public Response findWbsByProjectId(@RequestParam("projectId") Long projectId){
        return Response.dataResponse(projectWbsBudgetService.findWbsByProjectId(projectId));
    }
}
