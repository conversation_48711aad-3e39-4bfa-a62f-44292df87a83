package com.midea.pam.ctc.web.config;

import com.midea.pam.common.filter.AuthorityFilter;
import com.midea.pam.common.logs.SqlLogCollection;
import com.midea.pam.common.logs.SqlLogSignalQueue;
import com.midea.pam.common.util.SqlLogCollectionUtils;
import com.midea.pam.ctc.web.ApplicationIndustryController;
import com.midea.pam.ctc.web.ApplyContractUnitController;
import com.midea.pam.ctc.web.AssetDeprnAccountingController;
import com.midea.pam.ctc.web.AsyncRequestResultController;
import com.midea.pam.ctc.web.BasicCacheController;
import com.midea.pam.ctc.web.BudgetItemController;
import com.midea.pam.ctc.web.BusiSceneNonSaleController;
import com.midea.pam.ctc.web.CarryoverBillAccountingController;
import com.midea.pam.ctc.web.CarryoverBillController;
import com.midea.pam.ctc.web.CheckConfigController;
import com.midea.pam.ctc.web.CheckItemController;
import com.midea.pam.ctc.web.CheckResultController;
import com.midea.pam.ctc.web.CloudPrintController;
import com.midea.pam.ctc.web.CnapsInfoController;
import com.midea.pam.ctc.web.CodeRuleController;
import com.midea.pam.ctc.web.CollectionConfigurationController;
import com.midea.pam.ctc.web.CommonPushController;
import com.midea.pam.ctc.web.ContractClassificationController;
import com.midea.pam.ctc.web.ContractController;
import com.midea.pam.ctc.web.ContractDrafterChangeCallBackController;
import com.midea.pam.ctc.web.ContractProductChanageCallBackController;
import com.midea.pam.ctc.web.ContractReceiptPlanChanageCallBackController;
import com.midea.pam.ctc.web.ContractTerminationCallbackController;
import com.midea.pam.ctc.web.ContractWorkflowCallbackController;
import com.midea.pam.ctc.web.CostCollectionController;
import com.midea.pam.ctc.web.CustomerTransferController;
import com.midea.pam.ctc.web.CustomerTransferReverseWorkflowCallbackController;
import com.midea.pam.ctc.web.CustomerTransferWorkflowCallBackController;
import com.midea.pam.ctc.web.DeliveryAddressController;
import com.midea.pam.ctc.web.DifferenceShareAccountController;
import com.midea.pam.ctc.web.DifferenceShareController;
import com.midea.pam.ctc.web.DocumentLibraryController;
import com.midea.pam.ctc.web.EamPurchaseInfoController;
import com.midea.pam.ctc.web.ErpCodeRuleController;
import com.midea.pam.ctc.web.EsbResultReturnController;
import com.midea.pam.ctc.web.ExchangeAccountController;
import com.midea.pam.ctc.web.GSCInvoiceController;
import com.midea.pam.ctc.web.GSCInvoiceDetailWorkflowCallbackController;
import com.midea.pam.ctc.web.GSCInvoiceIspWorkflowCallbackController;
import com.midea.pam.ctc.web.GlegalContractChangeController;
import com.midea.pam.ctc.web.GlegalContractWorkflowCallbackController;
import com.midea.pam.ctc.web.GlegalPurchaseContractWorkflowCallbackController;
import com.midea.pam.ctc.web.GscPaymentInvoiceController;
import com.midea.pam.ctc.web.HroRequirementController;
import com.midea.pam.ctc.web.HroWorkingHourBillController;
import com.midea.pam.ctc.web.HroWorkingHourController;
import com.midea.pam.ctc.web.IFlowExceptionReBuildController;
import com.midea.pam.ctc.web.IhrAttendDetailController;
import com.midea.pam.ctc.web.ImportDataController;
import com.midea.pam.ctc.web.InnerSwapApplyController;
import com.midea.pam.ctc.web.InvoiceApplyController;
import com.midea.pam.ctc.web.InvoicePlanController;
import com.midea.pam.ctc.web.InvoiceReceivableController;
import com.midea.pam.ctc.web.InvoiceReceivableReverseCallBackController;
import com.midea.pam.ctc.web.LegalContractController;
import com.midea.pam.ctc.web.MRPController;
import com.midea.pam.ctc.web.MaterialAdjustController;
import com.midea.pam.ctc.web.MaterialAttributeController;
import com.midea.pam.ctc.web.MaterialChangeTypeController;
import com.midea.pam.ctc.web.MaterialCostTransferController;
import com.midea.pam.ctc.web.MaterialCustomDictController;
import com.midea.pam.ctc.web.MaterialDelistController;
import com.midea.pam.ctc.web.MaterialGetController;
import com.midea.pam.ctc.web.MaterialOutsourcingContractConfigController;
import com.midea.pam.ctc.web.MaterialReturnController;
import com.midea.pam.ctc.web.MaterialTransferController;
import com.midea.pam.ctc.web.MilepostDesignMemberController;
import com.midea.pam.ctc.web.MilepostDesignPlanChangeController;
import com.midea.pam.ctc.web.MilepostDesignPlanChangeWorkflowCallbackController;
import com.midea.pam.ctc.web.MilepostDesignPlanController;
import com.midea.pam.ctc.web.MilepostDesignPlanDetailChangeRecordController;
import com.midea.pam.ctc.web.MilepostDesignPlanNotPublishRequirementController;
import com.midea.pam.ctc.web.MilepostTemplateController;
import com.midea.pam.ctc.web.OrganizationCustomDictController;
import com.midea.pam.ctc.web.PaymentApplyController;
import com.midea.pam.ctc.web.PaymentApplyWorkflowCallbackController;
import com.midea.pam.ctc.web.PaymentImportController;
import com.midea.pam.ctc.web.PaymentInvoiceController;
import com.midea.pam.ctc.web.PaymentInvoiceDetailController;
import com.midea.pam.ctc.web.PaymentPlanController;
import com.midea.pam.ctc.web.PaymentRecordController;
import com.midea.pam.ctc.web.PaymentWriteOffRecordController;
import com.midea.pam.ctc.web.PreProjectCallBackController;
import com.midea.pam.ctc.web.ProductTaxSettingController;
import com.midea.pam.ctc.web.ProjectAssetChangeCallBackController;
import com.midea.pam.ctc.web.ProjectAwardController;
import com.midea.pam.ctc.web.ProjectAwardDeductionController;
import com.midea.pam.ctc.web.ProjectBaseInfoBatchChangeCallBackController;
import com.midea.pam.ctc.web.ProjectBaseInfoChangeCallBackController;
import com.midea.pam.ctc.web.ProjectBaselineBatchContractRsController;
import com.midea.pam.ctc.web.ProjectBudgetChangeWorkflowCallbackController;
import com.midea.pam.ctc.web.ProjectBudgetCostController;
import com.midea.pam.ctc.web.ProjectBudgetMaterialController;
import com.midea.pam.ctc.web.ProjectBudgetPushEmsErrorController;
import com.midea.pam.ctc.web.ProjectBudgetTargetChangeCallBackController;
import com.midea.pam.ctc.web.ProjectBusinessController;
import com.midea.pam.ctc.web.ProjectCallBackController;
import com.midea.pam.ctc.web.ProjectCloseMilestoneController;
import com.midea.pam.ctc.web.ProjectContractChangeCallBackController;
import com.midea.pam.ctc.web.ProjectCustomerSatisfactionController;
import com.midea.pam.ctc.web.ProjectDeliveriesController;
import com.midea.pam.ctc.web.ProjectFundsDeployController;
import com.midea.pam.ctc.web.ProjectGanttChartController;
import com.midea.pam.ctc.web.ProjectMilepostChangeCallBackController;
import com.midea.pam.ctc.web.ProjectMilepostChangeHistoryController;
import com.midea.pam.ctc.web.ProjectMilepostController;
import com.midea.pam.ctc.web.ProjectPreviewCallBackController;
import com.midea.pam.ctc.web.ProjectProblemCommentController;
import com.midea.pam.ctc.web.ProjectProblemController;
import com.midea.pam.ctc.web.ProjectProblemOperationRecordController;
import com.midea.pam.ctc.web.ProjectReopenCallBackController;
import com.midea.pam.ctc.web.ProjectReopenController;
import com.midea.pam.ctc.web.ProjectRiskSettingController;
import com.midea.pam.ctc.web.ProjectRoleController;
import com.midea.pam.ctc.web.ProjectSecurityIncidentController;
import com.midea.pam.ctc.web.ProjectTerminationWorkflowCallbackController;
import com.midea.pam.ctc.web.ProjectTransferQualityCallBackController;
import com.midea.pam.ctc.web.ProjectTransferQualityController;
import com.midea.pam.ctc.web.ProjectTypeController;
import com.midea.pam.ctc.web.ProjectWbsBaselineChangeCallBackController;
import com.midea.pam.ctc.web.ProjectWbsReceiptsBudgetController;
import com.midea.pam.ctc.web.ProjectWbsReceiptsController;
import com.midea.pam.ctc.web.ProjectWbsReceiptsRequirementChangeRecordController;
import com.midea.pam.ctc.web.ProjectWbsReceiptsRequirementChangeRecordWorkflowCallbackController;
import com.midea.pam.ctc.web.ProjectWbsReceiptsWorkflowCallBackController;
import com.midea.pam.ctc.web.ProjectWbsSubmitReceiptsWorkflowCallBackController;
import com.midea.pam.ctc.web.PurchaseBpaPriceController;
import com.midea.pam.ctc.web.PurchaseContractBudgetController;
import com.midea.pam.ctc.web.PurchaseContractController;
import com.midea.pam.ctc.web.PurchaseContractDetailChangeCallBackController;
import com.midea.pam.ctc.web.PurchaseContractInfoChangeCallBackController;
import com.midea.pam.ctc.web.PurchaseContractPlanChangeCallBackController;
import com.midea.pam.ctc.web.PurchaseContractProgressWorkflowCallBackController;
import com.midea.pam.ctc.web.PurchaseContractPunishmentConfigController;
import com.midea.pam.ctc.web.PurchaseContractPunishmentController;
import com.midea.pam.ctc.web.PurchaseContractPunishmentWorkflowCallbackController;
import com.midea.pam.ctc.web.PurchaseContractWorkflowCallbackController;
import com.midea.pam.ctc.web.PurchaseMaterialCloseDetailController;
import com.midea.pam.ctc.web.PurchaseMaterialReleaseDetailController;
import com.midea.pam.ctc.web.PurchaseMaterialRequirementController;
import com.midea.pam.ctc.web.PurchaseMaterialRequirementDeliveryAddressHistoryController;
import com.midea.pam.ctc.web.PurchaseOrderCallBackController;
import com.midea.pam.ctc.web.PurchaseOrderChangeCallBackController;
import com.midea.pam.ctc.web.PurchaseOrderChangeController;
import com.midea.pam.ctc.web.PurchaseOrderController;
import com.midea.pam.ctc.web.PurchaseOrderDetailDeliveryAddressHistoryController;
import com.midea.pam.ctc.web.RdmResourcePlanController;
import com.midea.pam.ctc.web.RdmSettlementSheetController;
import com.midea.pam.ctc.web.RdmWorkingHourController;
import com.midea.pam.ctc.web.ReceiptClaimController;
import com.midea.pam.ctc.web.ReceiptPlanController;
import com.midea.pam.ctc.web.ReceiptWorkOrderController;
import com.midea.pam.ctc.web.RefundApplyController;
import com.midea.pam.ctc.web.ResendExecuteController;
import com.midea.pam.ctc.web.RevenueCostOrderCallbackController;
import com.midea.pam.ctc.web.RevenueCostOrderController;
import com.midea.pam.ctc.web.SdpBuyersController;
import com.midea.pam.ctc.web.SdpController;
import com.midea.pam.ctc.web.SdpLogController;
import com.midea.pam.ctc.web.SealAdministratorController;
import com.midea.pam.ctc.web.StandardTermsCallbackController;
import com.midea.pam.ctc.web.StandardTermsController;
import com.midea.pam.ctc.web.SupplierQualityDeactivationController;
import com.midea.pam.ctc.web.SwapExecuteController;
import com.midea.pam.ctc.web.SystemSetController;
import com.midea.pam.ctc.web.TicketTasksController;
import com.midea.pam.ctc.web.TicketWorkingHourImportController;
import com.midea.pam.ctc.web.VendorAslController;
import com.midea.pam.ctc.web.VendorPenaltyConfigController;
import com.midea.pam.ctc.web.VendorPenaltyController;
import com.midea.pam.ctc.web.WorkingHourAccountingController;
import com.midea.pam.ctc.web.WorkingHourController;
import com.midea.pam.ctc.web.WorkingHourCostChangeHeaderController;
import com.midea.pam.ctc.web.WorkingHourDistributeController;
import com.midea.pam.ctc.web.WorkingHourRemindWhiteListController;
import com.midea.pam.ctc.web.WriteOffController;
import com.midea.pam.ctc.web.interceptor.SqlInterceptor;
import com.midea.pam.ctc.web.pass.PassController;
import com.midea.pam.ctc.web.wbs.ProjectActivityController;
import com.midea.pam.ctc.web.wbs.ProjectBaselineBatchController;
import com.midea.pam.ctc.web.wbs.ProjectWbsBaselineController;
import com.midea.pam.ctc.web.wbs.ProjectWbsBudgetBaselineChangeHistoryController;
import com.midea.pam.ctc.web.wbs.ProjectWbsBudgetChangeHistoryController;
import com.midea.pam.ctc.web.wbs.ProjectWbsBudgetChangeWorkflowCallbackController;
import com.midea.pam.ctc.web.wbs.ProjectWbsBudgetController;
import com.midea.pam.ctc.web.wbs.ProjectWbsBudgetSummaryChangeHistoryController;
import com.midea.pam.ctc.web.wbs.ProjectWbsBudgetSummaryController;
import com.midea.pam.ctc.web.wbs.WbsTemplateInfoController;
import com.midea.pam.ctc.web.wbs.WbsTemplateRuleController;
import com.midea.pam.ctc.web.wbs.WbsTemplateRuleDetailController;
import com.midea.pam.support.AuditUserLocalProvider;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.servlet.Filter;

@Configuration
public class CtcWebConfiguration {
    @Bean
    public FilterRegistrationBean setFilter() {
        FilterRegistrationBean filterBean = new FilterRegistrationBean();
        filterBean.setFilter(new AuthorityFilter());
        filterBean.setName("ctcFilter");
        filterBean.addUrlPatterns("/*");
        return filterBean;
    }

    @Bean
    public Filter filter() {
        return new AuthorityFilter();
    }

    @Bean
    public AuditUserLocalProvider auditUserLocalProvider() {
        return new CtcAuditUserLocalProvider();
    }

    @Bean
    public CommonExceptionHandler commonExceptionHandler() {
        return new CommonExceptionHandler();
    }

    @Bean
    public ProjectBusinessController projectBusinessController() {
        return new ProjectBusinessController();
    }

    @Bean
    public ProjectTypeController projectTypeController() {
        return new ProjectTypeController();
    }

    @Bean
    public CheckItemController checkItemController() {
        return new CheckItemController();
    }

    @Bean
    public BudgetItemController budgetItemController() {
        return new BudgetItemController();
    }

    @Bean
    public BusiSceneNonSaleController busiSceneNonSaleController() {
        return new BusiSceneNonSaleController();
    }

    @Bean
    public CnapsInfoController cnapsInfoController() {
        return new CnapsInfoController();
    }

    @Bean
    public CodeRuleController codeRuleController() {
        return new CodeRuleController();
    }

    @Bean
    public WorkingHourController workingHourController() {
        return new WorkingHourController();
    }

    @Bean
    public WorkingHourDistributeController workingHourDistributeController() {
        return new WorkingHourDistributeController();
    }

    @Bean
    public MilepostTemplateController milepostTemplateController() {
        return new MilepostTemplateController();
    }

    @Bean
    public ReceiptClaimController receiptClaimController() {
        return new ReceiptClaimController();
    }

    @Bean
    public ProjectRoleController projectRoleController() {
        return new ProjectRoleController();
    }

    @Bean
    public WorkingHourRemindWhiteListController workingHourRemindWhiteListController() {
        return new WorkingHourRemindWhiteListController();
    }

    @Bean
    public InvoiceReceivableController invoiceReceivableController() {
        return new InvoiceReceivableController();
    }

    @Bean
    public MilepostDesignPlanController milepostDesignPlanController() {
        return new MilepostDesignPlanController();
    }

    @Bean
    public ProjectDeliveriesController projectDeliveriesController() {
        return new ProjectDeliveriesController();
    }

    @Bean
    public ProjectFundsDeployController projectFundsDeployController() {
        return new ProjectFundsDeployController();
    }

    @Bean
    public ProductTaxSettingController productTaxSettingController() {
        return new ProductTaxSettingController();
    }

    @Bean
    public ProjectRiskSettingController projectRiskSettingController() {
        return new ProjectRiskSettingController();
    }

    @Bean
    public ProjectCloseMilestoneController projectCloseMilestoneController() {
        return new ProjectCloseMilestoneController();
    }

    @Bean
    public ProjectCallBackController projectCallBackController() {
        return new ProjectCallBackController();
    }

    @Bean
    public InvoiceApplyController invoiceApplyController() {
        return new InvoiceApplyController();
    }

    @Bean
    public ContractController contractController() {
        return new ContractController();
    }

    @Bean
    public SdpBuyersController ddpBuyersController() {
        return new SdpBuyersController();
    }

    @Bean
    public ContractWorkflowCallbackController contractWorkflowCallbackController() {
        return new ContractWorkflowCallbackController();
    }

    @Bean
    public WriteOffController writeOffController() {
        return new WriteOffController();
    }

    @Bean
    public InvoicePlanController invoicePlanController() {
        return new InvoicePlanController();
    }

    @Bean
    public LegalContractController legalContractController() {
        return new LegalContractController();
    }

    @Bean
    public MaterialGetController materialGetController() {
        return new MaterialGetController();
    }

    @Bean
    public MaterialCostTransferController materialCostTransferController() {
        return new MaterialCostTransferController();
    }

    @Bean
    public MaterialReturnController materialReturnController() {
        return new MaterialReturnController();
    }

    @Bean
    public PurchaseMaterialReleaseDetailController purchaseMaterialReleaseDetailController() {
        return new PurchaseMaterialReleaseDetailController();
    }

    @Bean
    public PurchaseMaterialCloseDetailController purchaseMaterialCloseDetailController() {
        return new PurchaseMaterialCloseDetailController();
    }

    @Bean
    public PurchaseMaterialRequirementController purchaseMaterialRequirementController() {
        return new PurchaseMaterialRequirementController();
    }

    @Bean
    public OrganizationCustomDictController organizationCustomDictController() {
        return new OrganizationCustomDictController();
    }

    @Bean
    public MaterialCustomDictController materialCustomDictController() {
        return new MaterialCustomDictController();
    }

    @Bean
    public PurchaseOrderController purchaseOrderController() {
        return new PurchaseOrderController();
    }

    @Bean
    public PurchaseOrderChangeController purchaseOrderChangeController() {
        return new PurchaseOrderChangeController();
    }

    @Bean
    public RdmResourcePlanController rdmResourcePlanController() {
        return new RdmResourcePlanController();
    }

    @Bean
    public EamPurchaseInfoController eamPurchaseInfoController() {
        return new EamPurchaseInfoController();
    }

    @Bean
    public RdmSettlementSheetController rdmSettlementSheetController() {
        return new RdmSettlementSheetController();
    }

    @Bean
    public RdmWorkingHourController rdmWorkingHourController() {
        return new RdmWorkingHourController();
    }

    @Bean
    public VendorAslController vendorAslController() {
        return new VendorAslController();
    }

    @Bean
    public MaterialTransferController materialTransferController() {
        return new MaterialTransferController();
    }

    @Bean
    public PurchaseContractController purchaseContractController() {
        return new PurchaseContractController();
    }

    @Bean
    public ProjectBaseInfoChangeCallBackController projectBaseInfoChangeCallBackController() {
        return new ProjectBaseInfoChangeCallBackController();
    }

    @Bean
    public ProjectBudgetTargetChangeCallBackController projectTargetCostChangeCallBackController() {
        return new ProjectBudgetTargetChangeCallBackController();
    }

    @Bean
    public ContractReceiptPlanChanageCallBackController contractReceiptPlanChanageCallBackController() {
        return new ContractReceiptPlanChanageCallBackController();
    }

    @Bean
    public ContractProductChanageCallBackController contractProductChanageCallBackController() {
        return new ContractProductChanageCallBackController();
    }

    @Bean
    public ProjectBudgetCostController projectBudgetCostController() {
        return new ProjectBudgetCostController();
    }

    @Bean
    public ProjectMilepostChangeCallBackController projectMilepostChangeCallBackController() {
        return new ProjectMilepostChangeCallBackController();
    }

    @Bean
    public ProjectPreviewCallBackController projectPreviewCallBackController() {
        return new ProjectPreviewCallBackController();
    }

    @Bean
    public CostCollectionController costCollectionController() {
        return new CostCollectionController();
    }

    @Bean
    public CarryoverBillController carryoverBillController() {
        return new CarryoverBillController();
    }

    @Bean
    public PurchaseContractWorkflowCallbackController purchaseContractWorkflowCallbackController() {
        return new PurchaseContractWorkflowCallbackController();
    }

    @Bean
    public PurchaseContractInfoChangeCallBackController purchaseContractInfoChangeCallBackController() {
        return new PurchaseContractInfoChangeCallBackController();
    }

    @Bean
    public PurchaseContractDetailChangeCallBackController purchaseContractDetailChangeCallBackController() {
        return new PurchaseContractDetailChangeCallBackController();
    }

    @Bean
    public PurchaseContractPlanChangeCallBackController purchaseContractPlanChangeCallBackController() {
        return new PurchaseContractPlanChangeCallBackController();
    }

    @Bean
    public PurchaseBpaPriceController purchaseBpaPriceController() {
        return new PurchaseBpaPriceController();
    }

    @Bean
    public CarryoverBillAccountingController carryoverBillAccountingController() {
        return new CarryoverBillAccountingController();
    }

    @Bean
    public MilepostDesignPlanChangeController milepostDesignPlanChangeController() {
        return new MilepostDesignPlanChangeController();
    }

    @Bean
    public RevenueCostOrderController revenueCostOrderController() {
        return new RevenueCostOrderController();
    }

    @Bean
    public ProjectBudgetChangeWorkflowCallbackController projectBudgetChangeWorkflowCallbackController() {
        return new ProjectBudgetChangeWorkflowCallbackController();
    }

    @Bean
    public VendorPenaltyConfigController vendorPenaltyConfigController() {
        return new VendorPenaltyConfigController();
    }

    @Bean
    public VendorPenaltyController vendorPenaltyController() {
        return new VendorPenaltyController();
    }

    @Bean
    public WorkingHourAccountingController workingHourAccountingController() {
        return new WorkingHourAccountingController();
    }

    @Bean
    public WorkingHourCostChangeHeaderController workingHourCostChangeHeaderController() {
        return new WorkingHourCostChangeHeaderController();
    }

    @Bean
    public RevenueCostOrderCallbackController revenueCostOrderCallbackController() {
        return new RevenueCostOrderCallbackController();
    }

    @Bean
    public AsyncRequestResultController asyncRequestResultController() {
        return new AsyncRequestResultController();
    }

    @Bean
    public MaterialOutsourcingContractConfigController materialOutsourcingContractConfigController() {
        return new MaterialOutsourcingContractConfigController();
    }

    @Bean
    public PaymentPlanController paymentPlanController() {
        return new PaymentPlanController();
    }

    @Bean
    public PaymentRecordController paymentRecordController() {
        return new PaymentRecordController();
    }

    @Bean
    public PaymentWriteOffRecordController paymentWriteOffRecordController() {
        return new PaymentWriteOffRecordController();
    }

    @Bean
    public PaymentApplyController paymentApplyController() {
        return new PaymentApplyController();
    }

    @Bean
    public PaymentApplyWorkflowCallbackController paymentApplyWorkflowCallbackController() {
        return new PaymentApplyWorkflowCallbackController();
    }

    @Bean
    public PaymentInvoiceDetailController paymentInvoiceDetailController() {
        return new PaymentInvoiceDetailController();
    }

    @Bean
    public PaymentInvoiceController paymentInvoiceController() {
        return new PaymentInvoiceController();
    }

    @Bean
    public ResendExecuteController resendExecuteController() {
        return new ResendExecuteController();
    }

    @Bean
    public ErpCodeRuleController erpCodeRuleController() {
        return new ErpCodeRuleController();
    }

    @Bean
    public EsbResultReturnController esbResultReturnController() {
        return new EsbResultReturnController();
    }

    @Bean
    public IhrAttendDetailController ihrAttendDetailController() {
        return new IhrAttendDetailController();
    }

    @Bean
    public PreProjectCallBackController preProjectCallBackController() {
        return new PreProjectCallBackController();
    }

    @Bean
    public DifferenceShareAccountController differenceShareAccountController() {
        return new DifferenceShareAccountController();
    }

    @Bean
    public DifferenceShareController differenceShareController() {
        return new DifferenceShareController();
    }

    @Bean
    public SystemSetController systemSetController() {
        return new SystemSetController();
    }

    @Bean
    public ProjectTerminationWorkflowCallbackController projectTerminationWorkflowCallbackController() {
        return new ProjectTerminationWorkflowCallbackController();
    }

    @Bean
    public SqlInterceptor SqlInterceptor() {
        return new SqlInterceptor();
    }

    @Bean
    public SqlLogCollectionUtils sqlLogCollectionUtils() {
        return new SqlLogCollectionUtils();
    }

    @Bean
    public SqlLogCollection sqlLogCollection() {
        return new SqlLogCollection();
    }

    @Bean
    public SqlLogSignalQueue sqlLogSignalQueue() {
        return new SqlLogSignalQueue();
    }

    @Bean
    public RefundApplyController refundApplyController() {
        return new RefundApplyController();
    }

    @Bean
    public ReceiptPlanController receiptPlanController() {
        return new ReceiptPlanController();
    }

    @Bean
    public ApplyContractUnitController applyContractUnitController() {
        return new ApplyContractUnitController();
    }

    @Bean
    public SealAdministratorController sealAdministratorController() {
        return new SealAdministratorController();
    }

    @Bean
    public GlegalContractWorkflowCallbackController glegalContractWorkflowCallbackController() {
        return new GlegalContractWorkflowCallbackController();
    }

    @Bean
    public GlegalPurchaseContractWorkflowCallbackController glegalPurchaseContractWorkflowCallbackController() {
        return new GlegalPurchaseContractWorkflowCallbackController();
    }

    @Bean
    public HroRequirementController hroRequirementController() {
        return new HroRequirementController();
    }

    @Bean
    public HroWorkingHourController hroWorkingHourController() {
        return new HroWorkingHourController();
    }

    @Bean
    public HroWorkingHourBillController hroWorkingHourBillController() {
        return new HroWorkingHourBillController();
    }

    @Bean
    public ProjectBaseInfoBatchChangeCallBackController projectBaseInfoBatchChangeCallBackController() {
        return new ProjectBaseInfoBatchChangeCallBackController();
    }

    @Bean
    public ProjectAwardDeductionController projectAwardDeductionController() {
        return new ProjectAwardDeductionController();
    }

    @Bean
    public ProjectAwardController projectAwardController() {
        return new ProjectAwardController();
    }

    @Bean
    public ProjectGanttChartController projectGanttChartController() {
        return new ProjectGanttChartController();
    }

    @Bean
    public ProjectBudgetPushEmsErrorController projectBudgetPushEmsErrorController() {
        return new ProjectBudgetPushEmsErrorController();
    }

    @Bean
    public ApplicationIndustryController applicationIndustryController() {
        return new ApplicationIndustryController();
    }

    @Bean
    public InnerSwapApplyController innerSwapApplyController() {
        return new InnerSwapApplyController();
    }

    @Bean
    public SwapExecuteController swapExecuteController() {
        return new SwapExecuteController();
    }

    @Bean
    public CommonPushController commonPushController() {
        return new CommonPushController();
    }

    @Bean
    public MaterialAttributeController materialAttributeController() {
        return new MaterialAttributeController();
    }

    @Bean
    public MaterialAdjustController materialAdjustController() {
        return new MaterialAdjustController();
    }

    @Bean
    public MilepostDesignMemberController milepostDesignMemberController() {
        return new MilepostDesignMemberController();
    }

    @Bean
    public TicketTasksController ticketTasksController() {
        return new TicketTasksController();
    }

    @Bean
    public TicketWorkingHourImportController ticketWorkingHourImportController() {
        return new TicketWorkingHourImportController();
    }

    @Bean
    public MaterialChangeTypeController materialChangeTypeController() {
        return new MaterialChangeTypeController();
    }

    @Bean
    public CloudPrintController cloudPrintController() {
        return new CloudPrintController();
    }

    @Bean
    public MRPController mRPController() {
        return new MRPController();
    }

    @Bean
    public PurchaseContractBudgetController purchaseContractBudgetController() {
        return new PurchaseContractBudgetController();
    }

    @Bean
    public ProjectProblemController projectProblemController() {
        return new ProjectProblemController();
    }

    @Bean
    public DocumentLibraryController documentLibraryController() {
        return new DocumentLibraryController();
    }

    @Bean
    public ProjectProblemCommentController projectProblemCommentController() {
        return new ProjectProblemCommentController();
    }

    @Bean
    public ProjectProblemOperationRecordController projectProblemOperationRecordController() {
        return new ProjectProblemOperationRecordController();
    }

    @Bean
    public PaymentImportController paymentImportController() {
        return new PaymentImportController();
    }

    @Bean
    public CollectionConfigurationController collectionConfigurationController() {
        return new CollectionConfigurationController();
    }

    @Bean
    public ContractClassificationController contractClassificationController() {
        return new ContractClassificationController();
    }

    @Bean
    public ProjectTransferQualityController projectTransferQualityController() {
        return new ProjectTransferQualityController();
    }

    @Bean
    public ProjectBudgetMaterialController projectBudgetMaterialController() {
        return new ProjectBudgetMaterialController();
    }

    @Bean
    public ProjectTransferQualityCallBackController projectTransferQualityCallBackController() {
        return new ProjectTransferQualityCallBackController();
    }

    @Bean
    public ProjectReopenController projectReopenController() {
        return new ProjectReopenController();
    }

    @Bean
    public ProjectReopenCallBackController projectReopenCallBackController() {
        return new ProjectReopenCallBackController();
    }

    @Bean
    public WbsTemplateRuleDetailController wbsTemplateRuleDetailController() {
        return new WbsTemplateRuleDetailController();
    }

    @Bean
    public WbsTemplateInfoController wbsTemplateInfoController() {
        return new WbsTemplateInfoController();
    }

    @Bean
    public ProjectActivityController projectActivityController() {
        return new ProjectActivityController();
    }

    @Bean
    public ProjectWbsBudgetController projectWbsBudgetController() {
        return new ProjectWbsBudgetController();
    }

    @Bean
    public WbsTemplateRuleController wbsTemplateRuleController() {
        return new WbsTemplateRuleController();
    }

    @Bean
    public ProjectWbsBudgetSummaryController projectWbsBudgetSummaryController() {
        return new ProjectWbsBudgetSummaryController();
    }

    @Bean
    public ProjectWbsBudgetBaselineChangeHistoryController projectWbsBudgetBaselineChangeHistoryController() {
        return new ProjectWbsBudgetBaselineChangeHistoryController();
    }

    @Bean
    public ProjectWbsReceiptsController projectWbsReceiptsController() {
        return new ProjectWbsReceiptsController();
    }

    @Bean
    public ProjectWbsReceiptsBudgetController projectWbsReceiptsBudgetController() {
        return new ProjectWbsReceiptsBudgetController();
    }

    @Bean
    public ProjectWbsReceiptsWorkflowCallBackController projectWbsReceiptsWorkflowCallBackController() {
        return new ProjectWbsReceiptsWorkflowCallBackController();
    }

    @Bean
    public BasicCacheController basicCacheController() {
        return new BasicCacheController();
    }

    @Bean
    public PurchaseContractProgressWorkflowCallBackController purchaseContractProgressWorkflowCallBackController() {
        return new PurchaseContractProgressWorkflowCallBackController();
    }

    @Bean
    public ProjectWbsSubmitReceiptsWorkflowCallBackController projectWbsSubmitReceiptsWorkflowCallBackController() {
        return new ProjectWbsSubmitReceiptsWorkflowCallBackController();
    }

    @Bean
    public MilepostDesignPlanChangeWorkflowCallbackController milepostDesignPlanChangeWorkflowCallbackController() {
        return new MilepostDesignPlanChangeWorkflowCallbackController();
    }

    @Bean
    public ProjectWbsBudgetChangeWorkflowCallbackController projectWbsBudgetChangeWorkflowCallbackController() {
        return new ProjectWbsBudgetChangeWorkflowCallbackController();
    }

    @Bean
    public PurchaseOrderCallBackController purchaseOrderCallBackController() {
        return new PurchaseOrderCallBackController();
    }

    @Bean
    public PurchaseOrderChangeCallBackController purchaseOrderChangeCallBackController() {
        return new PurchaseOrderChangeCallBackController();
    }

    @Bean
    public ProjectWbsBaselineController projectWbsBaselineController() {
        return new ProjectWbsBaselineController();
    }

    @Bean
    public ProjectWbsBudgetChangeHistoryController projectWbsBudgetChangeHistoryController() {
        return new ProjectWbsBudgetChangeHistoryController();
    }

    @Bean
    public ProjectWbsBudgetSummaryChangeHistoryController projectWbsBudgetSummaryChangeHistoryController() {
        return new ProjectWbsBudgetSummaryChangeHistoryController();
    }

    @Bean
    public CustomerTransferController customerTransferController() {
        return new CustomerTransferController();
    }

    @Bean
    public ProjectBaselineBatchController projectBaselineBatchController() {
        return new ProjectBaselineBatchController();
    }

    @Bean
    public CustomerTransferWorkflowCallBackController customerTransferWorkflowCallBackController() {
        return new CustomerTransferWorkflowCallBackController();
    }

    @Bean
    public CustomerTransferReverseWorkflowCallbackController customerTransferReverseWorkflowCallbackController() {
        return new CustomerTransferReverseWorkflowCallbackController();
    }

    @Bean
    public ProjectMilepostController projectMilepostController() {
        return new ProjectMilepostController();
    }

    @Bean
    public ProjectMilepostChangeHistoryController projectMilepostChangeHistoryController() {
        return new ProjectMilepostChangeHistoryController();
    }

    @Bean
    public SupplierQualityDeactivationController supplierQualityDeactivationController() {
        return new SupplierQualityDeactivationController();
    }

    @Bean
    public ProjectContractChangeCallBackController projectContractChangeCallBackController() {
        return new ProjectContractChangeCallBackController();
    }

    @Bean
    public ProjectBaselineBatchContractRsController projectBaselineBatchContractRsController() {
        return new ProjectBaselineBatchContractRsController();
    }

    @Bean
    public ImportDataController importDataController() {
        return new ImportDataController();
    }

    @Bean
    public ProjectCustomerSatisfactionController projectCustomerSatisfactionController() {
        return new ProjectCustomerSatisfactionController();
    }

    @Bean
    public ProjectSecurityIncidentController projectSecurityIncidentController() {
        return new ProjectSecurityIncidentController();
    }

    @Bean
    public MaterialDelistController materialDelistController() {
        return new MaterialDelistController();
    }

    @Bean
    public MilepostDesignPlanNotPublishRequirementController milepostDesignPlanNotPublishRequirementController() {
        return new MilepostDesignPlanNotPublishRequirementController();
    }

    @Bean
    public ReceiptWorkOrderController receiptWorkOrderController() {
        return new ReceiptWorkOrderController();
    }

    @Bean
    public MilepostDesignPlanDetailChangeRecordController milepostDesignPlanDetailChangeRecordController() {
        return new MilepostDesignPlanDetailChangeRecordController();
    }

    @Bean
    public ProjectWbsReceiptsRequirementChangeRecordController projectWbsReceiptsRequirementChangeRecordController() {
        return new ProjectWbsReceiptsRequirementChangeRecordController();
    }

    @Bean
    public ProjectWbsReceiptsRequirementChangeRecordWorkflowCallbackController projectWbsReceiptsRequirementChangeRecordWorkflowCallbackController() {
        return new ProjectWbsReceiptsRequirementChangeRecordWorkflowCallbackController();
    }

    @Bean
    public SdpController sdpController() {
        return new SdpController();
    }

    @Bean
    public CheckConfigController checkConfigController() {
        return new CheckConfigController();
    }

    @Bean
    public CheckResultController checkResultController() {
        return new CheckResultController();
    }

    @Bean
    public ContractTerminationCallbackController contractTerminationCallbackController() {
        return new ContractTerminationCallbackController();
    }

    @Bean
    public PurchaseContractPunishmentController purchaseContractPunishmentController() {
        return new PurchaseContractPunishmentController();
    }

    @Bean
    public PurchaseContractPunishmentConfigController purchaseContractPunishmentConfigController() {
        return new PurchaseContractPunishmentConfigController();
    }

    @Bean
    public PurchaseContractPunishmentWorkflowCallbackController purchaseContractPunishmentWorkflowCallbackController() {
        return new PurchaseContractPunishmentWorkflowCallbackController();
    }


    @Bean
    public GlegalContractChangeController glegalContractChangeController() {
        return new GlegalContractChangeController();
    }

    @Bean
    public ContractDrafterChangeCallBackController contractDrafterChangeCallBackController() {
        return new ContractDrafterChangeCallBackController();
    }

    @Bean
    public ExchangeAccountController exchangeAccountController() {
        return new ExchangeAccountController();
    }

    @Bean
    public ProjectAssetChangeCallBackController projectAssetChangeCallBackController() {
        return new ProjectAssetChangeCallBackController();
    }

    @Bean
    public AssetDeprnAccountingController assetDeprnAccountingController() {
        return new AssetDeprnAccountingController();
    }

    @Bean
    public ProjectWbsBaselineChangeCallBackController projectWbsBaselineChangeCallBackController() {
        return new ProjectWbsBaselineChangeCallBackController();
    }


    @Bean
    public SdpLogController sdpLogController() {
        return new SdpLogController();
    }

    @Bean
    public GSCInvoiceIspWorkflowCallbackController gSCInvoiceIspWorkflowCallbackController() {
        return new GSCInvoiceIspWorkflowCallbackController();
    }

    @Bean
    public GSCInvoiceController gSCInvoiceIspController() {
        return new GSCInvoiceController();
    }

    @Bean
    public GSCInvoiceDetailWorkflowCallbackController gSCInvoiceDetailWorkflowCallbackController() {
        return new GSCInvoiceDetailWorkflowCallbackController();
    }

    @Bean
    public InvoiceReceivableReverseCallBackController invoiceReceivableReverseCallBackController() {
        return new InvoiceReceivableReverseCallBackController();
    }

    @Bean
    public DeliveryAddressController deliveryAddressController(){
        return new DeliveryAddressController();
    }

    @Bean
    public PurchaseMaterialRequirementDeliveryAddressHistoryController purchaseMaterialRequirementDeliveryAddressHistoryController(){
        return new PurchaseMaterialRequirementDeliveryAddressHistoryController();
    }

    @Bean
    public PurchaseOrderDetailDeliveryAddressHistoryController purchaseOrderDetailDeliveryAddressHistoryController(){
      return   new PurchaseOrderDetailDeliveryAddressHistoryController();
    }

    @Bean
    public GscPaymentInvoiceController gscPaymentInvoiceController() {
        return new GscPaymentInvoiceController();
    }

    @Bean
    public IFlowExceptionReBuildController iFlowExceptionReBuildController() {
        return new IFlowExceptionReBuildController();
    }

    @Bean
    public PassController passController() {
        return new PassController();
    }


    @Bean
    public StandardTermsController standardTermsController() {
        return new StandardTermsController();
    }

    @Bean
    public StandardTermsCallbackController standardTermsCallbackController() {
        return new StandardTermsCallbackController();
    }
}
