package com.midea.pam.ctc.web.wbs;


import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.ProjectActivityDto;
import com.midea.pam.common.ctc.excelVo.ProjectActivityExcelVo;
import com.midea.pam.common.ctc.vo.FeeItemFeeSettingModeDropdownBoxVO;
import com.midea.pam.common.enums.ProjectActivityBudgetMattersStateESnums;
import com.midea.pam.common.enums.ProjectActivityParentStateEnums;
import com.midea.pam.ctc.wbs.service.ProjectActivityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Api("项目活动")
@RestController
@RequestMapping("projectActivity")
public class ProjectActivityController {

    @Resource
    private ProjectActivityService projectActivityService;

    @ApiOperation(value = "查询项目活动", notes = "此方法已过滤System")
    @GetMapping("findList")
    public Response findList(@ApiParam("编码") @RequestParam(required = false) String code,
                             @ApiParam("类别名称") @RequestParam(required = false) String name,
                             @ApiParam("类别描述") @RequestParam(required = false) String description,
                             @ApiParam("备注") @RequestParam(required = false) String remark,
                             @ApiParam("预算事项") @RequestParam(required = false) String budgetMattersStateStr,
                             @ApiParam("类别属性") @RequestParam(required = false) String typeStr,
                             @ApiParam("是否父级") @RequestParam(required = false) String parentStateStr,
                             @ApiParam("层级") @RequestParam(required = false) String level) {
        ProjectActivityDto param = new ProjectActivityDto();
        if (StringUtils.isNotBlank(code)) {
            param.setCode(code);
        }
        if (StringUtils.isNotBlank(name)) {
            param.setName(name);
        }
        if (StringUtils.isNotBlank(description)) {
            param.setDescription(description);
        }
        if (StringUtils.isNotBlank(remark)) {
            param.setRemark(remark);
        }
        if (StringUtils.isNotBlank(budgetMattersStateStr)) {
            List<Boolean> budgetMattersStateList = Arrays.stream(budgetMattersStateStr.split(","))
                    .map(a -> ProjectActivityBudgetMattersStateESnums.YES.getCode().equals(a) ? true : false)
                    .collect(Collectors.toList());
            param.setBudgetMattersStateList(budgetMattersStateList);
        }
        if (StringUtils.isNotBlank(typeStr)) {
            List<String> typeList = Arrays.asList(typeStr.split(","));
            param.setTypeList(typeList);
        }
        if (StringUtils.isNotBlank(parentStateStr)) {
            List<Boolean> parentStateList = Arrays.stream(parentStateStr.split(","))
                    .map(a -> ProjectActivityParentStateEnums.YES.getCode().equals(a) ? true : false)
                    .collect(Collectors.toList());
            param.setParentStateList(parentStateList);
        }
        if (!Objects.isNull(level)) {
            param.setLevel(level);
        }
        DataResponse<List<ProjectActivityDto>> response = Response.dataResponse();
        return response.setData(projectActivityService.findList(param));
    }

    @ApiOperation(value = "查询层级")
    @GetMapping("findLevelList")
    public Response findLevelList(@ApiParam("组织id") @RequestParam Long unitId){
        DataResponse<List<String>> response = Response.dataResponse();
        return response.setData(projectActivityService.findLevelList(unitId));
    }

    @ApiOperation(value = "保存项目活动")
    @PostMapping("save")
    public Response save(@RequestBody List<ProjectActivityDto> projectActivityDtoList) {
        DataResponse<Boolean> response = Response.dataResponse();
        return response.setData(projectActivityService.save(projectActivityDtoList));
    }

    @ApiOperation(value = "导出项目活动")
    @GetMapping("export")
    public Response export() {
        DataResponse<List<ProjectActivityExcelVo>> response = Response.dataResponse();
        return response.setData(projectActivityService.export());
    }

    @ApiOperation(value = "增量导入项目活动")
    @PostMapping("importData")
    public Response importData(@RequestBody List<ProjectActivityExcelVo> excelVOList) {
        DataResponse<Boolean> response = Response.dataResponse();
        return response.setData(projectActivityService.importData(excelVOList));
    }

    @ApiOperation(value = "费用类型下拉查询（activity方式）")
    @PostMapping("feeItemJoinQuery")
    public Response feeItemJoinQuery(@RequestBody(required = false) FeeItemFeeSettingModeDropdownBoxVO param) {
        if(null == param){
            param = new FeeItemFeeSettingModeDropdownBoxVO();
        }
        DataResponse<List<FeeItemFeeSettingModeDropdownBoxVO>> response = Response.dataResponse();
        List<FeeItemFeeSettingModeDropdownBoxVO> list = projectActivityService.feeItemJoinQuery(param);
        return response.setData(list);
    }

    @ApiOperation(value = "根据编码查询")
    @GetMapping("findByCode")
    public Response findByCode(@RequestParam(required = false) String code,
                               @RequestParam(required = false) String type,
                               @RequestParam(required = false) Long unitId,
                               @RequestParam(required = false,defaultValue = "false") Boolean isBudget){
        return Response.dataResponse(projectActivityService.findByCode(code,type,unitId,isBudget));
    }

    @ApiOperation(value = "根据编码批量查询")
    @PostMapping("listByCodes")
    public Response listByCodes(@RequestBody List<String> codes, @RequestParam Long unitId){
        return Response.dataResponse(projectActivityService.listByCodes(codes,unitId));
    }
}
