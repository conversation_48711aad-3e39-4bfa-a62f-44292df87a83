<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.ProjectWbsBudgetExtMapper">

    <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.dto.ProjectWbsBudgetDto">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="project_id" jdbcType="BIGINT" property="projectId" />
        <result column="project_code" jdbcType="VARCHAR" property="projectCode" />
        <result column="description" jdbcType="VARCHAR" property="description" />
        <result column="activity_order_no" jdbcType="VARCHAR" property="activityOrderNo" />
        <result column="activity_code" jdbcType="VARCHAR" property="activityCode" />
        <result column="activity_name" jdbcType="VARCHAR" property="activityName" />
        <result column="activity_type" jdbcType="VARCHAR" property="activityType" />
        <result column="price" jdbcType="DECIMAL" property="price" />
        <result column="baseline_cost" jdbcType="DECIMAL" property="baselineCost" />
        <result column="demand_cost" jdbcType="DECIMAL" property="demandCost" />
        <result column="on_the_way_cost" jdbcType="DECIMAL" property="onTheWayCost" />
        <result column="incurred_cost" jdbcType="DECIMAL" property="incurredCost" />
        <result column="remaining_cost" jdbcType="DECIMAL" property="remainingCost" />
        <result column="change_accumulate_cost" jdbcType="DECIMAL" property="changeAccumulateCost" />
        <result column="parent_wbs_id" jdbcType="BIGINT" property="parentWbsId" />
        <result column="parent_activity_id" jdbcType="BIGINT" property="parentActivityId" />
        <result column="create_by" jdbcType="BIGINT" property="createBy" />
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
        <result column="update_by" jdbcType="BIGINT" property="updateBy" />
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
        <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
        <result column="version" jdbcType="BIGINT" property="version" />
        <result column="wbs_full_code" jdbcType="VARCHAR" property="wbsFullCode" />
        <result column="wbs_last_code" jdbcType="VARCHAR" property="wbsLastCode" />
        <result column="dynamic_wbs_template_rule_ids" jdbcType="VARCHAR" property="dynamicWbsTemplateRuleIds" />
        <result column="dynamic_fields" jdbcType="VARCHAR" property="dynamicFields" />
        <result column="dynamic_values" jdbcType="VARCHAR" property="dynamicValues" />
        <result column="fee_type_id" jdbcType="BIGINT" property="feeTypeId" />
        <result column="fee_type_name" jdbcType="VARCHAR" property="feeTypeName" />
        <result column="fee_sync_ems" jdbcType="TINYINT" property="feeSyncEms" />
    </resultMap>

    <sql id="Base_Column_List">
        t1.id,
        t1.project_id,
        t1.project_code,
        t1.description,
        t1.activity_order_no,
        t1.activity_code,
        t1.activity_name,
        t1.activity_type,
        t1.price,
        t1.baseline_cost,
        t1.demand_cost,
        t1.on_the_way_cost,
        t1.incurred_cost,
        t1.remaining_cost,
        t1.change_accumulate_cost,
        t1.parent_wbs_id,
        t1.parent_activity_id,
        t1.create_by,
        t1.create_at,
        t1.update_by,
        t1.update_at,
        t1.deleted_flag,
        t1.version,
        t1.wbs_full_code,
        t1.wbs_last_code,
        t1.dynamic_wbs_template_rule_ids,
        t1.dynamic_fields,
        t1.dynamic_values,
        t1.fee_type_id,
        t1.fee_type_name,
        t1.fee_sync_ems
    </sql>

    <!-- 查询预算汇总列表 -->
    <select id="listByParam" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
            pam_ctc.project_wbs_budget t1
        WHERE t1.deleted_flag = 0
        <if test="projectId != null and projectId != ''">
            AND t1.project_id = #{projectId}
        </if>
        <if test="description != null and description != ''">
            AND t1.description LIKE concat('%', #{description, jdbcType=VARCHAR}, '%')
        </if>
        <if test="activityCode != null and activityCode != ''">
            AND t1.activity_code = #{activityCode}
        </if>
        <if test="activityType != null and activityType != ''">
            AND t1.activity_type = #{activityType}
        </if>
        <if test="activityName != null and activityName != ''">
            AND t1.activity_name LIKE concat('%', #{activityName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="minRemainingCost != null">
            AND t1.remaining_cost &gt;= #{minRemainingCost}
        </if>
        <if test="maxRemainingCost != null">
            AND t1.remaining_cost &lt;= #{maxRemainingCost}
        </if>
        <if test="idList != null and idList.size > 0">
            AND t1.id IN
            <foreach collection="idList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="parentWbsIdList != null and parentWbsIdList.size > 0">
            AND t1.parent_wbs_id IN
            <foreach collection="parentWbsIdList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="parentActivityIdList != null and parentActivityIdList.size > 0">
            AND t1.parent_activity_id IN
            <foreach collection="parentActivityIdList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dynamicFieldList != null and dynamicFieldList.size > 0">
            <foreach collection="dynamicFieldList" item="item" index="index">
                AND t1.id IN (
                    SELECT project_wbs_budget_id
                    FROM pam_ctc.project_wbs_budget_dynamic
                    WHERE wbs_template_rule_detail_code = #{item.value}
                    AND field_name = #{item.key}
                    <if test="projectId != null and projectId != ''">
                        AND project_id = #{projectId}
                    </if>
                )
            </foreach>
        </if>
        ORDER BY t1.id desc
    </select>

    <!-- 获取项目id -->
    <select id="listDistinctProjectId" resultType="long">
        SELECT
            DISTINCT t.project_id
        FROM
            pam_ctc.project_wbs_budget t
        WHERE t.deleted_flag = 0
    </select>
    
    <select id="selectByProjectCodeAndWbsFullCode" resultType="com.midea.pam.common.ctc.dto.ProjectWbsBudgetDto">
        select project_code as projectCode,
               wbs_full_code as wbsFullCode
        from project_wbs_budget
        where concat(project_code,'-',wbs_full_code) in
        <foreach collection="joinCodes" open="(" item="joinCode" close=")" separator=",">
            #{joinCode}
        </foreach>
        and (deleted_flag = 0 or deleted_flag is null)
    </select>

    <select id="selectByWbsCode" resultType="com.midea.pam.common.ctc.dto.ProjectWbsBudgetDto">
        select
            w.project_code projectCode,
            w.wbs_full_code wbsFullCode,
            w.activity_code activityCode,
            p.wbs_enabled wbsEnabled
        from project_wbs_budget w
        inner join project p on p.id = w.project_id
        where p.ou_id = #{ouId}
        and concat(w.project_code,'-',w.wbs_full_code) in
        <foreach collection="codes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
        and w.deleted_flag = 0
    </select>

    <!-- 根据projectId删除记录 -->
    <delete id="deleteByProjectId" parameterType="java.lang.Long">
        delete from project_wbs_budget
        where project_id = #{projectId,jdbcType=BIGINT}
    </delete>

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="com.midea.pam.common.ctc.entity.ProjectWbsBudget">
        insert into project_wbs_budget
            (id, project_id, project_code,
            description, activity_order_no, activity_code,
            activity_name, activity_type, wbs_full_code,
            wbs_summary_code, wbs_last_code, dynamic_wbs_template_rule_ids,
            dynamic_fields, dynamic_values, price,
            baseline_cost, demand_cost, on_the_way_cost,
            incurred_cost, remaining_cost, change_accumulate_cost,
            parent_wbs_id, parent_activity_id, create_by,
            create_at, update_by, update_at,
            deleted_flag, version, fee_type_id,
            fee_type_name, fee_sync_ems)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.projectId,jdbcType=BIGINT}, #{item.projectCode,jdbcType=VARCHAR},
            #{item.description,jdbcType=VARCHAR}, #{item.activityOrderNo,jdbcType=VARCHAR}, #{item.activityCode,jdbcType=VARCHAR},
            #{item.activityName,jdbcType=VARCHAR}, #{item.activityType,jdbcType=VARCHAR}, #{item.wbsFullCode,jdbcType=VARCHAR},
            #{item.wbsSummaryCode,jdbcType=VARCHAR}, #{item.wbsLastCode,jdbcType=VARCHAR}, #{item.dynamicWbsTemplateRuleIds,jdbcType=VARCHAR},
            #{item.dynamicFields,jdbcType=VARCHAR}, #{item.dynamicValues,jdbcType=VARCHAR}, #{item.price,jdbcType=DECIMAL},
            #{item.baselineCost,jdbcType=DECIMAL}, #{item.demandCost,jdbcType=DECIMAL}, #{item.onTheWayCost,jdbcType=DECIMAL},
            #{item.incurredCost,jdbcType=DECIMAL}, #{item.remainingCost,jdbcType=DECIMAL}, #{item.changeAccumulateCost,jdbcType=DECIMAL},
            #{item.parentWbsId,jdbcType=BIGINT}, #{item.parentActivityId,jdbcType=BIGINT}, #{item.createBy,jdbcType=BIGINT},
            #{item.createAt,jdbcType=TIMESTAMP}, #{item.updateBy,jdbcType=BIGINT}, #{item.updateAt,jdbcType=TIMESTAMP},
            #{item.deletedFlag,jdbcType=TINYINT}, #{item.version,jdbcType=BIGINT}, #{item.feeTypeId,jdbcType=BIGINT},
            #{item.feeTypeName,jdbcType=VARCHAR}, #{item.feeSyncEms,jdbcType=TINYINT})
        </foreach>
    </insert>

    <!-- 批量更新 -->
    <update id="batchUpdate" parameterType="java.util.List">
        update pam_ctc.project_wbs_budget
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="parent_activity_id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.parentActivityId !=null">
                        when id=#{item.id} then #{item.parentActivityId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="parent_wbs_id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.parentWbsId !=null">
                        when id=#{item.id} then #{item.parentWbsId}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
</mapper>