<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.WorkingHourAccountingMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.entity.WorkingHourAccounting">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="ou_id" jdbcType="BIGINT" property="ouId" />
    <result column="ou_name" jdbcType="VARCHAR" property="ouName" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="total_working_hour" jdbcType="DECIMAL" property="totalWorkingHour" />
    <result column="total_labor_cost" jdbcType="DECIMAL" property="totalLaborCost" />
    <result column="gl_period" jdbcType="VARCHAR" property="glPeriod" />
    <result column="gl_date" jdbcType="TIMESTAMP" property="glDate" />
    <result column="apply_month" jdbcType="VARCHAR" property="applyMonth" />
    <result column="approve_month" jdbcType="VARCHAR" property="approveMonth" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="daily_batch_num" jdbcType="VARCHAR" property="dailyBatchNum" />
    <result column="daily_batch_name" jdbcType="VARCHAR" property="dailyBatchName" />
    <result column="debit_subject" jdbcType="VARCHAR" property="debitSubject" />
    <result column="credit_subject" jdbcType="VARCHAR" property="creditSubject" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="erp_status" jdbcType="TINYINT" property="erpStatus" />
    <result column="erp_message" jdbcType="VARCHAR" property="erpMessage" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
    <result column="write_off_reason" jdbcType="VARCHAR" property="writeOffReason" />
    <result column="write_off_user" jdbcType="BIGINT" property="writeOffUser" />
    <result column="write_off_time" jdbcType="TIMESTAMP" property="writeOffTime" />
    <result column="write_off_file" jdbcType="VARCHAR" property="writeOffFile" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="write_off_status" jdbcType="TINYINT" property="writeOffStatus" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, ou_id, ou_name, code, total_working_hour, total_labor_cost, gl_period, gl_date, 
    apply_month, approve_month, currency, daily_batch_num, daily_batch_name, debit_subject, 
    credit_subject, remark, erp_status, erp_message, create_by, create_at, update_by, 
    update_at, deleted_flag, write_off_reason, write_off_user, write_off_time, write_off_file, 
    status, write_off_status
  </sql>
  <select id="selectByExample" parameterType="com.midea.pam.common.ctc.entity.WorkingHourAccountingExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from working_hour_accounting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from working_hour_accounting
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from working_hour_accounting
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.midea.pam.common.ctc.entity.WorkingHourAccounting">
    insert into working_hour_accounting (id, ou_id, ou_name, 
      code, total_working_hour, total_labor_cost, 
      gl_period, gl_date, apply_month, 
      approve_month, currency, daily_batch_num, 
      daily_batch_name, debit_subject, credit_subject, 
      remark, erp_status, erp_message, 
      create_by, create_at, update_by, 
      update_at, deleted_flag, write_off_reason, 
      write_off_user, write_off_time, write_off_file, 
      status, write_off_status)
    values (#{id,jdbcType=BIGINT}, #{ouId,jdbcType=BIGINT}, #{ouName,jdbcType=VARCHAR}, 
      #{code,jdbcType=VARCHAR}, #{totalWorkingHour,jdbcType=DECIMAL}, #{totalLaborCost,jdbcType=DECIMAL}, 
      #{glPeriod,jdbcType=VARCHAR}, #{glDate,jdbcType=TIMESTAMP}, #{applyMonth,jdbcType=VARCHAR}, 
      #{approveMonth,jdbcType=VARCHAR}, #{currency,jdbcType=VARCHAR}, #{dailyBatchNum,jdbcType=VARCHAR}, 
      #{dailyBatchName,jdbcType=VARCHAR}, #{debitSubject,jdbcType=VARCHAR}, #{creditSubject,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{erpStatus,jdbcType=TINYINT}, #{erpMessage,jdbcType=VARCHAR}, 
      #{createBy,jdbcType=BIGINT}, #{createAt,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=BIGINT}, 
      #{updateAt,jdbcType=TIMESTAMP}, #{deletedFlag,jdbcType=TINYINT}, #{writeOffReason,jdbcType=VARCHAR}, 
      #{writeOffUser,jdbcType=BIGINT}, #{writeOffTime,jdbcType=TIMESTAMP}, #{writeOffFile,jdbcType=VARCHAR}, 
      #{status,jdbcType=TINYINT}, #{writeOffStatus,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.midea.pam.common.ctc.entity.WorkingHourAccounting">
    insert into working_hour_accounting
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="ouId != null">
        ou_id,
      </if>
      <if test="ouName != null">
        ou_name,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="totalWorkingHour != null">
        total_working_hour,
      </if>
      <if test="totalLaborCost != null">
        total_labor_cost,
      </if>
      <if test="glPeriod != null">
        gl_period,
      </if>
      <if test="glDate != null">
        gl_date,
      </if>
      <if test="applyMonth != null">
        apply_month,
      </if>
      <if test="approveMonth != null">
        approve_month,
      </if>
      <if test="currency != null">
        currency,
      </if>
      <if test="dailyBatchNum != null">
        daily_batch_num,
      </if>
      <if test="dailyBatchName != null">
        daily_batch_name,
      </if>
      <if test="debitSubject != null">
        debit_subject,
      </if>
      <if test="creditSubject != null">
        credit_subject,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="erpStatus != null">
        erp_status,
      </if>
      <if test="erpMessage != null">
        erp_message,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
      <if test="deletedFlag != null">
        deleted_flag,
      </if>
      <if test="writeOffReason != null">
        write_off_reason,
      </if>
      <if test="writeOffUser != null">
        write_off_user,
      </if>
      <if test="writeOffTime != null">
        write_off_time,
      </if>
      <if test="writeOffFile != null">
        write_off_file,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="writeOffStatus != null">
        write_off_status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="ouId != null">
        #{ouId,jdbcType=BIGINT},
      </if>
      <if test="ouName != null">
        #{ouName,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="totalWorkingHour != null">
        #{totalWorkingHour,jdbcType=DECIMAL},
      </if>
      <if test="totalLaborCost != null">
        #{totalLaborCost,jdbcType=DECIMAL},
      </if>
      <if test="glPeriod != null">
        #{glPeriod,jdbcType=VARCHAR},
      </if>
      <if test="glDate != null">
        #{glDate,jdbcType=TIMESTAMP},
      </if>
      <if test="applyMonth != null">
        #{applyMonth,jdbcType=VARCHAR},
      </if>
      <if test="approveMonth != null">
        #{approveMonth,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="dailyBatchNum != null">
        #{dailyBatchNum,jdbcType=VARCHAR},
      </if>
      <if test="dailyBatchName != null">
        #{dailyBatchName,jdbcType=VARCHAR},
      </if>
      <if test="debitSubject != null">
        #{debitSubject,jdbcType=VARCHAR},
      </if>
      <if test="creditSubject != null">
        #{creditSubject,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="erpStatus != null">
        #{erpStatus,jdbcType=TINYINT},
      </if>
      <if test="erpMessage != null">
        #{erpMessage,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedFlag != null">
        #{deletedFlag,jdbcType=TINYINT},
      </if>
      <if test="writeOffReason != null">
        #{writeOffReason,jdbcType=VARCHAR},
      </if>
      <if test="writeOffUser != null">
        #{writeOffUser,jdbcType=BIGINT},
      </if>
      <if test="writeOffTime != null">
        #{writeOffTime,jdbcType=TIMESTAMP},
      </if>
      <if test="writeOffFile != null">
        #{writeOffFile,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="writeOffStatus != null">
        #{writeOffStatus,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.midea.pam.common.ctc.entity.WorkingHourAccountingExample" resultType="java.lang.Long">
    select count(*) from working_hour_accounting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.midea.pam.common.ctc.entity.WorkingHourAccounting">
    update working_hour_accounting
    <set>
      <if test="ouId != null">
        ou_id = #{ouId,jdbcType=BIGINT},
      </if>
      <if test="ouName != null">
        ou_name = #{ouName,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="totalWorkingHour != null">
        total_working_hour = #{totalWorkingHour,jdbcType=DECIMAL},
      </if>
      <if test="totalLaborCost != null">
        total_labor_cost = #{totalLaborCost,jdbcType=DECIMAL},
      </if>
      <if test="glPeriod != null">
        gl_period = #{glPeriod,jdbcType=VARCHAR},
      </if>
      <if test="glDate != null">
        gl_date = #{glDate,jdbcType=TIMESTAMP},
      </if>
      <if test="applyMonth != null">
        apply_month = #{applyMonth,jdbcType=VARCHAR},
      </if>
      <if test="approveMonth != null">
        approve_month = #{approveMonth,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="dailyBatchNum != null">
        daily_batch_num = #{dailyBatchNum,jdbcType=VARCHAR},
      </if>
      <if test="dailyBatchName != null">
        daily_batch_name = #{dailyBatchName,jdbcType=VARCHAR},
      </if>
      <if test="debitSubject != null">
        debit_subject = #{debitSubject,jdbcType=VARCHAR},
      </if>
      <if test="creditSubject != null">
        credit_subject = #{creditSubject,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="erpStatus != null">
        erp_status = #{erpStatus,jdbcType=TINYINT},
      </if>
      <if test="erpMessage != null">
        erp_message = #{erpMessage,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedFlag != null">
        deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      </if>
      <if test="writeOffReason != null">
        write_off_reason = #{writeOffReason,jdbcType=VARCHAR},
      </if>
      <if test="writeOffUser != null">
        write_off_user = #{writeOffUser,jdbcType=BIGINT},
      </if>
      <if test="writeOffTime != null">
        write_off_time = #{writeOffTime,jdbcType=TIMESTAMP},
      </if>
      <if test="writeOffFile != null">
        write_off_file = #{writeOffFile,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="writeOffStatus != null">
        write_off_status = #{writeOffStatus,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.midea.pam.common.ctc.entity.WorkingHourAccounting">
    update working_hour_accounting
    set ou_id = #{ouId,jdbcType=BIGINT},
      ou_name = #{ouName,jdbcType=VARCHAR},
      code = #{code,jdbcType=VARCHAR},
      total_working_hour = #{totalWorkingHour,jdbcType=DECIMAL},
      total_labor_cost = #{totalLaborCost,jdbcType=DECIMAL},
      gl_period = #{glPeriod,jdbcType=VARCHAR},
      gl_date = #{glDate,jdbcType=TIMESTAMP},
      apply_month = #{applyMonth,jdbcType=VARCHAR},
      approve_month = #{approveMonth,jdbcType=VARCHAR},
      currency = #{currency,jdbcType=VARCHAR},
      daily_batch_num = #{dailyBatchNum,jdbcType=VARCHAR},
      daily_batch_name = #{dailyBatchName,jdbcType=VARCHAR},
      debit_subject = #{debitSubject,jdbcType=VARCHAR},
      credit_subject = #{creditSubject,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      erp_status = #{erpStatus,jdbcType=TINYINT},
      erp_message = #{erpMessage,jdbcType=VARCHAR},
      create_by = #{createBy,jdbcType=BIGINT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      write_off_reason = #{writeOffReason,jdbcType=VARCHAR},
      write_off_user = #{writeOffUser,jdbcType=BIGINT},
      write_off_time = #{writeOffTime,jdbcType=TIMESTAMP},
      write_off_file = #{writeOffFile,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      write_off_status = #{writeOffStatus,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>