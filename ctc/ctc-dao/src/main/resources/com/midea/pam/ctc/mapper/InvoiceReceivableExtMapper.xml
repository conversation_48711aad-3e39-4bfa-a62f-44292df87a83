<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.InvoiceReceivableExtMapper">

    <update id="updateDueDateStatusBatch">
      update invoice_receivable
      set due_date_status = #{dueDateStatus}
      where id in
      <foreach collection="ids" item="id" open="(" separator="," close=")">
        #{id}
      </foreach>
    </update>

    <select id="findByHeaderId" resultType="com.midea.pam.common.ctc.dto.InvoiceReceivableDto">
        SELECT
          ir.contract_id AS contractId,
          ir.currency_code AS currencyCode,
          ir.customer_code AS customerCode,
          ir.customer_Id AS customerId,
          ir.customer_name AS customerName,
          ir.exchange_rate_date AS exchangeRateDate,
          ir.exchange_rate_type AS exchangeRateType,
          ir.invoice_customer AS invoiceCustomer,
          ir.invoice_type AS invoiceType,
          ir.limit_price AS limitPrice,
          ir.ou_id AS ouId,
          ir.product_tax_name AS productTaxCode,
          ir.tax_code AS taxCode,
          ir.contract_code AS contractCode,
          ir.product AS product,
          ir.product_tax_name AS productTaxName,
          ir.unit AS unit,
          ir.price AS price,
          ir.tax_race AS taxRace,
          - ABS(ir.quantity) AS quantity,
          - ABS(ir.tax_included_price) AS taxIncludedPrice,
          - ABS(ir.exclusive_of_tax) AS exclusiveOfTax,
          ir.quantity AS oldquantity,
          ir.remark AS remark,
          ir.invoice_code AS oldInvoiceCode,
          ir.apply_code AS oldApplyCode,
          ir.notice AS notice,
          ir.old_external_invoice_num AS oldExternalInvoiceNum,
          ir.send_to_kpy AS sendToKpy,
          ir.id AS oldInvoiceId,
          ir.apply_code AS applyCode
        FROM
          invoice_receivable ir
        WHERE ir.deleted_flag = 0
        and ir.apply_header_id = #{applyHeaderId}
    </select>

    <select id="getListByClaimDetailId" resultType="com.midea.pam.common.ctc.dto.InvoiceReceivableDto">
      with temp_table as (
      select
        rcd.customer_id as temp_customer_id,
        rc.ou_id as temp_ou_id,
        ifnull(rcrel.contract_id, 9999999999999) as temp_contract_id,
        sum(rcrel.allocated_amount) as allocatedAmount ,
        sum(rcrel.local_allocated_amount) as localAllocatedAmount
      from
        pam_ctc.receipt_claim_detail rcd
      left join receipt_claim rc on
        rc.id = rcd.receipt_claim_id
        and rc.deleted_flag = 0
      left join receipt_claim_contract_rel rcrel on
        rcd.id = rcrel.receipt_claim_detail_id
        and rcrel.deleted_flag = 0
      where
        rcd.id = #{claimDetailId}
        and rcd.claim_status = 2
        and rcd.erp_status = 3
        and IFNULL(rcd.deleted_flag, 0) = 0
      group by rcrel.contract_id
      )
    select
      ir.id,
      t1.receiptClaimDetailIds,
      ir.invoice_code as invoiceCode,
      ir.external_invoice_date as externalInvoiceDate,
      round(ifnull(t1.writeOffAmount1, 0) + ifnull(t2.writeOffAmount2, 0), 2) as hasAmount,
      round(ifnull(ir.tax_included_price, 0), 2) as taxIncludedPrice,
      ir.currency_code as currencyCode,
      c.code as contractCode,
      c.name as contractName,
      tt.allocatedAmount,
      tt.localAllocatedAmount
    from
      pam_ctc.invoice_receivable ir
    left join temp_table tt on
      ir.customer_id = tt.temp_customer_id
      and ir.ou_id = tt.temp_ou_id
    left join pam_ctc.contract c on
      tt.temp_contract_id = c.id
      and c.deleted_flag = 0
    left join (
      select
        group_concat(receipt_claim_detail_id) as receiptClaimDetailIds,
        invoice_id,
        SUM(write_off_amount) as writeOffAmount1
      from
        pam_ctc.receipt_claim_invoice_rel
      where
        erp_status != 6 and deleted_flag = 0
        group by invoice_id) t1 on
      t1.invoice_id = ir.id
      left join (
        select
            ABS(sum(b.tax_included_price)) as writeOffAmount2,
            a.id
        from
            invoice_receivable a
        inner join invoice_receivable b on
            a.invoice_code = b.old_invoice_code and b.deleted_flag = 0 and b.status not in ('作废')
        where
            a.deleted_flag = 0
        group by
            a.id) t2 on
        t2.id = ir.id
      where
        ir.erp_status = 1
        and round(ifnull(t1.writeOffAmount1, 0) + ifnull(t2.writeOffAmount2, 0), 2) != round(ifnull(ir.tax_included_price, 0), 2)
        and ifnull(ir.tax_included_price, 0) > 0
        and ir.deleted_flag = 0
        and (ifnull(ir.contract_id, 9999999999999) = tt.temp_contract_id)
      <if test="contractId != null">
        and ir.contract_id = #{contractId}
      </if>
      <if test="invoiceCode != null">
        and ir.invoice_code like concat('%',#{invoiceCode},'%')
      </if>
      order by ir.gl_date asc
    </select>

</mapper>