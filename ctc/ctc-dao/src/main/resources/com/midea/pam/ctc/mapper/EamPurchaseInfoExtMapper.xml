<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.EamPurchaseInfoExtMapper">
    <select id="findList" resultType="com.midea.pam.common.ctc.dto.EamPurchaseInfoDto">
        SELECT
        ep.id,
        ep.apply_code as applyCode,
        ep.apply_name as applyName,
        ep.project_id as projectId,
        ep.project_code as projectCode,
        ep.project_name as projectName,
        ep.auditing,
        ep.contract_id as contractId,
        ep.contract_code as contractCode,
        ep.contract_name as contractName,
        ep.is_person as is<PERSON>erson,
        ep.provider_code as providerCode,
        ep.provider_name as providerName,
        ep.vendor_site_code as vendorSiteCode,
        ep.bank_name as bankName,
        ep.datacontract_number as datacontractNumber,
        ep.bank_account_num as bankAccountNum,
        ep.ou_name as ouName,
        ep.ou_id as ouId,
        ep.paid_money_cny as paidMoneyCny,
        ep.nopay_money_cny as nopayMoneyCny,
        ep.currency,
        ep.ex_rate as exRate,
        ep.travel_expenses as travelExpenses,
        ep.apply_area as applyArea,
        ep.edit_date as editDate,
        ep.edit_user_code as editUserCode,
        ep.attribute1,
        ep.attribute2,
        ep.attribute3 as managerName,
        ep.attribute4,
        ep.attribute5,
        ep.modelKey,
        ep.deleted_flag as deletedFlag,
        ep.create_at as createAt,
        ep.create_by as createBy,
        ep.update_at as updateAt,
        ep.update_by as updateBy,
        (select cm.id from pam_crm.customer cm where cm.crm_code = concat('E0',substring(ep.ou_name,4,6)) limit 1) AS customerId,
        (select cm.name from pam_crm.customer cm where cm.crm_code = concat('E0',substring(ep.ou_name,4,6)) limit 1) AS customerName,
        (select cm.crm_code from pam_crm.customer cm where cm.crm_code = concat('E0',substring(ep.ou_name,4,6)) limit 1) AS customerCrmCode,
        if((select count(1) from pam_ctc.contract c where c.eampurchase_id like concat('%',ep.id,'%') and c.deleted_flag = 0 and c.status in (1,2,3,5,10) and c.parent_id is not null)>0,'true','false') as orUsed,
        (select c.code from contract_eampurchase_relation cer left join contract c on c.id = cer.contract_id where cer.deleted_flag = 0 and cer.contract_id is not null and cer.eampurchase_id = ep.id
        order by cer.create_at desc limit 1) as connectContractCode,
        (select c.name from contract_eampurchase_relation cer left join contract c on c.id = cer.contract_id where cer.deleted_flag = 0 and cer.contract_id is not null and cer.eampurchase_id = ep.id
        order by cer.create_at desc limit 1) as connectContractName
        FROM pam_ctc.eam_purchase_info ep
        where ep.deleted_flag = 0
        and ep.auditing != '7'
        <if test="orMain != null">
            <if test="modelKey != null">
                and ep.modelKey = #{modelKey}
            </if>
        </if>
        <if test="projectName != null">
            and ep.project_name like concat('%',#{projectName},'%')
        </if>
        <if test="managerName != null">
            and ep.attribute3 like concat('%',#{managerName},'%')
        </if>
        <if test="contractCode != null">
            and ep.contract_code like concat('%',#{contractCode},'%')
        </if>
        <if test="contractName != null">
            and ep.contract_name like concat('%',#{contractName},'%')
        </if>
        <if test="crmCode != null">
            and ep.ou_name like concat('%',#{crmCode},'%')
        </if>
        <if test="ouName != null">
            and ep.ou_name like concat('%',#{ouName},'%')
        </if>
        <if test="datacontractNumber != null">
            and ep.datacontract_number like concat('%',#{datacontractNumber},'%')
        </if>
        <if test="operatingUnitName != null">
            and ep.provider_code like concat('%',#{operatingUnitName},'%')
        </if>
        <if test="eamPurchasePayIds != null">
            and ep.id in
            <foreach collection="eamPurchasePayIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="providerCodes != null">
            and ep.provider_code in
            <foreach collection="providerCodes" item="providerCode" index="index" open="(" separator="," close=")">
                #{providerCode}
            </foreach>
        </if>
    </select>

    <update id="deletecontractEamPurchaseRel" parameterType="java.util.List">
        update contract_eampurchase_relation set deleted_flag = 1
        where contract_id in 
        <foreach collection="contractIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="deletecontractEamPurchaseHisRel" parameterType="java.util.List">
        update contract_eampurchase_relation set deleted_flag = 1
        where contracthis_id in
        <foreach collection="contractIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <delete id="deleteBusinessApplyRel">
        delete from business_apply_rel where header_id is not null
    </delete>
    <select id="selectEamPaymentApplyInfo" resultType="com.midea.pam.common.ctc.dto.EamPaymentApplyInfoDto">
        select
            iah.id as invoiceApplyHeaderId,
            detail.id as invoiceApplyDetailsId,
            epai.seq_id as seqId,
            epai.pam_invoice_code as pamInvoiceCode,
            iah.eam_status as status,
            epai.apply_code as applyCode,
            epai.auditing,
            epai.edit_user as paymentApplyName,
            epai.ems_sync_status as emsSynchStatus,
            epai.ems_sync_date as emsSyncDate,
            epai.ems_pay_code as emsPayCode,
            epai.ems_biz_status as emsMoneyStatus,
            epai.ap_invoice_code as apInvoiceCode,
            epai.error_reason as errorReason,
            epi.attribute3 as eamProjectManager,
            c.name as pamContractName,
            epi.attribute4 as eamManagerMip,
            c.manager as pamManagerId,
            date_format(ir.external_invoice_date,'%Y-%m-%d')  as invoiceDate,
            ltc.name as pamProjectManager
        from
            pam_ctc.eam_payment_apply_info epai
        left join pam_ctc.invoice_apply_header iah on
            iah.apply_code = epai.pam_invoice_code
        left join pam_ctc.eam_purchase_info epi on
            epi.contract_code = epai.contract_code
        left join pam_ctc.invoice_receivable ir on
            iah.id=ir.apply_header_id
        left join pam_ctc.contract c on
            c.id=iah.contract_id
        left join pam_ctc.project p on
            p.contract_id = c.id
        left join pam_ctc.invoice_apply_details detail on
            detail.apply_header_id = iah.id
        left join pam_basedata.ltc_user_info ltc on ltc.id = c.manager
        where
            (iah.eam_status in (4,5) or (iah.eam_status = 6 and epai.apply_code is not null))
            and iah.eam_status is not null
            and epai.deleted_flag = 0
        <if test="seqId != null">
            AND epai.seq_id like concat('%', #{seqId}, '%')
        </if>
        <if test="pamInvoiceCode != null">
            AND epai.pam_invoice_code like concat('%', #{pamInvoiceCode}, '%')
        </if>
        <if test="applyCode != null">
            AND epai.apply_code like concat('%', #{applyCode}, '%')
        </if>
        <if test="paymentApplyStatusList != null">
            AND epai.auditing in
            <foreach collection="paymentApplyStatusList" item="paymentApplyStatus" index="index" open="(" separator=","
                     close=")">
                #{paymentApplyStatus}
            </foreach>
        </if>
        <if test="invoiceApplyStatusList != null">
            AND iah.eam_status in
            <foreach collection="invoiceApplyStatusList" item="invoiceApplyStatus" index="index" open="(" separator=","
                     close=")">
                #{invoiceApplyStatus}
            </foreach>
        </if>
        <if test="paymentApplyName != null">
            AND epai.edit_user like concat('%', #{paymentApplyName}, '%')
        </if>
        <if test="emsSyscStatusList != null">
            AND epai.ems_sync_status in
            <foreach collection="emsSyscStatusList" item="emsSynchStatus" index="index" open="(" separator=","
                     close=")">
                #{emsSynchStatus}
            </foreach>
        </if>
        <if test="emsMoneyStatusList != null">
            AND epai.ems_biz_status in
            <foreach collection="emsMoneyStatusList" item="emsMoneyStatus" index="index" open="(" separator=","
                     close=")">
                #{emsMoneyStatus}
            </foreach>
        </if>
        <if test="apInvoiceCode != null">
            AND epai.ap_invoice_code like concat('%', #{apInvoiceCode}, '%')
        </if>
        <if test="pamContractName != null">
            AND c.name like concat('%', #{pamContractName}, '%')
        </if>
        <if test="pamProjectManager != null">
            AND ltc.name like concat('%', #{pamProjectManager}, '%')
        </if>
        <if test="secondUnits != null and secondUnits.size > 0">
            AND c.unit_id in
            <foreach collection="secondUnits" item="secondUnit" index="index" open="(" separator=","
                     close=")">
                #{secondUnit}
            </foreach>
        </if>
        group by epai.id
        ORDER BY ir.external_invoice_date desc, epai.create_at desc
    </select>
</mapper>