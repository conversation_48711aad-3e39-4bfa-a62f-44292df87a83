<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.ProjectActivityExtMapper">
    <resultMap id="BaseResultDtoMap" type="com.midea.pam.common.ctc.dto.ProjectActivityDto">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="budget_matters_state" jdbcType="TINYINT" property="budgetMattersState"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="parent_state" jdbcType="TINYINT" property="parentState"/>
        <result column="level" jdbcType="VARCHAR" property="level"/>
        <result column="unit_id" jdbcType="BIGINT" property="unitId"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt"/>
        <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag"/>
        <result column="version" jdbcType="BIGINT" property="version"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, order_no, code, name, description, remark, budget_matters_state, type, start_time,
        end_time, parent_state, level, unit_id, create_by, create_at, update_by, update_at,
        deleted_flag, version
    </sql>

    <!-- 查询项目活动列表 -->
    <select id="selectList" parameterType="com.midea.pam.common.ctc.dto.ProjectActivityDto"
            resultMap="BaseResultDtoMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM project_activity t
        <where>
            <if test="orderNoList != null and orderNoList.size > 0">
                AND t.order_no in
                <foreach collection="orderNoList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="code != null and code != ''">
                AND t.code LIKE concat('%', #{code}, '%')
            </if>
            <if test="name != null and name != ''">
                AND t.name LIKE concat('%', #{name}, '%')
            </if>
            <if test="description != null and description != ''">
                AND t.description LIKE concat('%', #{description}, '%')
            </if>
            <if test="remark != null and remark != ''">
                AND t.remark LIKE concat('%', #{remark}, '%')
            </if>
            <if test="budgetMattersState != null">
                AND t.budget_matters_state = #{budgetMattersState,jdbcType=TINYINT}
            </if>
            <if test="budgetMattersStateList != null and budgetMattersStateList.size > 0">
                AND t.budget_matters_state in
                <foreach collection="budgetMattersStateList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="type != null and type != ''">
                AND t.type = #{type}
            </if>
            <if test="typeList != null and typeList.size > 0">
                AND t.type in
                <foreach collection="typeList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="parentState != null">
                AND t.parent_state = #{parentState,jdbcType=TINYINT}
            </if>
            <if test="parentStateList != null and parentStateList.size > 0">
                AND t.parent_state in
                <foreach collection="parentStateList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="level != null and level != ''">
                AND t.level = #{level}
            </if>
            <if test="deletedFlag != null">
                AND t.deleted_flag = #{deletedFlag,jdbcType=TINYINT}
            </if>
            <if test="unitId != null">
                AND t.unit_id = #{unitId}
            </if>
        </where>
        ORDER BY t.order_no
    </select>

    <!-- 获取第一层最大序号 -->
    <select id="maxOrderNo" resultType="string">
        SELECT
            MAX( CAST( t.order_no AS UNSIGNED) )
        FROM
            pam_ctc.project_activity t
        WHERE t.deleted_flag = 0
        AND t.level = '1'
        AND t.unit_id = #{unitId}
    </select>

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="com.midea.pam.common.ctc.entity.ProjectActivity">
        INSERT INTO project_activity(
        <include refid="Base_Column_List"/>
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.id,jdbcType=BIGINT},
            #{item.orderNo,jdbcType=VARCHAR},
            #{item.code,jdbcType=VARCHAR},
            #{item.name,jdbcType=VARCHAR},
            #{item.description,jdbcType=VARCHAR},
            #{item.remark,jdbcType=VARCHAR},
            #{item.budgetMattersState,jdbcType=TINYINT},
            #{item.type,jdbcType=VARCHAR},
            #{item.startTime,jdbcType=TIMESTAMP},
            #{item.endTime,jdbcType=TIMESTAMP},
            #{item.parentState,jdbcType=TINYINT},
            #{item.level,jdbcType=VARCHAR},
            #{item.unitId,jdbcType=BIGINT},
            #{item.createBy,jdbcType=BIGINT},
            #{item.createAt,jdbcType=TIMESTAMP},
            #{item.updateBy,jdbcType=BIGINT},
            #{item.updateAt,jdbcType=TIMESTAMP},
            #{item.deletedFlag,jdbcType=TINYINT},
            #{item.version,jdbcType=BIGINT}
            )
        </foreach>
    </insert>

    <!-- 查询层级 -->
    <select id="findLevelList" resultType="string">
        SELECT
            DISTINCT t.level
        FROM
            pam_ctc.project_activity t
        WHERE t.deleted_flag = 0
        AND t.unit_id = #{unitId}
        ORDER BY t.level
    </select>

    <!-- 费用类型下拉查询（activity方式） -->
    <select id="feeItemJoinQuery" parameterType="com.midea.pam.common.ctc.vo.FeeItemFeeSettingModeDropdownBoxVO" resultType="com.midea.pam.common.ctc.vo.FeeItemFeeSettingModeDropdownBoxVO">
        select
            t.id as projectActivityId,
            t.code,
            t.name,
            t.description
        from
            pam_ctc.project_activity t
        where
            t.budget_matters_state = 1
            and t.deleted_flag = 0
            and (t.start_time is null or t.start_time &lt;= now())
            and (t.end_time is null or t.end_time &gt;= now())
        <if test="activityType != null and activityType != '' ">
            and type = #{activityType}
        </if>
        <if test="unitId != null">
            and unit_id = #{unitId,jdbcType=BIGINT}
        </if>
        <if test="name != null and name != '' ">
            and name like concat('%', #{name,jdbcType=VARCHAR}, '%')
        </if>
        <if test="code != null and code != ''">
            and code like concat('%', #{code,jdbcType=VARCHAR}, '%')
        </if>
    </select>

    <select id="selectByCodeAndUnitId" resultType="com.midea.pam.common.ctc.entity.ProjectActivity">
        select code,id
        from project_activity
        where unit_id = #{unitId}
        and code in
            <foreach collection="activityCodes" item="activityCode" open="(" separator="," close=")">
                #{activityCode}
            </foreach>
        and start_time &lt;= date_format(now(),"%Y-%m-%d")
        and ( end_time is null or end_time >= date_format(now(),"%Y-%m-%d") )
        and ( deleted_flag = 0 or deleted_flag is null )
    </select>

</mapper>