<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.ProjectIncomeCostPlanExtMapper">

    <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.entity.ProjectIncomeCostPlan">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="project_id" jdbcType="BIGINT" property="projectId" />
        <result column="project_milepost_id" jdbcType="BIGINT" property="projectMilepostId" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="order_num" jdbcType="BIGINT" property="orderNum" />
        <result column="income_flag" jdbcType="BIT" property="incomeFlag" />
        <result column="income_ratio" jdbcType="DECIMAL" property="incomeRatio" />
        <result column="income_amount" jdbcType="DECIMAL" property="incomeAmount" />
        <result column="cost_ratio" jdbcType="DECIMAL" property="costRatio" />
        <result column="cost_amount" jdbcType="DECIMAL" property="costAmount" />
        <result column="actual_start_time" jdbcType="TIMESTAMP" property="actualStartTime" />
        <result column="actual_end_time" jdbcType="TIMESTAMP" property="actualEndTime" />
        <result column="day_num" jdbcType="INTEGER" property="dayNum" />
        <result column="carry_status" jdbcType="VARCHAR" property="carryStatus" />
        <result column="carryover_bill_id" jdbcType="BIGINT" property="carryoverBillId" />
        <result column="carryover_batch_num" jdbcType="VARCHAR" property="carryoverBatchNum" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="deleted_flag" jdbcType="BIT" property="deletedFlag" />
        <result column="create_by" jdbcType="BIGINT" property="createBy" />
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
        <result column="update_by" jdbcType="BIGINT" property="updateBy" />
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    </resultMap>

    <insert id="insertBatch" parameterType="com.midea.pam.common.ctc.entity.ProjectIncomeCostPlan">
        insert into project_income_cost_plan (id, project_id, project_milepost_id,
        name, order_num, income_flag,
        income_ratio, income_amount, cost_ratio,
        cost_amount, actual_start_time, actual_end_time,
        day_num, carry_status, carryover_bill_id,
        carryover_batch_num, remark, deleted_flag,
        create_by, create_at, update_by, update_at
        ) values
        <foreach collection="list" item="paramValue" index="index" separator=",">
            (
            #{paramValue.id,jdbcType=BIGINT}, #{paramValue.projectId,jdbcType=BIGINT},
            #{paramValue.projectMilepostId,jdbcType=BIGINT},
            #{paramValue.name,jdbcType=VARCHAR}, #{paramValue.orderNum,jdbcType=BIGINT},
            #{paramValue.incomeFlag,jdbcType=BIT},
            #{paramValue.incomeRatio,jdbcType=DECIMAL}, #{paramValue.incomeAmount,jdbcType=DECIMAL},
            #{paramValue.costRatio,jdbcType=DECIMAL},
            #{paramValue.costAmount,jdbcType=DECIMAL}, #{paramValue.actualStartTime,jdbcType=TIMESTAMP},
            #{paramValue.actualEndTime,jdbcType=TIMESTAMP},
            #{paramValue.dayNum,jdbcType=INTEGER},#{paramValue.carryStatus,jdbcType=VARCHAR},
            #{paramValue.carryoverBillId,jdbcType=BIGINT},
            #{paramValue.carryoverBatchNum,jdbcType=VARCHAR}, #{paramValue.remark,jdbcType=VARCHAR},
            #{paramValue.deletedFlag,jdbcType=BIT},
            #{paramValue.createBy,jdbcType=BIGINT}, #{paramValue.createAt,jdbcType=TIMESTAMP},
            #{paramValue.updateBy,jdbcType=BIGINT},
            #{paramValue.updateAt,jdbcType=TIMESTAMP}
            )
        </foreach>

    </insert>

    <update id="updateCarryStatusToNo" parameterType="string">
        UPDATE project_income_cost_plan
        SET carry_status = 0
        WHERE deleted_flag = 0
        and carry_status = '1'
        and carryover_batch_num = #{carryoverBatchNum}
    </update>

    <update id="updateCarryStatusToYes" parameterType="string">
        UPDATE project_income_cost_plan
        SET carry_status = 1
        WHERE deleted_flag = 0
        and carry_status = '0'
        and carryover_batch_num = #{carryoverBatchNum}
    </update>

    <select id="listNoCarryoverOfBeforeIncomeCostPlan" resultMap="BaseResultMap">
        SELECT m.*
        FROM project_income_cost_plan m
        WHERE m.deleted_flag = 0 AND m.income_flag = 1 AND (m.carryover_bill_id is null)
        AND m.project_id = #{projectId}
        <![CDATA[ AND m.order_num < (select pm.order_num from project_income_cost_plan pm where pm.id = #{id}) ]]>
    </select>

    <sql id="Base_Column_List">
        bill.id, bill.carryover_batch_num, bill.bill_num, bill.gl_period_id, bill.period_name, bill.ou_id, bill.ou_name, bill.project_id,
        bill.project_num, bill.project_name, bill.project_type, bill.customer_id, bill.customer_num, bill.customer_name,
        bill.project_milepost_id, bill.project_milepost_name, bill.income_milepost_actual_end_time, bill.cumulative_income_percent, bill.cumulative_cost_percent,
        bill.cumulative_income_total, bill.cumulative_cost_total, bill.current_income_percent, bill.current_income_amount, bill.current_income_adjustment,
        bill.current_income_total_amount, bill.current_cost_actual, bill.spread_adjustment_amount, bill.cost_ratio_config_detail_id,
        bill.cost_ratio_config_detail_cost_ratio, bill.current_cost_outsourcing_cost, bill.gross_profit_rate, bill.should_carryover_cost,
        bill.local_currency, bill.total_income, bill.total_budget, bill.erp_sync_status, bill.cost_method, bill.cost_method_name,
        bill.income_point, bill.income_point_name, bill.period_total_confirmation, bill.period_confirmed, bill.source,
        bill.status, bill.reverse_status, bill.reverse_reason, bill.reverse_period, bill.resource_type, bill.deleted_flag,
        bill.create_by, bill.create_at, bill.update_by, bill.update_at, bill.handmade_income, bill.handmade_cost, bill.original_id,
        bill.original_num, bill.accumulated_carryover_cost, bill.seq, bill.standard_cumulative_income_total, bill.standard_current_income_total_amount,
        bill.sync_erp_time, bill.conversion_type, bill.conversion_date, bill.conversion_rate
    </sql>

    <resultMap id="CarryoverBillBaseResultMap" type="com.midea.pam.common.ctc.entity.CarryoverBill">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="carryover_batch_num" jdbcType="VARCHAR" property="carryoverBatchNum" />
        <result column="bill_num" jdbcType="VARCHAR" property="billNum" />
        <result column="gl_period_id" jdbcType="BIGINT" property="glPeriodId" />
        <result column="period_name" jdbcType="VARCHAR" property="periodName" />
        <result column="ou_id" jdbcType="BIGINT" property="ouId" />
        <result column="ou_name" jdbcType="VARCHAR" property="ouName" />
        <result column="project_id" jdbcType="BIGINT" property="projectId" />
        <result column="project_num" jdbcType="VARCHAR" property="projectNum" />
        <result column="project_name" jdbcType="VARCHAR" property="projectName" />
        <result column="project_type" jdbcType="BIGINT" property="projectType" />
        <result column="customer_id" jdbcType="BIGINT" property="customerId" />
        <result column="customer_num" jdbcType="VARCHAR" property="customerNum" />
        <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
        <result column="project_milepost_id" jdbcType="BIGINT" property="projectMilepostId" />
        <result column="project_milepost_name" jdbcType="VARCHAR" property="projectMilepostName" />
        <result column="income_milepost_actual_end_time" jdbcType="TIMESTAMP" property="incomeMilepostActualEndTime" />
        <result column="cumulative_income_percent" jdbcType="DECIMAL" property="cumulativeIncomePercent" />
        <result column="cumulative_cost_percent" jdbcType="DECIMAL" property="cumulativeCostPercent" />
        <result column="accumulated_carryover_cost" jdbcType="DECIMAL" property="accumulatedCarryoverCost" />
        <result column="should_carryover_cost" jdbcType="DECIMAL" property="shouldCarryoverCost" />
        <result column="seq" jdbcType="INTEGER" property="seq" />
        <result column="cumulative_income_total" jdbcType="DECIMAL" property="cumulativeIncomeTotal" />
        <result column="standard_cumulative_income_total" jdbcType="DECIMAL" property="standardCumulativeIncomeTotal" />
        <result column="cumulative_cost_total" jdbcType="DECIMAL" property="cumulativeCostTotal" />
        <result column="current_income_percent" jdbcType="DECIMAL" property="currentIncomePercent" />
        <result column="current_income_amount" jdbcType="DECIMAL" property="currentIncomeAmount" />
        <result column="current_income_adjustment" jdbcType="DECIMAL" property="currentIncomeAdjustment" />
        <result column="current_income_total_amount" jdbcType="DECIMAL" property="currentIncomeTotalAmount" />
        <result column="standard_current_income_total_amount" jdbcType="DECIMAL" property="standardCurrentIncomeTotalAmount" />
        <result column="current_cost_actual" jdbcType="DECIMAL" property="currentCostActual" />
        <result column="spread_adjustment_amount" jdbcType="DECIMAL" property="spreadAdjustmentAmount" />
        <result column="cost_ratio_config_detail_id" jdbcType="BIGINT" property="costRatioConfigDetailId" />
        <result column="cost_ratio_config_detail_cost_ratio" jdbcType="DECIMAL" property="costRatioConfigDetailCostRatio" />
        <result column="current_cost_outsourcing_cost" jdbcType="DECIMAL" property="currentCostOutsourcingCost" />
        <result column="gross_profit_rate" jdbcType="DECIMAL" property="grossProfitRate" />
        <result column="local_currency" jdbcType="VARCHAR" property="localCurrency" />
        <result column="total_income" jdbcType="DECIMAL" property="totalIncome" />
        <result column="total_budget" jdbcType="DECIMAL" property="totalBudget" />
        <result column="erp_sync_status" jdbcType="INTEGER" property="erpSyncStatus" />
        <result column="cost_method" jdbcType="VARCHAR" property="costMethod" />
        <result column="cost_method_name" jdbcType="VARCHAR" property="costMethodName" />
        <result column="income_point" jdbcType="VARCHAR" property="incomePoint" />
        <result column="income_point_name" jdbcType="VARCHAR" property="incomePointName" />
        <result column="period_total_confirmation" jdbcType="INTEGER" property="periodTotalConfirmation" />
        <result column="period_confirmed" jdbcType="INTEGER" property="periodConfirmed" />
        <result column="source" jdbcType="INTEGER" property="source" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="reverse_status" jdbcType="INTEGER" property="reverseStatus" />
        <result column="reverse_reason" jdbcType="VARCHAR" property="reverseReason" />
        <result column="reverse_period" jdbcType="VARCHAR" property="reversePeriod" />
        <result column="resource_type" jdbcType="INTEGER" property="resourceType" />
        <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
        <result column="create_by" jdbcType="BIGINT" property="createBy" />
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
        <result column="update_by" jdbcType="BIGINT" property="updateBy" />
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
        <result column="handmade_income" jdbcType="DECIMAL" property="handmadeIncome" />
        <result column="handmade_cost" jdbcType="DECIMAL" property="handmadeCost" />
        <result column="original_id" jdbcType="BIGINT" property="originalId" />
        <result column="original_num" jdbcType="VARCHAR" property="originalNum" />
        <result column="sync_erp_time" jdbcType="TIMESTAMP" property="syncErpTime" />
        <result column="conversion_type" jdbcType="VARCHAR" property="conversionType" />
        <result column="conversion_date" jdbcType="TIMESTAMP" property="conversionDate" />
        <result column="conversion_rate" jdbcType="DECIMAL" property="conversionRate" />
    </resultMap>

    <select id="findWaitFroCarryoverByBatchNum" resultMap="CarryoverBillBaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        carryover_bill bill,
        project_income_cost_plan m
        WHERE
        bill.project_milepost_id = m.id
        AND bill.carryover_batch_num = m.carryover_batch_num
        AND m.carry_status = true
        AND m.carryover_bill_id IS NULL
        AND bill.carryover_batch_num = #{carryoverBatchNum,jdbcType=VARCHAR}
    </select>

    <select id="findLastIncomeCostPlanByProjectId" resultMap="BaseResultMap">
        SELECT
        id, project_id, project_milepost_id,
        name, order_num, income_flag,
        income_ratio, income_amount, cost_ratio,
        cost_amount, actual_start_time, actual_end_time,
        day_num, carry_status, carryover_bill_id,
        carryover_batch_num, remark, deleted_flag,
        create_by, create_at, update_by, update_at
        FROM project_income_cost_plan
        WHERE deleted_flag = 0 AND income_flag = 1
        AND project_id = #{projectId}
        ORDER BY order_num DESC
        limit 1
        <!-- SELECT
         picp.id, picp.project_id, picp.project_milepost_id,
         picp.name, picp.order_num, picp.income_flag,
         picp.income_ratio, picp.income_amount, picp.cost_ratio,
         picp.cost_amount, picp.actual_start_time, picp.actual_end_time,
         picp.day_num, picp.carry_status, picp.carryover_bill_id,
         picp.carryover_batch_num, picp.remark, picp.deleted_flag,
         picp.create_by, picp.create_at, picp.update_by, picp.update_at
         FROM pam_ctc.project_income_cost_plan picp
         left join pam_ctc.project p on picp.project_id = p.id
         WHERE picp.deleted_flag = 0 AND picp.income_flag = 1
         AND picp.project_id = #{projectId}
         and picp.carry_status != 1
         AND p.status = 10
         ORDER BY picp.order_num DESC
         limit 1-->
     </select>

    <select id="findLastMonthByProjectId" resultMap="BaseResultMap">
        SELECT
        id, project_id, project_milepost_id,
        name, order_num, income_flag,
        income_ratio, income_amount, cost_ratio,
        cost_amount, actual_start_time, actual_end_time,
        day_num, carry_status, carryover_bill_id,
        carryover_batch_num, remark, deleted_flag,
        create_by, create_at, update_by, update_at
        FROM project_income_cost_plan
        WHERE deleted_flag = 0
        AND project_id = #{projectId}
        ORDER BY order_num DESC
        limit 1
     </select>

     <update id="releaseCarryoverDraftStatusByBatchNum">
         UPDATE project_income_cost_plan
         SET carryover_batch_num = null,
             update_by = #{updateBy},
             update_at = now()
         WHERE deleted_flag = 0
         and carry_status = '0'
         and carryover_batch_num = #{carryoverBatchNum}
     </update>

     <select id="getNeedCreateIncomeCostPlan" resultType="com.midea.pam.common.ctc.dto.ProjectIncomeCostPlanCreateDTO">
         select
             t.id as projectId, t.status as projectStatus, t.approveMonth as approveMonth, t2.lastName
         FROM (select distinct p.id, p.status, max(date_format(wh.approve_time, '%Y-%m')) as 'approveMonth'
             FROM pam_ctc.working_hour wh, pam_ctc.project p, pam_ctc.project_type pt
             WHERE (date_format(wh.create_at, '%Y-%m-%d') = #{date} or date_format(wh.update_at, '%Y-%m-%d') = #{date})
             and wh.rdm_flag = 1 and wh.status = 4 and wh.approve_time is not null and p.`type` = pt.id
             and pt.cost_method = '0411' and pt.income_point = '0003'
             and p.id = wh.project_id
             group by p.id) t
         LEFT JOIN (
             select picp.project_id as project_id, max(picp.name) as lastName from pam_ctc.project_income_cost_plan picp
             where picp.deleted_flag = 0
             and picp.carryover_bill_id is null
             group by picp.project_id
             ) t2 ON t.id = t2.project_id
         where t.approveMonth > t2.lastName or t2.lastName is null
         group by t.id

     </select>

     <select id="getKsNeedCreateIncomeCostPlan" resultType="com.midea.pam.common.ctc.dto.ProjectIncomeCostPlanCreateDTO">
         select distinct tt.projectId,
                         tt.projectStatus,
                         tt.lastName
         from (
                  select t.*,
                         t2.lastName
                  from (
                           select p.id                    as projectId,
                                  p.status                as projectStatus,
                                  pt.milepost_template_id as milepostTemplateId
                           from
                               pam_ctc.project p
                               inner join pam_ctc.project_type pt on p.`type` = pt.id
                               inner join pam_ctc.project_profit pp on p.id = pp.project_id
                           where pp.deleted_flag = 0
                             and pp.cost_method_main = '0410'
                             and pp.income_point_main = '0003'
                             and p.preview_flag = 0
                             and p.status not in (0, 12, 10, 16)
                           group by p.id) t
                           left join (
                      select picp.project_id as project_id,
                             picp.order_num  as orderNum,
                             picp.name as lastName
                      from pam_ctc.project_income_cost_plan picp
                      where picp.deleted_flag = 0 ) t2 on
                      t.projectId = t2.project_id
                  where t2.lastName is not null and t2.orderNum = (select max(picp1.order_num) from pam_ctc.project_income_cost_plan picp1 where picp1.project_id = t2.project_id and picp1.deleted_flag= 0)
                  group by t.projectId) tt
     </select>

     <select id="getConfirmationFromCp" resultType="integer">
         select count(1) from pam_ctc.project_income_cost_plan  where project_id = #{projectId} and deleted_flag = 0
     </select>


     <select id="findLastIncomeCostPlan" resultMap="BaseResultMap">
         SELECT
             picp.id, picp.project_id, picp.project_milepost_id,
             picp.name, picp.order_num, picp.income_flag,
             picp.income_ratio, picp.income_amount, picp.cost_ratio,
             picp.cost_amount, picp.actual_start_time, picp.actual_end_time,
             picp.day_num, picp.carry_status, picp.carryover_bill_id,
             picp.carryover_batch_num, picp.remark, picp.deleted_flag,
             picp.create_by, picp.create_at, picp.update_by, picp.update_at
         FROM pam_ctc.project_income_cost_plan picp
                  left join pam_ctc.project p on picp.project_id = p.id
         WHERE picp.deleted_flag = 0 AND picp.income_flag = 1
           AND picp.project_id = #{projectId}
           and picp.carryover_bill_id is null
           AND p.status in (10, 16)
         ORDER BY picp.order_num DESC
             limit 1
     </select>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" open=" " separator=" " close="">
            update pam_ctc.project_income_cost_plan
            <set>
                <if test="item.projectId != null">
                    project_id = #{item.projectId,jdbcType=BIGINT},
                </if>
                <if test="item.projectMilepostId != null">
                    project_milepost_id = #{item.projectMilepostId,jdbcType=BIGINT},
                </if>
                <if test="item.name != null">
                    name = #{item.name,jdbcType=VARCHAR},
                </if>
                <if test="item.orderNum != null">
                    order_num = #{item.orderNum,jdbcType=BIGINT},
                </if>
                <if test="item.incomeFlag != null">
                    income_flag = #{item.incomeFlag,jdbcType=BIT},
                </if>
                <if test="item.incomeRatio != null">
                    income_ratio = #{item.incomeRatio,jdbcType=DECIMAL},
                </if>
                <if test="item.incomeAmount != null">
                    income_amount = #{item.incomeAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.costRatio != null">
                    cost_ratio = #{item.costRatio,jdbcType=DECIMAL},
                </if>
                <if test="item.costAmount != null">
                    cost_amount = #{item.costAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.actualStartTime != null">
                    actual_start_time = #{item.actualStartTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.actualEndTime != null">
                    actual_end_time = #{item.actualEndTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.dayNum != null">
                    day_num = #{item.dayNum,jdbcType=INTEGER},
                </if>
                <if test="item.carryStatus != null">
                    carry_status = #{item.carryStatus,jdbcType=VARCHAR},
                </if>
                <if test="item.carryoverBillId != null">
                    carryover_bill_id = #{item.carryoverBillId,jdbcType=BIGINT},
                </if>
                <if test="item.carryoverBatchNum != null">
                    carryover_batch_num = #{item.carryoverBatchNum,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.deletedFlag != null">
                    deleted_flag = #{item.deletedFlag,jdbcType=BIT},
                </if>
                <if test="item.createBy != null">
                    create_by = #{item.createBy,jdbcType=BIGINT},
                </if>
                <if test="item.createAt != null">
                    create_at = #{item.createAt,jdbcType=TIMESTAMP},
                </if>
                <if test="item.updateBy != null">
                    update_by = #{item.updateBy,jdbcType=BIGINT},
                </if>
                <if test="item.updateAt != null">
                    update_at = #{item.updateAt,jdbcType=TIMESTAMP},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT};
        </foreach>
    </update>

    <select id="findNotCarried" resultType="com.midea.pam.common.ctc.entity.ProjectIncomeCostPlan">
        select
            *
        from
            pam_ctc.project_income_cost_plan
        where
            project_id = #{projectId}
            and income_flag = 1
            and ifnull(carry_status,'0') = '0'
            and carryover_bill_id is null
            and deleted_flag = 0
    </select>

 </mapper>