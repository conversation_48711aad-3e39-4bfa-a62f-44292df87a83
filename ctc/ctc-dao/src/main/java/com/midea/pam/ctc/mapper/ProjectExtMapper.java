package com.midea.pam.ctc.mapper;

import com.meicloud.light.mapper.Mapper;
import com.midea.pam.common.crm.dto.ProjectSummaryBudget;
import com.midea.pam.common.ctc.dto.PCInvRecDTO;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.dto.WorkingHourQueryDto;
import com.midea.pam.common.ctc.entity.Contract;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectBudgetAssetChangeSummaryHistory;
import com.midea.pam.common.ctc.entity.ProjectBudgetChangeSummaryHistory;
import com.midea.pam.common.ctc.entity.ProjectBudgetFeeChangeSummaryHistory;
import com.midea.pam.common.ctc.entity.ProjectBudgetHumanChangeSummaryHistory;
import com.midea.pam.common.ctc.entity.ProjectBudgetMaterialChangeSummaryHistory;
import com.midea.pam.common.ctc.entity.ProjectBudgetTravelChangeSummaryHistory;
import com.midea.pam.common.ctc.entity.ProjectMember;
import com.midea.pam.common.ctc.vo.ProjectMemberExcelVO;
import com.midea.pam.common.ctc.vo.ProjectMilepostExcelVO;
import com.midea.pam.common.sdp.dto.SdpQuery;
import com.midea.pam.common.sdp.vo.ProjectFeeCollectionSdpVo;
import com.midea.pam.common.sdp.vo.ProjectSdpVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface ProjectExtMapper extends Mapper {

    List<ProjectDto> selectProjectForUser(@Param("userBy") Long userBy, @Param("projectStatus") int projectStatus);

    long countForDisplay(WorkingHourQueryDto query);

    /**
     * 过滤项目主里程碑的最后一个里程碑节点必须是“通过”状态的项目
     *
     * @param projectIds 待过滤的项目ID列表
     * @return 满足条件的项目ID列表
     */
    List<Long> filterLastMilepostAndApproved(@Param("projectIds") Collection<Long> projectIds);

    /**
     * 项目列表查询
     *
     * @param projectDto
     * @return
     */
    List<ProjectDto> list(ProjectDto projectDto);

    List<ProjectMilepostExcelVO> listProjectMilepostsByProjectIds(@Param("projectIds") Collection<Long> projectIds);

    List<ProjectMemberExcelVO> listProjectMembersByProjectIds(@Param("projectIds") Collection<Long> projectIds);

    /**
     * 查询项目对应的物料预算
     *
     * @param projectIds
     * @return
     */
    List<ProjectDto> getProjectMaterialAmounts(@Param("projectIds") List<Long> projectIds);

    /**
     * 查询项目对应的人力预算
     *
     * @param projectIds
     * @return
     */
    List<ProjectDto> getProjectHumanAmounts(@Param("projectIds") List<Long> projectIds);

    /**
     * 查询项目对应的差旅预算
     *
     * @param projectIds
     * @return
     */
    List<ProjectDto> getProjectTravelAmounts(@Param("projectIds") List<Long> projectIds);

    /**
     * 查询项目对应的非差旅预算
     *
     * @param projectIds
     * @return
     */
    List<ProjectDto> getProjectFeeAmounts(@Param("projectIds") List<Long> projectIds);

    List<Long> getProjectByBusinessId(@Param("businessCode") String businessCode, @Param("businessId") Long businessId, @Param("currentProjectId") Long currentProjectId);

    List<Contract> selectRemainContract(@Param("businessId") Long businessId);

    List<Long> selectRemainContractNew(@Param("businessId") Long businessId, @Param("currentProjectId") Long currentProjectId);

    BigDecimal getSummaryBudgetMaterial(@Param("projectList") List<Long> projectList);

    BigDecimal getSummaryBudgetHuman(@Param("projectList") List<Long> projectList);

    BigDecimal getSummaryBudgetTravel(@Param("projectList") List<Long> projectList);

    BigDecimal getSummaryBudgetfee(@Param("projectList") List<Long> projectList);

    List<ProjectSummaryBudget> getDetailBudgetByBusinessId(@Param("businessCode") String businessCode, @Param("businessId") Long businessId, @Param("currentProjectId") Long currentProjectId);

    Long checkPurchaseOrder(Map<String, Object> param);

    List<Project> selectRdmProjectByApplyDate(@Param("userId") Long userId, @Param("applyDate") String applyDate);

    List<ProjectDto> selectProjectByMember(ProjectMember projectMember);

    List<PCInvRecDTO> getPCInvRecMilestoneInf(@Param("projectId") Long projectId);

    List<Long> getSecondUnits(@Param("unitId") Long unitId);

    Long getUnitIdByProjectId(@Param("projectId") Long projectId);

    List<Project> findByTechnologyLeaderIdOrManagerId(@Param("projectId") Long projectId, @Param("userId") Long userId);

    Boolean queryRequirementDeliverMrp(@Param("projectId") Long projectId);

    List<Long> getProjectDetail(Long operatingUnitId);

    @Select("select * from project where original_project = #{projectId}")
    Project selectProjectByOriginalProject(@Param("projectId") Long projectId);

    @Select("select * from project where code = #{code}")
    Project findByProjectCode(@Param("code") String code);

    ProjectBudgetMaterialChangeSummaryHistory queryMaterialChangeSummaryHistory(@Param("projectId") Long projectId, @Param("headerId") Long headerId);

    ProjectBudgetHumanChangeSummaryHistory queryHumanChangeSummaryHistory(@Param("projectId") Long projectId, @Param("headerId") Long headerId);

    ProjectBudgetTravelChangeSummaryHistory queryTravelChangeSummaryHistory(@Param("projectId") Long projectId, @Param("headerId") Long headerId);

    ProjectBudgetFeeChangeSummaryHistory queryFeeChangeSummaryHistory(@Param("projectId") Long projectId, @Param("headerId") Long headerId);

    ProjectBudgetAssetChangeSummaryHistory queryAssetChangeSummaryHistory(@Param("projectId") Long projectId, @Param("headerId") Long headerId);

    ProjectBudgetChangeSummaryHistory queryChangeSummaryHistory(@Param("projectId") Long projectId, @Param("headerId") Long headerId);

    /**
     * 根据id批量查询项目记录
     *
     * @param projectIds ：项目id集合
     * @return ：List<Project>：项目集合
     */
    List<Project> selectByIds(@Param("projectIds") List<Long> projectIds);

    /**
     * 根据项目编号批量查询项目
     *
     * @param projectCodes 项目编号批集合
     * @return 项目集合
     */
    List<Project> selectByCodes(@Param("projectCodes") List<String> projectCodes);

    /**
     * 列表查询
     *
     * @return 列表信息
     */
    List<ProjectDto> selectByRequirementQuery();

    List<Map<String, Object>> getWbsByProjectId(@Param("projectId") Long projectId, @Param("userId") Long userId);

    int countIdsProjectBudgetIncrementData(SdpQuery sdpQuery);

    List<Long> selectIdsProjectBudgetIncrementData(SdpQuery sdpQuery);

    List<ProjectSdpVo> selectSdpVoByIds(@Param("projectIdList") List<Long> projectIdList, @Param("ouId") Long ouId);

    List<ProjectFeeCollectionSdpVo> selectEmsBudgetSdpVoByIds(@Param("projectIdList") List<Long> projectIdList);

    /**
     * 查询项目不含税金额（本位币）
     *
     * @param projectId
     * @return
     */
    BigDecimal getProjectContractStandardAmount(@Param("projectId") Long projectId);
}