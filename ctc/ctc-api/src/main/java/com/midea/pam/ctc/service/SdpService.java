package com.midea.pam.ctc.service;


import com.midea.pam.common.base.EsbResponse;
import com.midea.pam.common.ctc.dto.CostAccountingErpDto;
import com.midea.pam.common.ctc.dto.CustomerTransferDto;
import com.midea.pam.common.ctc.dto.DifferenceShareEsbSyncDTO;
import com.midea.pam.common.ctc.dto.InvoiceReceivableErpDto;
import com.midea.pam.common.ctc.dto.MaterialSyncErpDto;
import com.midea.pam.common.ctc.dto.PaymentApplyDto;
import com.midea.pam.common.ctc.dto.PaymentInvoiceErpDto;
import com.midea.pam.common.ctc.dto.PaymentPlanErpDto;
import com.midea.pam.common.ctc.dto.PositiveAndNegativeInvoiceRecordDto;
import com.midea.pam.common.ctc.dto.PrepayErpDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderDto;
import com.midea.pam.common.ctc.dto.WriteOffDto;
import com.midea.pam.common.enums.TopicCodeEnum;
import com.midea.pam.common.ctc.dto.ReceiptClaimDto;
import com.midea.pam.common.ctc.dto.WriteOffDto;
import com.midea.pam.common.enums.BusinessTypeEnums;

import java.util.List;

public interface SdpService {

    EsbResponse callGERPPoCommonReq(DifferenceShareEsbSyncDTO dto);

    EsbResponse callGERPPoStandardPo(List<PurchaseOrderDto> dtos, Boolean isChange);

    EsbResponse callFAPArTransfer(List<CustomerTransferDto> dtoList);

    EsbResponse callGERPGlJournalsImport(List<CostAccountingErpDto> dtoList);

    String generateSequence(TopicCodeEnum topicCodeEnum);

    /**
     * PAM-ERP-019
     *
     * @param dtoList
     * @return
     */
    EsbResponse callErpReceipt(List<ReceiptClaimDto> dtoList);

    /**
     * PAM-ERP-020
     *
     * @param dtoList
     * @return
     */
    EsbResponse callErpReverseReceipt(List<ReceiptClaimDto> dtoList);

    EsbResponse callErpPaymentInvoice(List<PaymentInvoiceErpDto> dtoList);

    EsbResponse callErpPaymentInvoiceThird(List<PaymentInvoiceErpDto> dtoList);

    EsbResponse callGERPInvTransationMaterialTransfer(List<MaterialSyncErpDto> dtoList);

    EsbResponse callGERPInvTransationMaterial(List<MaterialSyncErpDto> dtoList);

    /**
     * PAM-ERP-034 采购订单写入 (使用GERPPoStandardPoData数据结构)
     * 参照EsbServiceImpl#callCUXPOSTANDARDPOAPIPKGPortType的原逻辑，适配SDP平台调用方式
     *
     * @param dtos 采购订单数据列表
     * @return EsbResponse 响应结果
     */
    EsbResponse callGERPPoStandardPoWithData(List<PurchaseOrderDto> dtos);

    EsbResponse callSdpApCancel(List<PaymentInvoiceErpDto> dtoList, BusinessTypeEnums businessTypeEnums);

    EsbResponse callSdpPaymentPlan(List<PaymentPlanErpDto> dtoList);

    EsbResponse callSdpPrepay(List<PrepayErpDto> dtoList, String businessType);

    EsbResponse callPurchaseOrderPushFap(List<PurchaseOrderDto> purchaseOrderDtoList);

    EsbResponse callGERPApPayments(List<PaymentApplyDto> dtoList);

    EsbResponse callGERPPositiveAndNegativeRecord(List<PositiveAndNegativeInvoiceRecordDto> dtoList);

    EsbResponse callCuxicpGetSeqPkgPortType();

    EsbResponse callIcpNonrelate(List<CustomerTransferDto> dtoList);
    EsbResponse callCuxArUnapplyApiPkgPortType(List<WriteOffDto> writeOffDtoList);


    /**
     * PAM-ERP-018 应收发票写入
     *
     * @param dtoList
     * @param voucherType
     * @return
     */
    EsbResponse callErpArInvoice(List<InvoiceReceivableErpDto> dtoList, String voucherType);

    /**
     * PAM-ERP-054 应收正负发票核销
     *
     * @param dtoList
     * @return
     */
    EsbResponse callErpArTrxApply(List<InvoiceReceivableErpDto> dtoList);

    /**
     * PAM-ERP-021 收款核销
     *
     * @param dtoList
     * @return
     */
    EsbResponse callErpArApply(List<WriteOffDto> dtoList);
}
