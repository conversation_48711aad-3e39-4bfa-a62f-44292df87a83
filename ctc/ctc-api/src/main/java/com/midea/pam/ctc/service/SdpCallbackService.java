package com.midea.pam.ctc.service;


import com.midea.pam.common.sdp.dto.SdpResponse;
import com.midea.sdp.dto.common.SdpResult;
import com.midea.sdp.dto.driver.SdpDataImportReplyToDto;
import com.midea.sdp.dto.driver.SdpDataTradeImportReplyToDto;

public interface SdpCallbackService {

    SdpResult callback(SdpDataImportReplyToDto replyToDto);

    SdpResponse tradeCallback(SdpDataTradeImportReplyToDto tradeImportReplyToDto);

}
