package com.midea.pam.ctc.contract.service;

import com.github.pagehelper.PageInfo;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.PaymentApplyDto;
import com.midea.pam.common.ctc.entity.PaymentApply;
import com.midea.pam.common.ctc.query.PaymentApplyQuery;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-7-30
 * @description 付款申请
 */
public interface PaymentApplyService{

    /**
     * 付款申请单据审批通过
     * @param paymentApply
     * @return String
     */
    public void auditPass(PaymentApply paymentApply);

    /**
     * 付款申请单据审批作废
     * @param paymentApply
     * @return String
     */
    public void cancelApply(PaymentApply paymentApply);

    /**
     * 释放付款申请ap票占用金额
     * @param paymentApply
     * @return String
     */
    public void releaseApply(PaymentApply paymentApply);

    /**
     * 推送采购付款申请
     * @param paymentApplyId
     * @return String
     */
    public Map<String,String> pushPaymentApply(Long paymentApplyId);

    /**
     * 手工重推采购付款申请
     * @param paymentApplyId
     * @return String
     */
    public PaymentApply handlePaymentApply(Long paymentApplyId);

    /**
     * 分页查询付款申请分页
     *
     * @param query 查询条件
     * <AUTHOR>
     * @return 付款申请列表
     */
    PageInfo<PaymentApplyDto> selectPage(PaymentApplyQuery query);

    /**
     * 转换付款申请状态
     *
     * @param paymentApplyDto
     */
    void statusTransformation(PaymentApplyDto paymentApplyDto);

    /**
     * 分页查询付款申请
     *
     * @param query 查询条件
     * <AUTHOR>
     * @return 付款申请列表
     */
    public List<PaymentApplyDto> selectListAll(PaymentApplyQuery query);


    /**
     * 查询付款申请对象
     *
     * @param code 编码
     * <AUTHOR>
     * @return PaymentApply
     */
    PaymentApply findPaymentApplyByCode(String code);


    /**
     * 查询付款申请
     *
     * @param query 查询条件
     * @return 付款申请列表
     */
    List<PaymentApplyDto> list(PaymentApplyQuery query);

    /**
     * 付款申请详情
     * @param id 付款申请ID
     * <AUTHOR>
     * @return 付款申请详情
     */
    PaymentApplyDto findPaymentApplyById(Long id);


    /**
     * 保存付款申请
     * <AUTHOR>
     * @param paymentApplyDto 付款申请
     * @return Long
     */
    PaymentApplyDto saveOrUpdate(PaymentApplyDto paymentApplyDto);

    /**
     * 作废付款申请
     * <AUTHOR>
     * @param applyId 付款申请ID
     * @return Long
     */
    int abandon(Long applyId);

    /**
     * 保存付款申请
     * <AUTHOR>
     * @param paymentApply 付款申请
     * @return 影响记录数
     */
    int save(PaymentApply paymentApply);

    /**
     * 更新付款申请
     *
     * @param paymentApply 付款申请
     * @return 影响记录数
     */
    int update(PaymentApply paymentApply);

    /**
     * 预付款回调接口
     * @param paymentApplyNo 单据编码
     * @param gcebStatus 状态
     * @param remark 备注
     * @return String success成功 fail失败
     */
    String updatePaymentGcebStatus(String paymentApplyNo, String gcebStatus, String remark, BigDecimal cancelAmount);

    /**
     * 移除付款申请（逻辑删除）
     *
     * @param id 付款申请ID
     * @return 影响记录数
     */
    int deleteById(Long id);

    /**
     * 付款申请详情
     * @param id 付款申请ID
     * <AUTHOR>
     * @return 付款申请详情
     */
    PaymentApply findById(Long id);

    /**
     * PAM-ERP-028 付款申请写入
     * @param applyDto
     */
    void pushApplyToErp(PaymentApplyDto applyDto, String lastUpdateDate);

    /**
     * PAM-ERP-028 付款计划写入
     * 处理erp回掉逻辑
     * @param id
     * @param isSuccess
     * @param msg
     * @param actualCost
     * @return
     */
    Boolean planCallBack(Long id, Boolean isSuccess, String msg, String actualCost);

    /**
     * erp查询应付付款结果
     * @param paramMap
     */
    void getApplyInfoFromErp(PaymentApplyDto applyDto, Map<String, String> paramMap);

    /**
     * erp查询应付付款状态
     * @param paramMap
     */
    void getApplyStatusFromErp(Map<String, String> paramMap, PaymentApplyDto applyDto);

    /**
     * 获取应付付款明细
     * @param applyDto
     * @param lastUpdateDate
     */
    void getApplyInfo(PaymentApplyDto applyDto, String lastUpdateDate);

    /**
     * 定时处理付款申请单
     * @param lastUpdateDate
     */
    void dealPaymentApplyAtTime(String lastUpdateDate,String paymentApplyCode);

    /**
     * 预付款核销
     * @return
     */
    String paymentWriteOff(Long paymentRecordId);


    String deme(String username);

    /**
     * 移动审批采购合同付款申请
     * @param id
     * @return
     */
    ResponseMap getPaymentApplyApp(Long id);

    Map<String, String> emailPaymentApply(Long id);

    /**
     * 迪链发票写入完成后，回调发送迪链预付款付款
     * @param paymentId ： 付款申请id
     * @param status : 迪链发票写入是否成功
     * @param msg ：迪链发票写入响应消息
     */
    void prePaymentDlPay(Long paymentId,boolean status,String msg);

    /**
     * 迪链预付款付款回调
     * @param paymentId ： 付款申请id
     * @param status : 迪链预付款付款是否成功
     * @param msg ：迪链预付款付款写入响应消息
     */
    void prePaymentDlPayCallBack(Long paymentId, boolean status, String msg);

    /**
     * 更新付款计划支付状态
     *
     * @param paymentPlanId
     */
    void updatePaymentPlanStatus(Long paymentPlanId);


    void updatePaymentPlanStatusAndActualAmount(Long paymentPlanId);

    /**
     * 更新审核状态为异常
     * @param id
     * @return
     */
    boolean updateAuditStatusForException(Long id);
}
