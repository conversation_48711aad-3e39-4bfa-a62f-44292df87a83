package com.midea.pam.ctc.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.midea.gems.gemsadjustapplyservice.v1.CreateAdjustApply;
import com.midea.gems.gemsbudgetnodeservice.v1.CreateBudgetNode;
import com.midea.gems.gemsprojectnumberservice.v1.GemsProjectNumber;
import com.midea.pam.common.base.CarrierResponse;
import com.midea.pam.common.base.EsbResponse;
import com.midea.pam.common.crm.dto.BusinessDto;
import com.midea.pam.common.ctc.dto.ProjectBudgetFeeItemDto;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.entity.EsbResultReturn;
import com.midea.pam.common.ctc.entity.EsbResultReturnRecord;
import com.midea.pam.common.ctc.entity.ProjectBudgetChangePushEms;
import com.midea.pam.common.ctc.vo.ZeroZeroNineResponseVo;
import com.midea.pam.common.enums.GEMSOrderTypeEnums;
import com.midea.pam.common.enums.ResponseCodeEnums;
import com.midea.pam.common.esb.constant.EsbConstant;
import com.midea.pam.common.esb.dto.EmsPassRequest;
import com.midea.pam.common.esb.dto.PassResponse;
import com.midea.pam.common.esb.util.EsbUtil;
import com.midea.pam.common.util.BeanMapTool;
import com.midea.pam.common.util.DateUtil;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.PamStringUtil;
import com.midea.pam.ctc.common.utils.CarrierHelper;
import com.midea.pam.ctc.esb.service.EsbResultReturnRecordService;
import com.midea.pam.ctc.esb.service.EsbResultReturnService;
import com.midea.pam.ctc.project.service.ProjectBudgetChangePushEmsService;
import com.midea.pam.ctc.service.GEMSCarrierServicel;
import com.midea.pam.ctc.service.ProjectFeeCollectionService;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class GEMSCarrierServicelImpl implements GEMSCarrierServicel {

    private static final Logger logger = LoggerFactory.getLogger(GEMSCarrierServicelImpl.class);

    // 项目号同步
    private static final String SAVE_PAM_PROJECT_NUMBER = "GEMS/gems-expense/emsBdPamProject/savePamProjectNumber";
    // 创建预算单元
    private static final String CREATE_OFFICAL_BUDGET_NODE = "GEMS/gems-esb2httpv3/emsIoBmBudgetNode";
    // 创建预算调整单
    private static final String CREATE_IO_ADJUST_APPLY = "GEMS/gems-esb2httpv3/emsIoBmAdjustApplyH/createIoAdjustApply";
    // 创建pam-wbs号
    private static final String SAVE_PROJ_WBS_REL = "GEMS/gems-esb2httpv3/pam/saveProjWbsRel";

    public static final String PROCESSED = "Processed";
    public static final String ERROR = "Error";


    @Resource
    private CarrierHelper carrierHelper;
    @Resource
    private EsbResultReturnService esbResultReturnService;
    @Resource
    private EsbResultReturnRecordService esbResultReturnRecordService;
    @Resource
    private ProjectBudgetChangePushEmsService projectBudgetChangePushEmsService;
    @Resource
    private ProjectFeeCollectionService projectFeeCollectionService;


    /**
     * PAM-EMS-004 项目号同步
     */
    @Override
    public EsbResponse gemsProjectNumber(final ProjectDto project, Integer projectStatus, BusinessDto businessDto) {
        logger.info("GEMS项目号同步原始入参：【project:】{}, 【projectStatus:】{}, 【businessDto:】{}", JSONObject.toJSONString(project), projectStatus, JSONObject.toJSONString(businessDto));
        //交易流水号
        String serialNo = EsbConstant.PAM_REQUEST_ID + new Date().getTime();
        try {
            //构建入参
            List<Map<String, Object>> body = buildSavePamProjectNumberBody(project, projectStatus, businessDto);
            //发起请求
            String res = carrierHelper.doPost(SAVE_PAM_PROJECT_NUMBER, JSONObject.toJSONString(body));
            JSONObject jsonResult = JSONObject.parseObject(res);
            if (Objects.nonNull(jsonResult) && jsonResult.getString("code").equals("0000")) {
                EsbResponse<String> esbResponse = new EsbResponse<>();
                esbResponse.setResponsecode(ResponseCodeEnums.SUCESS.getCode());
                esbResponse.setResponsemessage(jsonResult.getString("msg"));
                esbResponse.setData(serialNo);
                return esbResponse;
            } else {
                String result = JSON.toJSONString(jsonResult);
                return new EsbResponse<>("", ResponseCodeEnums.FAULT.getCode(), result.length() > 3000 ? result.substring(0, 3000) : result, serialNo);
            }
        } catch (Exception e) {
            logger.error("GEMS项目号同步出现异常", e);
            return new EsbResponse<>("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), serialNo);
        }
    }

    /**
     * PAM-EMS-006 创建预算单元
     */
    @Override
    public EsbResponse createBudgetNode(final ProjectBudgetFeeItemDto budget) {
        logger.info("GEMS创建预算单元原始入参：{}", JSONObject.toJSONString(budget));
        //交易流水号
        String serialNo = EsbConstant.PAM_REQUEST_ID + new Date().getTime();
        try {
            //构建入参
            Map<String, Object> body = buildEmsIoBmBudgetNodeBody(budget);
            //发起请求
            String res = carrierHelper.doPost(CREATE_OFFICAL_BUDGET_NODE, JSONObject.toJSONString(body));
            JSONObject jsonResult = JSONObject.parseObject(res);
            if (Objects.nonNull(jsonResult) && jsonResult.getString("code").equals("0000")) {
                EsbResponse<String> esbResponse = new EsbResponse<>();
                esbResponse.setResponsecode(ResponseCodeEnums.SUCESS.getCode());
                esbResponse.setResponsemessage(jsonResult.getString("msg"));
                esbResponse.setData(serialNo);
                return esbResponse;
            } else {
                String result = JSON.toJSONString(jsonResult);
                return new EsbResponse<>("", ResponseCodeEnums.FAULT.getCode(), result.length() > 3000 ? result.substring(0, 3000) : result, serialNo);
            }
        } catch (Exception e) {
            logger.error("GEMS预算单元写入出现异常", e);
            return new EsbResponse<>("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), serialNo);
        }
    }

    /**
     * PAM-EMS-007 创建预算调整单
     */
    @Override
    public EsbResponse createAdjustApply(ProjectBudgetChangePushEms projectBudgetChangePushEm) {
        logger.info("GEMS创建预算调整单原始入参：{}", JSONObject.toJSONString(projectBudgetChangePushEm));
        if (projectBudgetChangePushEm.getBudgetNodeId() == null) {
            return new EsbResponse<>("", ResponseCodeEnums.FAULT.getCode(), "预算id不存在，不同步", null);
        }

        //交易流水号
        String serialNo = EsbConstant.PAM_REQUEST_ID + new Date().getTime();
        try {
            //构建入参
            Map<String, Object> body = buildEmsIoBmAdjustApplyBody(projectBudgetChangePushEm);
            //发起请求
            String res = carrierHelper.doPost(CREATE_IO_ADJUST_APPLY, JSONObject.toJSONString(body));
            JSONObject jsonResult = JSONObject.parseObject(res);
            if (Objects.nonNull(jsonResult) && jsonResult.getString("code").equals("0000")) {
                EsbResponse<String> esbResponse = new EsbResponse<>();
                esbResponse.setResponsecode(ResponseCodeEnums.SUCESS.getCode());
                esbResponse.setResponsemessage(jsonResult.getString("msg"));
                esbResponse.setData(serialNo);
                return esbResponse;
            } else {
                String result = JSON.toJSONString(jsonResult);
                return new EsbResponse<>("", ResponseCodeEnums.FAULT.getCode(), result.length() > 3000 ? result.substring(0, 3000) : result, serialNo);
            }
        } catch (Exception e) {
            logger.error("GEMS创建预算调整单出现异常", e);
            return new EsbResponse<>("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), serialNo);
        }
    }

    /**
     * PAM-EMS-009 创建pam-wbs号 同步项目-wbs关联关系
     *
     * @param projectDto
     * @return
     */
    @Override
    public CarrierResponse saveProjWbsRel(ProjectDto projectDto) {
        logger.info("GEMS创建pam-wbs号同步项目-wbs关联关系原始入参：{}", JSONObject.toJSONString(projectDto));
        List<Map<String, String>> wbsList = new ArrayList<>();
        Map<String, String> params1 = new HashMap<>();
        params1.put("projectCode", projectDto.getCode());
        params1.put("wbsCode", projectDto.getWbsCode());
        params1.put("wbsName", projectDto.getWbsName());
        wbsList.add(params1);
        String wbsCodeJson = JSONObject.toJSONString(wbsList);
        String response = carrierHelper.doPost(SAVE_PROJ_WBS_REL, wbsCodeJson);
        ZeroZeroNineResponseVo data = JSON.parseObject(response, new TypeReference<ZeroZeroNineResponseVo>() {
        });

        CarrierResponse result = new CarrierResponse();
        if (data == null) {
            result.setResponsecode("9999");
            result.setResponsemessage(response);
        } else {
            result.setResponsecode(data.getCode());
            result.setData(data.getData());
            result.setResponsemessage(data.getMsg());
        }
        return result;
    }

    private List<Map<String, Object>> buildSavePamProjectNumberBody(ProjectDto project, Integer projectStatus, BusinessDto businessDto) {
        GemsProjectNumber.BodyInput.ItemDetail itemDetail = new GemsProjectNumber.BodyInput.ItemDetail();
        if (project != null) {
            itemDetail.setOuId(String.valueOf(project.getOuId()));
            itemDetail.setOuCode(project.getOuCode());
            itemDetail.setOuName(project.getOuName());
            itemDetail.setProjectNumber(project.getCode());
            itemDetail.setProjectName(project.getName());
            itemDetail.setManagerMip(project.getManagerMip());
            itemDetail.setManagerMipName(project.getManagerName());
            itemDetail.setFinanceMip(project.getFinancialMip());
            itemDetail.setFinanceMipName(project.getFinancialName());
            itemDetail.setStatus(String.valueOf(projectStatus));
            // 预算维度: 1-项目号,2-wbs预算编制
            if (BooleanUtils.isTrue(project.getWbsEnabled())) {
                itemDetail.setAttribute1("2");
            } else {
                itemDetail.setAttribute1("1");
            }

        } else if (businessDto != null) {
            itemDetail.setOuId(String.valueOf(businessDto.getOperatingUnitId()));
            itemDetail.setOuCode(businessDto.getOuCode());
            itemDetail.setOuName(businessDto.getOperatingUnitName());
            itemDetail.setProjectNumber(businessDto.getBusinessCode());
            itemDetail.setProjectName(businessDto.getName());
            if (Objects.equals(1, businessDto.getStatus())) { //审批通过/关闭(放弃)后重新打开
                itemDetail.setStatus("Y");
            } else if (Objects.equals(0, businessDto.getStatus()) || Objects.equals(3, businessDto.getStatus())) { // 关闭
                // (放弃)/商机赢单（后45天）
                itemDetail.setStatus("N");
            }
            itemDetail.setType("business");
        }

        Map<String, Object> resultMap = BeanMapTool.beanToMap(itemDetail);
        resultMap.put("sourceSystem", "PAM");

        List<Map<String, Object>> resultList = new ArrayList<>();
        resultList.add(resultMap);
        return resultList;
    }

    private Map<String, Object> buildEmsIoBmBudgetNodeBody(ProjectBudgetFeeItemDto budget) {
        CreateBudgetNode.BmIoBudgetNode bmIoBudgetNode = new CreateBudgetNode.BmIoBudgetNode();
        bmIoBudgetNode.setSourceSystem(EsbConstant.PAM_REQUEST_ID);//PAM
        bmIoBudgetNode.setSourceOrderCode(String.valueOf(budget.getId()));
        bmIoBudgetNode.setSourceOrderID(String.valueOf(budget.getId()));
        bmIoBudgetNode.setBudgetHeadersId(budget.getBudgetHeaderId());
        bmIoBudgetNode.setBusiOrgCode(budget.getBudgetDepCode());
        bmIoBudgetNode.setBusiOrgName(budget.getBudgetDepName());
        bmIoBudgetNode.setProjectTypeCode(budget.getCode());
        if (Objects.equals(Boolean.TRUE, budget.getWbsEnabled())) {
            bmIoBudgetNode.setProjectTypeCode(budget.getWbsCode());
        }
        bmIoBudgetNode.setProjectTypeName(budget.getName());
        bmIoBudgetNode.setFeeTypeCode(budget.getFeeTypeCode());
        bmIoBudgetNode.setFeeTypeName(budget.getFeeTypeName());
        bmIoBudgetNode.setBudgetAmount(budget.getAmount());
        if (StringUtils.isNotEmpty(budget.getCreatedByCode())) {
            bmIoBudgetNode.setCreatedByCode(budget.getCreatedByCode());
        }
        if (budget.getCreatedAt() != null) {
            bmIoBudgetNode.setCreationDate(EsbUtil.formatXMLGreDate(budget.getCreatedAt()));
        }
        bmIoBudgetNode.setYearCode(DateUtils.getYear(new Date()));

        Map<String, Object> resultMap = BeanMapTool.beanToMap(bmIoBudgetNode);
        resultMap.put("creationDate", DateUtil.format(budget.getCreatedAt()));
        resultMap.put("lastUpdateDate", DateUtil.format(budget.getCreatedAt()));
        resultMap.put("businesstypename", budget.getName()); //项目名称
        resultMap.put("businesstypecode", budget.getCode()); //项目号
        return resultMap;
    }

    private Map<String, Object> buildEmsIoBmAdjustApplyBody(ProjectBudgetChangePushEms projectBudgetChangePushEms) {
        final Long projectBudgetChangePushEmsId = projectBudgetChangePushEms.getId();
        final Date applyDate = projectBudgetChangePushEms.getApplyDate();
        final String reason = projectBudgetChangePushEms.getApplyReason();
        final String username = projectBudgetChangePushEms.getApplyUser();
        final BigDecimal changeAmount = projectBudgetChangePushEms.getChangeAmount();
        final Long budgetNodeId = projectBudgetChangePushEms.getBudgetNodeId();

        //头
        CreateAdjustApply.EmsIoBmAdjustApplyH emsIoBmAdjustApplyH = new CreateAdjustApply.EmsIoBmAdjustApplyH();
        emsIoBmAdjustApplyH.setApplyByCode(username);
        emsIoBmAdjustApplyH.setApplyDate(EsbUtil.formatXMLGreDate(applyDate));
        emsIoBmAdjustApplyH.setReasonDesc(reason);
        emsIoBmAdjustApplyH.setSourceSystem("PAM");
        emsIoBmAdjustApplyH.setSourceOrderType("BM");
        emsIoBmAdjustApplyH.setSourceOrderId(String.valueOf(projectBudgetChangePushEmsId));
        emsIoBmAdjustApplyH.setSourceOrderCode(String.valueOf(projectBudgetChangePushEmsId));
        emsIoBmAdjustApplyH.setCreatedByCode(username);
        emsIoBmAdjustApplyH.setCreationDate(EsbUtil.formatXMLGreDate(applyDate));
        emsIoBmAdjustApplyH.setBmAdjustType("BESIDE_BUDGET");
        emsIoBmAdjustApplyH.setIsDraft("N");
        Map<String, Object> resultMap = BeanMapTool.beanToMap(emsIoBmAdjustApplyH);
        resultMap.put("applyDate", DateUtil.format(applyDate));
        resultMap.put("creationDate", DateUtil.format(applyDate));
        resultMap.put("applyLdapCode", username);
        resultMap.put("createdLdapCode", username);

        //行
        CreateAdjustApply.EmsIoBmAdjustApplyH.EmsIoBmAdjustApplyL emsIoBmAdjustApplyL = new CreateAdjustApply.EmsIoBmAdjustApplyH.EmsIoBmAdjustApplyL();
        emsIoBmAdjustApplyL.setAdjustAmount(changeAmount);
        emsIoBmAdjustApplyL.setBudgetNodeId(budgetNodeId);
        emsIoBmAdjustApplyL.setRemark(reason);
        resultMap.put("emsIoBmAdjustApplyLVO", Arrays.asList(BeanMapTool.beanToMap(emsIoBmAdjustApplyL)));

        return resultMap;
    }

    @Override
    public PassResponse callback(EmsPassRequest request) {
        PassResponse response = new PassResponse();
        try {
            if (request != null) {
                logger.info("GEMSCarrierServicelImpl回调入参: {}", JSON.toJSONString(request));
                //类似PROCESSING这类的日志不接，没什么意义
                if (!Objects.equals(request.getSyncStatus(), PROCESSED) && !Objects.equals(request.getSyncStatus(), ERROR)) {
                    return response;
                }

                //存入日志表
                EsbResultReturn esbResultReturn = new EsbResultReturn();
                esbResultReturn.setInput(JSON.toJSONString(request));
                esbResultReturn.setHandleFlag("N");
                esbResultReturnService.insert(esbResultReturn);

                callbackHandle(request);

                response.setResponseCode("000000");
                response.setResponseMessage("SUCCESS");
            } else {
                response.setResponseCode("000001");
                response.setResponseMessage("GEMSCarrierServicelImpl回调报文无法解析");
            }
        } catch (Exception ex) {
            logger.error("GEMSCarrierServicelImpl回调异常：", ex);
            response.setResponseCode("000001");
            response.setResponseMessage(ex.getStackTrace()[0].getClassName() + "/" + ex.getStackTrace()[0].getMethodName() + "/" + ex.getStackTrace()[0].getLineNumber()
                    + "/" + ex.getMessage());
        }
        return response;
    }

    public void callbackHandle(EmsPassRequest request) {
        String syncStatus = request.getSyncStatus();
        String errorType = request.getErrorType();
        String errorReason = request.getErrorReason();
        String sourceOrderId = request.getSourceOrderId();
        String sourceOrderCode = request.getSourceOrderCode();
        String budgetNodeId = request.getBudgetNodeId() != null ? request.getBudgetNodeId().toString() : null;
        String orderType = request.getOrderType(); // 接口编号

        EsbResultReturnRecord esbResultReturnRecord = new EsbResultReturnRecord();
        esbResultReturnRecord.setSourceId1(sourceOrderId);
        esbResultReturnRecord.setSourceId2(sourceOrderCode);
        esbResultReturnRecord.setSourceId3(budgetNodeId);
        esbResultReturnRecord.setStatus(syncStatus);
        esbResultReturnRecord.setErrorCode(errorType);
        esbResultReturnRecord.setErrorMessage(errorReason);
        esbResultReturnRecord.setTargetId1(orderType);
        esbResultReturnRecord.setApplyNo(sourceOrderId);
        esbResultReturnRecord.setHandleFlag("N");

        esbResultReturnRecordService.insert(esbResultReturnRecord);

        boolean handleFlag = true;

        try {
            //业务处理
            if (Objects.equals(orderType, GEMSOrderTypeEnums.CREATE_BUDGET.getCode())) {//创建预算单元
                projectFeeCollectionService.emsWriteReturn(PamStringUtil.toLongValue(sourceOrderId), budgetNodeId, syncStatus, errorReason);
            } else if (Objects.equals(orderType, GEMSOrderTypeEnums.BESIDE_BUDGET.getCode())) {//创建预算调整单
                projectBudgetChangePushEmsService.emsWriteReturn(PamStringUtil.toLongValue(sourceOrderId), null, syncStatus, errorReason);
            } else {
                // 未匹配
                handleFlag = false;
            }
        } catch (Exception e) {
            logger.error("GEMSCarrierServicelImpl回调业务处理异常,单据号:{},接口卡:{}", sourceOrderId, orderType, e);
            handleFlag = false;
            esbResultReturnRecord.setExceptionMessage(e.getMessage());
            esbResultReturnRecordService.updateByPrimaryKeyWithBLOBs(esbResultReturnRecord);
        }

        if (handleFlag) {
            esbResultReturnRecord.setHandleFlag("Y");
            esbResultReturnRecordService.updateByPrimaryKey(esbResultReturnRecord);
        }

    }
}
