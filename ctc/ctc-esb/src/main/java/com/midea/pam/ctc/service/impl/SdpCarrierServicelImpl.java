package com.midea.pam.ctc.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.basedata.entity.EsbMassQueryRecord;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.dto.PurchaseOrderDto;
import com.midea.pam.common.ctc.dto.SdpTradeResultResponseDto;
import com.midea.pam.common.ctc.dto.SdpTradeResultResponseEleDto;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.EsbMassQueryPushPlatformTypeEnum;
import com.midea.pam.common.esb.constant.EsbConstant;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.ctc.common.utils.HttpClientUtil;
import com.midea.pam.ctc.feign.basedata.feign.BasedataEsbMassQueryRecordFeignClient;
import com.midea.pam.ctc.sdp.vo.ResponseObj;
import com.midea.pam.ctc.service.PurchaseOrderService;
import com.midea.pam.ctc.service.SdpCarrierServicel;
import com.midea.sdp.constant.dispatcher.SdpOutType;
import com.midea.sdp.dto.common.SdpResult;
import com.midea.sdp.dto.common.SdpTradeResult;
import com.midea.sdp.dto.driver.SdpDataImportDto;
import com.midea.sdp.dto.driver.SdpDataTradeImportDto;
import com.midea.sdp.util.SignUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public class SdpCarrierServicelImpl implements SdpCarrierServicel {
    private Logger logger = LoggerFactory.getLogger(this.getClass());

    private static final String BUSINESSID_TRANSMIT_NAME = "businessId";

    @Value("${mgp.apiurl:}")
    private String apiurl; // 对应认证服务环境域名前缀，具体地址请前往访问令牌获取

    @Value("${sdp.appKey:}")
    private String appKey;

    @Value("${sdp.appSecret:}")
    private String appSecret;

    @Value("${sdp.callbackUrl:}")
    private String callbackUrl;  //sdp异步回调PAM的接口

    // sdp交易类或查询接口
    private static final String SDP_SERVICE = "SDP/sdp-service/service/driver/data/in";

    // sdp数据类推送接口
    private static final String SDP_DATA_SERVICE = "SDP/sdp-application/api/driver/data/in";

    @Resource
    private BasedataEsbMassQueryRecordFeignClient esbMassQueryRecordFeignClient;

    @Override
    public SdpTradeResult callERPQuery(Map<String, String> requestParam) {
        requestParam.put("sdpPExtend","ALTER SESSION SET NLS_LANGUAGE='AMERICAN'");
        return doSdpDataQuery("GERP", "GERPNEWMASSQUERY", requestParam);
    }


    /**
     * 查询类接口（SDP提供）
     *
     * @param targetSystemCode
     * @param topicCode
     * @param requestParam
     * @return
     */
    @Override
    public SdpTradeResult doSdpDataQuery(String targetSystemCode, String topicCode, Map<String, String> requestParam) {
        SdpDataTradeImportDto tradeImportDto = new SdpDataTradeImportDto();
        tradeImportDto.setTargetSystemCode(targetSystemCode);
        tradeImportDto.setIsQuery(Boolean.TRUE);
        // SDP-MLS-GERPWIPTRANSATION-39221110011002
        String sequence = CacheDataUtils.generateSequence(Constants.CODE_PAM_CODE_LENGTH, targetSystemCode);
        tradeImportDto.setBusinessId("SDP-PAM-" + topicCode + "-" + sequence);
        requestParam.put("sdpPEsbSerialNum", tradeImportDto.getBusinessId());
        tradeImportDto.setRequestObj(requestParam);
        tradeImportDto.setSystemCode("PAM");
        tradeImportDto.setCallbackEnabled(Boolean.FALSE);
        tradeImportDto.setTopicCode(topicCode);
        sign(tradeImportDto, com.midea.sdp.constant.common.Constants.SIGN_METHOD_MD5);

        String json = JSON.toJSONString(tradeImportDto);
        // 服务方paas提供API地址
        String paasApiUrl = apiurl + SDP_SERVICE;
        logger.info("SDP查询接口:{},请求参数:{}", paasApiUrl, json);
        // 发起请求
        try {
            String response = HttpClientUtil.doPost(paasApiUrl, json);
            logger.info("SDP查询接口返回结果:{}", getSort(response));
            SdpTradeResult data = JSON.parseObject(response, new TypeReference<SdpTradeResult>() {
            });
            return data;
        } catch (Exception e) {
            logger.error("流水号:{},SDP查询接口失败:", tradeImportDto.getBusinessId(), e);
            return null;
        }
    }

    /**
     * 交易类接口（SDP提供）
     *
     * @param targetSystemCode
     * @param topicCode
     * @param businessId
     * @param requestObj
     * @return
     */
    @Override
    public SdpTradeResult doSdpDataTrade(String targetSystemCode, String topicCode, String businessId, Object requestObj) {
        SdpDataTradeImportDto tradeImportDto = new SdpDataTradeImportDto();
        // requestObj 下的 esbSerialNum 参数和 businessId 保持一致，后续在SDP日志平台方便查询
        tradeImportDto.setBusinessId(businessId);
        // 是否查询请求
        tradeImportDto.setIsQuery(Boolean.FALSE);
        // 业务数据
        tradeImportDto.setRequestObj(requestObj);
        // 交易发起系统（如MLS调用GERP，则为MLS）
        tradeImportDto.setSystemCode("PAM");
        // 交易发起的目标系统（如MLS调用GERP，则为GERP）
        tradeImportDto.setTargetSystemCode(targetSystemCode);
        // 由SDP系统指定，如：ITEM
        tradeImportDto.setTopicCode(topicCode);
        // 签名
        sign(tradeImportDto, com.midea.sdp.constant.common.Constants.SIGN_METHOD_SHA256);
        // 是否回调（优先取输入配置中的是否回调信息）
        tradeImportDto.setCallbackEnabled(Boolean.TRUE);
        // 回调端点（优先取输入配置中的回调端点信息）
        tradeImportDto.setCallbackEndpoint(callbackUrl);
        // 回调类型,可用值:RabbitMQ,HTTP
        tradeImportDto.setCallbackType(SdpOutType.HTTP);

        String json = JSON.toJSONString(tradeImportDto);
        // 服务方paas提供API地址
        String paasApiUrl = apiurl + SDP_SERVICE;
        logger.info("SDP查询接口:{},请求参数:{}", paasApiUrl, json);
        // 发起请求
        try {
            String response = HttpClientUtil.doPost(paasApiUrl, json);
            logger.info("SDP查询接口返回结果:{}", getSort(response));
            SdpTradeResult<ResponseObj> data = JSON.parseObject(response, new TypeReference<SdpTradeResult<ResponseObj>>() {
            });
            return data;
        } catch (Exception e) {
            logger.error("流水号:{},SDP查询接口失败:", tradeImportDto.getBusinessId(), e);
            return SdpTradeResult.error(e.getMessage());
        }
    }

    /**
     * 数据类数据推送(SDP提供)
     *
     * @param targetSystemCode
     * @param topicCode
     * @param businessId
     * @param dataList
     * @return
     */
    @Override
    public SdpResult doSdpDataImport(String targetSystemCode, String topicCode, String businessId, List<Object> dataList) {
        SdpDataImportDto<Object> sdpDataImportDto = new SdpDataImportDto<>();
        // requestObj 下的 esbSerialNum 参数和 businessId 保持一致，后续在SDP日志平台方便查询
        sdpDataImportDto.setBusinessId(businessId);
        // 业务数据
        sdpDataImportDto.setParams(dataList);
        // 发起系统（PAM）
        sdpDataImportDto.setSystemCode("PAM");
        // 由SDP系统指定，如：ITEM
        sdpDataImportDto.setTopicCode(topicCode);
        // 签名
        sign(sdpDataImportDto, com.midea.sdp.constant.common.Constants.SIGN_METHOD_SHA256);
        // 是否回调（优先取输入配置中的是否回调信息）
        sdpDataImportDto.setCallbackEnabled(Boolean.TRUE);
        // 回调端点（优先取输入配置中的回调端点信息）
        sdpDataImportDto.setCallbackEndpoint(callbackUrl);
        // 回调类型,可用值:RabbitMQ,HTTP
        sdpDataImportDto.setCallbackType(SdpOutType.HTTP);
        String json = JSON.toJSONString(sdpDataImportDto);
        // 服务方paas提供API地址
        String paasApiUrl = apiurl + SDP_DATA_SERVICE;
        logger.info("SDP数据推送接口:{},请求参数:{}", paasApiUrl, json);
        // 发起请求
        try {
            String response = HttpClientUtil.doPost(paasApiUrl, json);
            logger.info("SDP数据推送接口返回结果:{}", getSort(response));
            SdpResult<ResponseObj> data = JSON.parseObject(response, new TypeReference<SdpResult<ResponseObj>>() {
            });
            return data;
        } catch (Exception e) {
            logger.error("流水号:{},SDP数据推送接口失败:", sdpDataImportDto.getBusinessId(), e);
            return SdpResult.error(e.getMessage());
        }
    }

    /**
     * 签名
     *
     * @param tradeImportDto
     * @param signMethod
     */
    private void sign(SdpDataTradeImportDto tradeImportDto, String signMethod) {
        tradeImportDto.setTimestamp(DateUtils.format(new Date()));
        tradeImportDto.setSignMethod(signMethod);
        Map<String, String> params = new HashMap<>(3);
        params.put("timestamp", tradeImportDto.getTimestamp());
        params.put("signMethod", tradeImportDto.getSignMethod());
        params.put("appKey", appKey);
        try {
            tradeImportDto.setSign(SignUtils.signTopRequest(params, appSecret, tradeImportDto.getSignMethod()));
        } catch (Exception e) {
            logger.error("sdp签名失败");
            throw new BizException(Code.ERROR, "sdp签名失败");
        }
    }

    /**
     * 数据推送内容签名
     *
     * @param sdpDataImportDto
     * @param signMethod
     */
    private void sign(SdpDataImportDto<Object> sdpDataImportDto, String signMethod) {
        sdpDataImportDto.setTimestamp(DateUtils.format(new Date()));
        sdpDataImportDto.setSignMethod(signMethod);
        Map<String, String> params = new HashMap<>(3);
        params.put("timestamp", sdpDataImportDto.getTimestamp());
        params.put("signMethod", sdpDataImportDto.getSignMethod());
        params.put("appKey", appKey);
        try {
            sdpDataImportDto.setSign(SignUtils.signTopRequest(params, appSecret, sdpDataImportDto.getSignMethod()));
        } catch (Exception e) {
            logger.error("SDP数据推送内容签名失败");
            throw new BizException(Code.ERROR, "SDP数据推送内容签名失败");
        }
    }

    private String getSort(String response) {
        if (response != null && response.length() > 2048) {
            return response.substring(0, 2048);
        }
        return response;
    }

    @Override
    public List<SdpTradeResultResponseEleDto> callSdpMassQuery(String pifaceCode, Map<String, String> requestParam) {
        EsbMassQueryRecord record = new EsbMassQueryRecord();
        Optional.ofNullable(pifaceCode).ifPresent(record::setBusinessType);
        record.setPushPlatformType(EsbMassQueryPushPlatformTypeEnum.SDP.getName());
        record.setNum(0L);
        record.setStatus(1);
        try {
            requestParam.put("sdpPIfaceCode", pifaceCode);
            requestParam.put("sdpPExtend","ALTER SESSION SET NLS_LANGUAGE='AMERICAN'");
            SdpTradeResult sdpTradeResult = callERPQuery2(requestParam);
            String businessId = requestParam.get(BUSINESSID_TRANSMIT_NAME);
            record.setEsbSerialNo(businessId);
            record.setErpIpP01(requestParam.get(EsbConstant.ERP_SDP_P01));
            record.setErpIpP02(requestParam.get(EsbConstant.ERP_SDP_P02));
            record.setErpIpP03(requestParam.get(EsbConstant.ERP_SDP_P03));
            record.setErpIpP04(requestParam.get(EsbConstant.ERP_SDP_P04));
            /** eg :
             *  sdpTradeResult == null 其他异常，callERPQuery返回null
             *  sdpTradeResult.getCode() 现在都为0, 防止不为0时返回空集合
             */
            if (sdpTradeResult == null || !"0".equals(sdpTradeResult.getCode())) {
                record.setStatus(2);
                record.setNum(0L);
                return null; // 查询异常
            }

            Object responseObj = sdpTradeResult.getResponseObj();

            /** eg :
             *  {
             *   "success": false,
             *   "code": "0",
             *   "message": "I/O error on POST",
             *   "responseObj": null
             * }
             */
            // 检查响应对象是否有效
            if (responseObj == null) {
                record.setStatus(2);
                record.setNum(0L);
                return null; // 查询异常
            }

            // 将响应对象解析为 DTO
            SdpTradeResultResponseDto responseDto = JSON.parseObject(
                    JSON.toJSONString(responseObj),
                    new TypeReference<SdpTradeResultResponseDto>() {
                    }
            );

            /** eg :
             * {
             *   "success": true,
             *   "code": "0",
             *   "message": "success",
             *   "responseObj": {
             *      "responsetype": "N",
             *      "responsecode": "000000",
             *      "responsemessage": "调用服务成功",
             *      "pkgXReturnTbl": [
             *          {
             *          "c1": "MA1157",
             *          "c2": "F70128.XC.."
             *          }
             *      ]
             *    }
             * }
             */
            // 检查响应 DTO 是否有效且包含数据
            if (responseDto != null && "000000".equals(responseDto.getResponsecode())) {
                record.setNum((long) responseDto.getPkgXReturnTbl().size());
                return responseDto.getPkgXReturnTbl();
            }
            /** eg :
             * {
             *   "success": true,
             *   "code": "0",
             *   "message": "success",
             *   "responseObj": {
             *     "responsetype": "W",
             *     "responsecode": "GERP600007",
             *     "responsemessage": "未查询到满足条件的记录"
             *   }
             * }
             */

        } catch (Exception e) {
            logger.error("流水号:{},SDP查询接口失败:", e);
            record.setStatus(2);
            record.setNum(0L);
        } finally {
            esbMassQueryRecordFeignClient.add(record);
        }
        return new ArrayList<>(); // 查询正常，但无数据
    }

    @Override
    public SdpTradeResult callERPQuery2(Map<String, String> requestParam) {
        return doSdpDataTrade("GERP", "GERPNEWMASSQUERY", requestParam);
    }

    @Override
    public SdpTradeResult doSdpDataTrade(String targetSystemCode, String topicCode, Map<String, String> requestParam) {
        SdpDataTradeImportDto tradeImportDto = new SdpDataTradeImportDto();
        tradeImportDto.setTargetSystemCode(targetSystemCode);
        tradeImportDto.setIsQuery(Boolean.TRUE);
        // SDP-MLS-GERPWIPTRANSATION-39221110011002
        String sequence = CacheDataUtils.generateSequence(Constants.CODE_PAM_CODE_LENGTH, targetSystemCode);
        String businessId = "SDP-PAM-" + topicCode + "-" + sequence;
        Optional.ofNullable(businessId).filter(StringUtils::isNotEmpty).ifPresent(s->requestParam.put(BUSINESSID_TRANSMIT_NAME, businessId));
        tradeImportDto.setBusinessId(businessId);
        requestParam.put("sdpPEsbSerialNum", tradeImportDto.getBusinessId());
        tradeImportDto.setRequestObj(requestParam);
        tradeImportDto.setSystemCode("PAM");
        tradeImportDto.setCallbackEnabled(Boolean.FALSE);
        tradeImportDto.setTopicCode(topicCode);
        sign(tradeImportDto);

        String json = JSON.toJSONString(tradeImportDto);
        // 服务方paas提供API地址
        String paasApiUrl = apiurl + SDP_SERVICE;
        logger.info("SDP查询接口:{},请求参数:{}", paasApiUrl, json);
        // 发起请求
        try {
            String response = HttpClientUtil.doPost(paasApiUrl, json);
            // 截取返回结果，防止日志过长，输出最长2048个中文字符
            logger.info("SDP查询接口返回结果:{}", getSort(response));
            SdpTradeResult data = JSON.parseObject(response, new TypeReference<SdpTradeResult>() {
            });
            return data;
        } catch (Exception e) {
            logger.error("流水号:{},SDP查询接口失败:", tradeImportDto.getBusinessId(), e);
            return null;
        }
    }

    private void sign(SdpDataTradeImportDto tradeImportDto) {
        tradeImportDto.setTimestamp(DateUtils.format(new Date()));
        tradeImportDto.setSignMethod(com.midea.sdp.constant.common.Constants.SIGN_METHOD_MD5);
        Map<String, String> params = new HashMap<>(3);
        params.put("timestamp", tradeImportDto.getTimestamp());
        params.put("signMethod", tradeImportDto.getSignMethod());
        params.put("appKey", appKey);
        try {
            tradeImportDto.setSign(SignUtils.signTopRequest(params, appSecret, tradeImportDto.getSignMethod()));
        } catch (Exception e) {
            logger.error("sdp签名失败");
            throw new BizException(Code.ERROR, "sdp签名失败");
        }
    }

}
