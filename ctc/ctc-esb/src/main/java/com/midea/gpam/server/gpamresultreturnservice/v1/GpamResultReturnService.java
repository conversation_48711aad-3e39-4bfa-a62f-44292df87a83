package com.midea.gpam.server.gpamresultreturnservice.v1;

import com.midea.esb.ReplyInformation;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;
import javax.xml.bind.annotation.XmlSeeAlso;

/**
 * This class was generated by Apache CXF 2.2.4
 * Tue Apr 02 15:46:36 CST 2019
 * Generated source version: 2.2.4
 */

@WebService(targetNamespace = "http://www.midea.com/gpam/GpamResultReturnService/v1", name = "GpamResultReturnService")
@XmlSeeAlso({com.midea.esb.ObjectFactory.class, ObjectFactory.class})
@SOAPBinding(parameterStyle = SOAPBinding.ParameterStyle.BARE)
public interface GpamResultReturnService {

    @WebMethod(action = "http://www.midea.com/gpam/GpamResultReturnService/v1/resultReturn")
    public void resultReturn(
            @WebParam(partName = "headerRequest", name = "requestHeader", targetNamespace = "http://www.midea.com/gpam/GpamResultReturnService/v1")
            com.midea.esb.RequestHeader headerRequest,
            @WebParam(partName = "input", name = "resultReturn", targetNamespace = "http://www.midea.com/gpam/GpamResultReturnService/v1")
            ResultReturn input,
            @WebParam(partName = "headOut", mode = WebParam.Mode.OUT, name = "replyInformation", targetNamespace = "http://www.midea.com/esb", header = true)
            javax.xml.ws.Holder<ReplyInformation> headOut
    );

}
