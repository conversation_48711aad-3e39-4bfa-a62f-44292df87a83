package com.midea.pam.ctc.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.pam.common.basedata.dto.GlPeriodDto;
import com.midea.pam.common.basedata.dto.OrganizationRelDto;
import com.midea.pam.common.basedata.entity.OperatingUnit;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.basedata.query.OrganizationRelQuery;
import com.midea.pam.common.ctc.dto.PaymentInvoiceDto;
import com.midea.pam.common.ctc.dto.PaymentRecordDto;
import com.midea.pam.common.ctc.dto.PaymentWriteOffRecordDto;
import com.midea.pam.common.ctc.entity.PaymentApply;
import com.midea.pam.common.ctc.entity.PaymentInvoice;
import com.midea.pam.common.ctc.entity.PaymentRecord;
import com.midea.pam.common.ctc.entity.PaymentRecordExample;
import com.midea.pam.common.ctc.entity.PaymentWriteOffCancelRecord;
import com.midea.pam.common.ctc.entity.PaymentWriteOffRecord;
import com.midea.pam.common.ctc.entity.PaymentWriteOffRecordExample;
import com.midea.pam.common.ctc.entity.PositiveNegativeInvoiceRecord;
import com.midea.pam.common.ctc.entity.PositiveNegativeInvoiceRecordExample;
import com.midea.pam.common.ctc.query.PaymentRecordQuery;
import com.midea.pam.common.enums.BusinessTypeEnums;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.GlPeriodType;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.common.util.Symbol;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.enums.DeletedFlagEnum;
import com.midea.pam.ctc.common.enums.PaymentRecordStatus;
import com.midea.pam.ctc.common.enums.PaymentWriteOffRecordCancelStatus;
import com.midea.pam.ctc.common.enums.PaymentWriteOffRecordStatus;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.contract.service.PaymentApplyService;
import com.midea.pam.ctc.mapper.PaymentInvoiceMapper;
import com.midea.pam.ctc.mapper.PaymentRecordExtMapper;
import com.midea.pam.ctc.mapper.PaymentRecordMapper;
import com.midea.pam.ctc.mapper.PaymentWriteOffCancelRecordMapper;
import com.midea.pam.ctc.mapper.PaymentWriteOffRecordMapper;
import com.midea.pam.ctc.ext.service.config.HandleDispatcher;
import com.midea.pam.ctc.mapper.PaymentInvoiceExtMapper;
import com.midea.pam.ctc.mapper.PositiveNegativeInvoiceRecordMapper;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.PaymentInvoiceService;
import com.midea.pam.ctc.service.PaymentRecordService;
import com.midea.pam.ctc.service.PaymentWriteOffRecordService;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.system.SystemContext;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.util.CollectionUtils;


public class PaymentRecordServiceImpl implements PaymentRecordService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());


    @Resource
    private PaymentInvoiceService paymentInvoiceService;

    @Resource
    private PaymentApplyService paymentApplyService;

    @Resource
    private PaymentRecordMapper paymentRecordMapper;

    @Resource
    private PaymentWriteOffRecordMapper paymentWriteOffRecordMapper;

    @Resource
    private PaymentRecordService paymentRecordService;

    @Resource
    private PaymentWriteOffRecordService writeOffRecordService;

    @Resource
    private PaymentRecordExtMapper paymentRecordExtMapper;

    @Resource
    private PaymentInvoiceMapper paymentInvoiceMapper;

    @Resource
    private PaymentWriteOffCancelRecordMapper paymentWriteOffCancelRecordMapper;

    @Resource
    private PositiveNegativeInvoiceRecordMapper positiveNegativeInvoiceRecordMapper;

    @Resource
    private PaymentInvoiceExtMapper paymentInvoiceExtMapper;

    @Resource
    private BasedataExtService basedataExtService;

    @Override
    public PageInfo<PaymentRecordDto> selectPage(PaymentRecordDto query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<PaymentRecordDto> list = selectList(query);
        PageInfo<PaymentRecordDto> page = BeanConverter.convertPage(list, PaymentRecordDto.class);
        return page;
    }

    @Override
    public List<PaymentRecordDto> selectList(PaymentRecordDto query){
        query.setPaymentStatusList(strToList(query.getPaymentStatusStr()));
        List<PaymentRecordDto> list = paymentRecordExtMapper.selectPage(query);
        for(PaymentRecordDto dto : list){
            packageDto(dto);
        }
        return list;
    }

    @Override
    public PaymentRecordDto findPaymentRecordById(Long id) {
        PaymentRecordDto result = null;
        if (id == null || id==0) {
            throw new BizException(ErrorCode.ID_NOT_NULL);
        }
        PaymentRecordDto query = new PaymentRecordDto();
        query.setId(id);
        List<PaymentRecordDto> list = paymentRecordExtMapper.selectPage(query);
        if(list==null || list.size()==0){
            throw new BizException(ErrorCode.CTC_PAYMENT_PLAN_CODE_EXIST);
        }
        result = list.get(0);
        packageDto(result);

        //查询核销记录
        PaymentWriteOffRecordDto writeOffRecordDto = new PaymentWriteOffRecordDto();
        writeOffRecordDto.setPaymentRecordId(id);
        List<PaymentWriteOffRecordDto> writeOffRecordDtos = writeOffRecordService.selectList(writeOffRecordDto);
        for(PaymentWriteOffRecordDto dto : writeOffRecordDtos){
            PaymentInvoice invoice = paymentInvoiceMapper.selectByPrimaryKey(dto.getPaymentInvoiceId());
            if(invoice != null){
                dto.setSurplusAmount(invoice.getSurplusAmount());//查询发票可用金额
                dto.setInvoicePaymentApplyCode(invoice.getPaymentApplyCode());//查询发票的付款申请编号
                dto.setInvoiceStatus(invoice.getStatus());//发票状态
                //核销记录ERP同步状态，取值规则：cancelStatus为空取status，否则取cancelStatus
                if(StringUtils.isEmpty(dto.getCancelStatus())){
                    dto.setWriteOffStatus(dto.getStatus());
                } else {
                    dto.setWriteOffStatus(dto.getCancelStatus());
                }
            }
        }
        result.setWriteOffRecordList(writeOffRecordDtos);
        return result;
    }

    @Override
    public PaymentRecordDto findPositiveAndNegativeInvoice(Long id) {
        PaymentRecordDto result = null;
        if (Objects.isNull(id) || Objects.equals(0,id)) {
            throw new BizException(ErrorCode.ID_NOT_NULL);
        }
        List<PaymentRecordDto> list = paymentRecordExtMapper.selectPositiveAndNegativeInvoice(id);
        if(CollectionUtils.isEmpty(list)){
            throw new MipException("负数发票不存在");
        }
        result = list.get(0);
        result.setPayType("负数发票");
        UserInfo userInfo = CacheDataUtils.findUserById(result.getCreateBy());
        if (userInfo != null) {
            result.setInvoiceCreateBy(userInfo.getName());
            result.setInvoiceCreateByName(userInfo.getName());
        }
        //查询正负发票的核销记录-neg_payment_invoice_id
        PositiveNegativeInvoiceRecordExample example = new PositiveNegativeInvoiceRecordExample();
        example.createCriteria().andNegPaymentInvoiceIdEqualTo(id).andDeletedFlagEqualTo(false);
        List<PositiveNegativeInvoiceRecord> invoiceRecords = positiveNegativeInvoiceRecordMapper.selectByExample(example);
        if(!CollectionUtils.isEmpty(invoiceRecords)){
            List<PaymentWriteOffRecordDto> writeOffRecordDtos = new ArrayList<>();
            BigDecimal writeOffAmount = BigDecimal.ZERO;
            for (PositiveNegativeInvoiceRecord invoiceRecord : invoiceRecords) {
                //查对应的正数发票
                PaymentInvoice invoice = paymentInvoiceMapper.selectByPrimaryKey(invoiceRecord.getPosPaymentInvoiceId());
                PaymentWriteOffRecordDto dto = new PaymentWriteOffRecordDto();
                dto.setSurplusAmount(invoice.getSurplusAmount());
                dto.setTotalInvoiceIncludedPrice(invoice.getTotalInvoiceIncludedPrice());
                dto.setInvoiceStatus(invoice.getStatus()); //发票状态(0草稿/1审批中/2审批通过/3/驳回/4撤回/5作废)
                dto.setWriteOffStatus(invoiceRecord.getSyncStatus());//核销记录ERP同步状态(-1同步失败，0-未同步，1同步中，2已同步，3撤销中，4已撤销，5撤销失败)
                dto.setId(invoiceRecord.getId());
                dto.setPaymentRecordId(invoiceRecord.getPaymentRecordId());
                dto.setPaymentApplyId(invoiceRecord.getPaymentApplyId());
                dto.setPaymentApplyCode(invoiceRecord.getPaymentApplyCode());
                dto.setPaymentInvoiceId(invoiceRecord.getPosPaymentInvoiceId());
                dto.setApInvoiceCode(invoiceRecord.getPosApInvoiceCode());
                dto.setAmount(invoiceRecord.getAmount());
                dto.setStatus(invoiceRecord.getSyncStatus());
                dto.setSyncMessage(invoiceRecord.getSyncMessage());
                dto.setHappenDate(invoiceRecord.getHappenDate());
                dto.setDeletedFlag(invoiceRecord.getDeletedFlag());
                dto.setVersion(invoiceRecord.getVersion());
                dto.setCreateBy(invoiceRecord.getCreateBy());
                dto.setCreateAt(invoiceRecord.getCreateAt());
                dto.setUpdateBy(invoiceRecord.getUpdateBy());
                dto.setUpdateAt(invoiceRecord.getUpdateAt());
                writeOffRecordDtos.add(dto);
                //累计核销金额
                writeOffAmount = writeOffAmount.add(invoiceRecord.getAmount());

            }
            result.setWriteOffAmount(writeOffAmount);
            result.setWriteOffRecordList(writeOffRecordDtos);
            long count = invoiceRecords.stream().filter(x -> Objects.equals(x.getSyncStatus(), -1)).count();
            logger.info("同步失败的计数为:{}",count);
            if (count > 0){
                result.setSyncStatus(-1);
            }else {
                result.setSyncStatus(1);
            }
        }else {
            //同步状态：-1-异常;1-无异常
            result.setWriteOffRecordList(new ArrayList<>());
            result.setWriteOffAmount(BigDecimal.ZERO);
            result.setSyncStatus(1);
        }
        return result;
    }

    private void packageDto(PaymentRecordDto dto) {
        Long ouId = dto.getOuId();//业务实体ID
        if(ouId!=null && ouId>0) {
            OperatingUnit operatingUnit = CacheDataUtils.findOuById(ouId);
            if(operatingUnit!=null) {
                dto.setOuName(operatingUnit.getOperatingUnitName());//业务实体名称
            }
        }
        if (StringUtils.isNotEmpty(dto.getInvoiceCreateBy())){
            UserInfo createBy = CacheDataUtils.findUserById(Long.valueOf(dto.getInvoiceCreateBy()));
            dto.setInvoiceCreateByName(createBy != null ? createBy.getName() : null);//发票创建人
        }
        PaymentWriteOffRecordExample example = new PaymentWriteOffRecordExample();
        //同步失败或撤销失败的核销记录
//        example.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE)
//                .andPaymentRecordIdEqualTo(dto.getId()).andStatusEqualTo(PaymentWriteOffRecordStatus.SYNCHRONIZATION_FAILED.code());
        final PaymentWriteOffRecordExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        criteria.andPaymentRecordIdEqualTo(dto.getId());
        criteria.andStatusEqualTo(PaymentWriteOffRecordStatus.SYNCHRONIZATION_FAILED.code());

        final PaymentWriteOffRecordExample.Criteria or1 = example.or();
        or1.andDeletedFlagEqualTo(Boolean.FALSE);
        or1.andPaymentRecordIdEqualTo(dto.getId());
        or1.andCancelStatusEqualTo(PaymentWriteOffRecordCancelStatus.FAIL.code());

        final PaymentWriteOffRecordExample.Criteria or2 = example.or();
        or2.andDeletedFlagEqualTo(Boolean.FALSE);
        or2.andPaymentRecordIdEqualTo(dto.getId());
        or2.andSyncMessageEqualTo("请联系IT查询记录");

        Long countFail = paymentWriteOffRecordMapper.countByExample(example);
        //同步状态：-1-异常;1-无异常
        if((countFail != null && countFail > 0)){
            dto.setSyncStatus(-1);
        }else {
            dto.setSyncStatus(1);
        }
    }

    @Override
    public List<PaymentRecord> selectByExample(PaymentRecordExample example) {
        return paymentRecordMapper.selectByExample(example);
    }

    @Override
    public int save(PaymentRecord paymentRecord) {
        logger.info("预付款核销,预付款撤销核销,删除核销,调用:{}", JSON.toJSONString(paymentRecord));
        return paymentRecordMapper.insert(paymentRecord);
    }

    @Override
    public PaymentRecord findById(Long id) {
        return paymentRecordMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<PaymentRecord> findByContractId(Long contractId) {
        PaymentRecordExample example = new PaymentRecordExample();
        final PaymentRecordExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(DeletedFlagEnum.VALID.code());
        criteria.andPurchaseContractIdEqualTo(contractId);
        final List<PaymentRecord> paymentRecords = selectByExample(example);

        return paymentRecords == null ? Lists.newArrayList() : paymentRecords;
    }

    @Override
    public Boolean positiveAndNegativeInvoiceCallBack(Long recordId, boolean status, String msg) {
        PositiveNegativeInvoiceRecord record = positiveNegativeInvoiceRecordMapper.selectByPrimaryKey(recordId);
        if(record == null){
            logger.info("回调不是正负发票核销,程序结束");
            return false;
        }else {
            logger.info("程序是正负发票核销");
            PositiveNegativeInvoiceRecord updateRecord = new PositiveNegativeInvoiceRecord();
            if (status){ //同步成功
                updateRecord.setSyncStatus(PaymentWriteOffRecordStatus.SYNCED.code());
            }else {
                //同步失败
                updateRecord.setSyncStatus(PaymentWriteOffRecordStatus.SYNCHRONIZATION_FAILED.code());
            }
            updateRecord.setSyncMessage(msg);
            updateRecord.setUpdateBy(SystemContext.getUserId());
            updateRecord.setUpdateAt(new Date());
            updateRecord.setId(recordId);
            positiveNegativeInvoiceRecordMapper.updateByPrimaryKeySelective(updateRecord);


            return true;
        }



    }

    /**
     * 正负发票核销
     * @param recordId 负数发票ID
     * @return
     */
    @Override
    public String positiveAndNegativeInvoiceWriteOff(Long recordId) {
        //查询所有符合要求的负数发票
        List<Long> list = new ArrayList<>();
        System.out.println(recordId);
        if(Objects.nonNull(recordId)){
            list.add(recordId);
        }
        logger.info("集合为:{}",JSON.toJSONString(list));
        //查询所有的负数发票
        //可用余额小于0的负数发票
        List<PaymentInvoiceDto> paymentInvoiceDtos = paymentInvoiceExtMapper.selectInvoiceListByIds(Objects.isNull(recordId) ? null : list);
        Guard.notNullOrEmpty(paymentInvoiceDtos, "未核销和部分核销的负数发票记录不存在");
        logger.info("positiveAndNegativeInvoiceWriteOff :{}",paymentInvoiceDtos.size());
        for (PaymentInvoiceDto paymentInvoiceDto : paymentInvoiceDtos) {
            //当前张发票的可核销金额,负数
            BigDecimal ableWriteOffAmount = paymentInvoiceDto.getSurplusAmount();
            //当前负数发票的实际支付金额 erp实际已付款额
            BigDecimal totalPayIncludedPrice = paymentInvoiceDto.getTotalPayIncludedPrice();
            //查询跟这张发票关联的正数发票,四个维度
            //即与负数发票：采购合同号相同、供应商编码相同、发票币种相同、的正数发票,这里其实要求金额大于0的
            Long purchaseContractId = paymentInvoiceDto.getPurchaseContractId();//采购合同编号
            Long vendorId = paymentInvoiceDto.getVendorId();//供应商ID-表主键
            String currency = paymentInvoiceDto.getCurrency();// 币种
            List<PaymentInvoiceDto> posInvoices = paymentInvoiceExtMapper.selectWriteOffInvoice(purchaseContractId, vendorId, currency,null);
            if (CollectionUtils.isEmpty(posInvoices)){
                logger.info("采购合同id：{}对应的发票信息不存在", purchaseContractId);
                continue;
            }
            //可用余额大于0,未被引用的票
            List<PaymentInvoiceDto> dtoList = posInvoices.stream().filter(x -> x.getSurplusAmount().compareTo(BigDecimal.ZERO) > 0
                    && Objects.equals(1,x.getAvailable()))
                    .sorted(Comparator.comparing(PaymentInvoiceDto::getGlDate,Comparator.nullsFirst(Comparator.naturalOrder())))
                    .collect(Collectors.toList());
            logger.info("获取处理后的正数发票集合:{}",JSON.toJSONString(dtoList));
            //如果有多张发票的话,还需要根据GL时间排序
            for (PaymentInvoiceDto posInvoice : dtoList) {
                if(ableWriteOffAmount.compareTo(BigDecimal.ZERO) >= 0){ //负数发票的可用额度等于0
                    logger.info("当前负数发票的可用额度以消耗完,为:{},跳出循环",ableWriteOffAmount);
                    continue;
                }
                BigDecimal surplusAmount = posInvoice.getSurplusAmount(); //剩余可用余额,正数
                if(surplusAmount.compareTo(BigDecimal.ZERO) == 0){//可用余额等于0
                    logger.info("该正数发票: {}无剩余可用额度可供核销",posInvoice.getApInvoiceCode());
                    continue;
                }
                Date date = setWriteOffDay(paymentInvoiceDto.getOuId());
                if (Objects.isNull(date)) {
                    logger.info("负数发票:{}核销日期为空",paymentInvoiceDto.getApInvoiceCode());
                    continue;
                }
                BigDecimal hx;//当前这张票的核销金额,正数
                if(surplusAmount.compareTo(ableWriteOffAmount.multiply(new BigDecimal(-1))) >= 0 ){
                    //正数发票的可用额度大于或等于可以核销的金额
                    hx = ableWriteOffAmount.multiply(new BigDecimal(-1));
                }else {
                    //正数发票的可用额度小于可核销金额
                    hx = surplusAmount;
                }
                logger.info("该正数发票: {}核销额度为: {}",posInvoice.getApInvoiceCode(),hx);

                //更新正数发票的额度
                //剩余的可用发票额度,正数
                PaymentInvoice updatePaymentInvoice = new PaymentInvoice();
                updatePaymentInvoice.setId(posInvoice.getId());
                BigDecimal remainSurplusAmount = surplusAmount.subtract(hx);
                updatePaymentInvoice.setSurplusAmount(remainSurplusAmount);
                //erp实际已付款额
                updatePaymentInvoice.setTotalPayIncludedPrice(Optional.ofNullable(posInvoice.getTotalPayIncludedPrice()).orElse(BigDecimal.ZERO).add(hx));
                updatePaymentInvoice.setUpdateAt(new Date());
                updatePaymentInvoice.setUpdateBy(SystemContext.getUserId());
                paymentInvoiceMapper.updateByPrimaryKeySelective(updatePaymentInvoice);

                //添加核销记录
                PositiveNegativeInvoiceRecord record = new PositiveNegativeInvoiceRecord();
                record.setNegPaymentInvoiceId(paymentInvoiceDto.getId());//负数发票
                record.setNegApInvoiceCode(paymentInvoiceDto.getApInvoiceCode());//负数发票编号
                //record.setPaymentRecordId();
                record.setPaymentApplyId(posInvoice.getPaymentApplyId());
                record.setPaymentApplyCode(posInvoice.getPaymentApplyCode());
                record.setPosPaymentInvoiceId(posInvoice.getId());//正数发票
                record.setPosApInvoiceCode(posInvoice.getApInvoiceCode());//正数发票
                record.setAmount(hx);//将核销的金额换成负数
                record.setSyncStatus(PaymentWriteOffRecordStatus.SYNCHRONIZING.code());
                record.setHappenDate(date);//设定核销日期
                //record.setSyncMessage("");
                record.setDeletedFlag(DeletedFlag.VALID.code());
                record.setCreateBy(SystemContext.getUserId());
                record.setCreateAt(new Date());
                //record.setUpdateBy(0L);
                //record.setUpdateAt(new Date());
                positiveNegativeInvoiceRecordMapper.insert(record);
                logger.info("获取的待添加的数据为:{}", JSON.toJSONString(record));
                //正负发票核销记录写入
                HandleDispatcher.route(BusinessTypeEnums.POSITIVE_AND_NEGATIVE_RECORD.getCode(), String.valueOf(record.getId()), null, null, false);

                //更新负数发票的剩余可用额度
                ableWriteOffAmount = ableWriteOffAmount.add(hx);
                //更新实际 erp实际已付款额
                totalPayIncludedPrice = Optional.ofNullable(totalPayIncludedPrice).orElse(BigDecimal.ZERO).subtract(hx);
            }

            //更新负数发票
            PaymentInvoice updateNegPaymentInvoice = new PaymentInvoice();
            updateNegPaymentInvoice.setId(paymentInvoiceDto.getId());
            updateNegPaymentInvoice.setSurplusAmount(ableWriteOffAmount);
            updateNegPaymentInvoice.setTotalPayIncludedPrice(totalPayIncludedPrice);
            updateNegPaymentInvoice.setUpdateAt(new Date());
            updateNegPaymentInvoice.setUpdateBy(SystemContext.getUserId());
            paymentInvoiceMapper.updateByPrimaryKeySelective(updateNegPaymentInvoice);
        }

        return "success";
    }

    private Date setWriteOffDay(Long ouId) {
        OrganizationRelQuery query = new OrganizationRelQuery();
        query.setOperatingUnitId(ouId);
        List<OrganizationRelDto> organizationRelList = basedataExtService.getOrganizationRel(query);
        if (ListUtils.isEmpty(organizationRelList)) {
            throw new BizException(Code.ERROR, "OU对应的ERP组织关系不存在");
        }
        OrganizationRelDto organizationRelDto = organizationRelList.get(0);
        Long ledgerId = organizationRelDto.getLedgerId();
        List<GlPeriodDto> glPeriodDtoList = basedataExtService.getGlPeriod(ledgerId, GlPeriodType.PAYMENT_PERIOD.getCode().toString(),
                DateUtils.format(new Date(), "yyyy-MM"));
        List<GlPeriodDto> glPeriodOpenList = basedataExtService.getGlPeriod2(ledgerId, GlPeriodType.PAYMENT_PERIOD.getCode().toString(), "O");

        Date happenDate = null;
        GlPeriodDto glPeriodOpen = null;
        if (ListUtils.isNotEmpty(glPeriodOpenList)) {
            glPeriodOpenList.sort(Comparator.comparing(GlPeriodDto::getEndDate).reversed());
            glPeriodOpen = glPeriodOpenList.get(0);
        }
        if (ListUtils.isNotEmpty(glPeriodDtoList)) {
            GlPeriodDto dto = glPeriodDtoList.get(0);
            String status = dto.getClosingStatus();
            Date startDate = dto.getStartDate();
            Date endDate = dto.getEndDate();
            if ((new Date()).after(startDate) && (new Date()).before(DateUtils.addDay(endDate, 1)) && "O".equals(status)) {
                happenDate = DateUtils.parse(DateUtils.format(new Date(), "yyyy-MM-dd 00:00:00"));
            } else {
                happenDate = Optional.ofNullable(glPeriodOpen).map(GlPeriodDto::getEndDate).orElse(null);
            }
        }
        return happenDate;
    }

    private List<String> strToList(String waitChange){
        if (com.midea.pam.common.util.StringUtils.isNotEmpty(waitChange)) {
            List<String> list = Arrays.asList(waitChange.split(Symbol.COMMA));
            return list;
        }
        return null;
    }

    @Override
    public PaymentRecord add(PaymentRecord dto) {
        paymentRecordMapper.insert(dto);
        return dto;
    }

    @Override
    public PaymentRecord update(PaymentRecord dto) {
        paymentRecordMapper.updateByPrimaryKey(dto);
        return dto;
    }

    @Override
    public PaymentRecord save(PaymentRecord dto, Long userBy) {
        if(dto.getId()!=null && dto.getId()>0){
            this.update(dto);
        }else{
            this.add(dto);
        }
        return dto;
    }

    @Override
    public PaymentRecord getById(Long id) {
        return paymentRecordMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<PaymentRecord> selectList(PaymentRecordQuery query) {
        List<PaymentRecord> relList = paymentRecordMapper.selectByExample(buildExample(query));
        return relList;
    }

    @Override
    public PaymentRecord getByApplyIdAndInvoiceCode(Long applyId, String invoiceCode) {
        PaymentRecord query = new PaymentRecord();
        query.setPaymentApplyId(applyId);
        query.setApInvoiceCode(invoiceCode);
        List<PaymentRecord> relList = selectList(query);
        if(null != relList && relList.size() > 0){
            return relList.get(0);
        }
        return null;
    }

    /**
     * 核销失败处理逻辑
     * @param record
     * @param responMsg
     * @return
     */
    @Override
    public PaymentRecord dealFailPrepay(PaymentWriteOffRecord writeOffRecord, PaymentRecord record, String responMsg) {
        //更新记录状态
        writeOffRecord.setStatus(PaymentWriteOffRecordStatus.SYNCHRONIZATION_FAILED.code());//同步失败
        writeOffRecord.setSyncMessage(responMsg);
        paymentWriteOffRecordMapper.updateByPrimaryKeySelective(writeOffRecord);

        record.setPaymentMsg(responMsg);
        paymentRecordService.save(record, SystemContext.getUserId());

        /** 迭代220415 功能调整为 同步成功与否不影响核销结果 **/
//        //恢复付款记录核销金额和状态
//        record.setWriteOffAmount(record.getWriteOffAmount().subtract(writeOffRecord.getAmount()));
//        //恢复发票占用金额
//        PaymentInvoiceDto invoiceDto = paymentInvoiceService.getById(writeOffRecord.getPaymentInvoiceId());
//        invoiceDto.setSurplusAmount(invoiceDto.getSurplusAmount().add(writeOffRecord.getAmount()));
//        invoiceDto.setTotalPayIncludedPrice(invoiceDto.getTotalPayIncludedPrice().subtract(writeOffRecord.getAmount()));
//        paymentInvoiceService.update(invoiceDto);
        return record;
    }

    /**
     * 撤销核销失败处理逻辑
     * @param record
     * @param responMsg
     * @return
     */
    @Override
    public PaymentRecord dealFailCancelPrepay(PaymentWriteOffCancelRecord writeOffCancelRecord, PaymentWriteOffRecord writeOffRecord, PaymentRecord record, String responMsg) {
        //更新记录状态
        writeOffRecord.setCancelStatus(PaymentWriteOffRecordCancelStatus.FAIL.code());//撤销失败
        writeOffRecord.setSyncMessage(responMsg);
        paymentWriteOffRecordMapper.updateByPrimaryKeySelective(writeOffRecord);

        record.setPaymentMsg(responMsg);
        paymentRecordService.save(record, SystemContext.getUserId());

        writeOffCancelRecord.setCancelStatus(PaymentWriteOffRecordCancelStatus.FAIL.code());//撤销失败
        writeOffCancelRecord.setSyncMessage(responMsg);
        paymentWriteOffCancelRecordMapper.updateByPrimaryKeySelective(writeOffCancelRecord);

        /** 迭代220415 功能调整为 同步成功与否不影响核销结果 **/
//        //恢复付款记录核销金额和状态
//        record.setWriteOffAmount(record.getWriteOffAmount().subtract(writeOffRecord.getAmount()));
//        //恢复发票占用金额
//        PaymentInvoiceDto invoiceDto = paymentInvoiceService.getById(writeOffRecord.getPaymentInvoiceId());
//        invoiceDto.setSurplusAmount(invoiceDto.getSurplusAmount().add(writeOffRecord.getAmount()));
//        invoiceDto.setTotalPayIncludedPrice(invoiceDto.getTotalPayIncludedPrice().subtract(writeOffRecord.getAmount()));
//        paymentInvoiceService.update(invoiceDto);
        return record;
    }

    /**
     * PAM-ERP-025 预付款核销 回调
     * @param id
     * @param isSuccess
     * @param msg
     * @param actualCost
     * @return
     */
    @Transactional
    @Override
    public Boolean prepayCallBack(Long id, Boolean isSuccess, String msg, String actualCost) {
        if (StringUtils.isEmpty(id)) {
            return null;
        }
        //查询核销记录表
        PaymentWriteOffRecord writeOffRecord = paymentWriteOffRecordMapper.selectByPrimaryKey(id);
        if(null == writeOffRecord){
            throw new BizException(Code.ERROR, "核销记录不能为空");
        }
        //付款记录
        PaymentRecord record = paymentRecordService.getById(writeOffRecord.getPaymentRecordId());
        if(null == record){
            throw new BizException(Code.ERROR, "付款记录不能为空");
        }
        //付款申请单
        PaymentApply paymentApply = paymentApplyService.findById(record.getPaymentApplyId());
        if(null == paymentApply){
            throw new BizException(Code.ERROR, "付款申请单不能为空");
        }
        if (isSuccess) {
            //更新核销记录
            writeOffRecord.setStatus(PaymentWriteOffRecordStatus.SYNCED.code());
            writeOffRecord.setSyncMessage(msg);
            paymentWriteOffRecordMapper.updateByPrimaryKeySelective(writeOffRecord);

            /** 迭代220415 功能调整为 同步成功与否不影响核销结果，以下逻辑转移到了 新增核销记录时 **/
            //修改付款记录核销状态--部分核销
//            record.setPaymentStatus(PaymentRecordStatus.PART.code());
//            //判断付款记录是否已经核销结束
//            if(isPrepayAllForRecord(record)){
//                record.setPaymentStatus(PaymentRecordStatus.DONE.code());
//            }
//            //修改预付款申请记录为部分核销
//            paymentApply.setWriteOffStatus(2);
//            //判断预付款是否完全核销
//            if(isPrepayAllForApply(paymentApply)){
//                paymentApply.setWriteOffStatus(3);
//            }
//            paymentApplyService.update(paymentApply);
            record.setPaymentMsg(msg);
            paymentRecordService.save(record, SystemContext.getUserId());

        } else {
            dealFailPrepay(writeOffRecord, record, msg);
        }
        return null;
    }

    /**
     * PAM-ERP-025-1 预付款撤销核销 回调
     * @param id
     * @param isSuccess
     * @param msg
     * @param actualCost
     * @return
     */
    @Transactional
    @Override
    public Boolean cancelPrepayCallBack(Long id, Boolean isSuccess, String msg, String actualCost) {
        if (StringUtils.isEmpty(id)) {
            return null;
        }
        //查询撤销核销记录表
        PaymentWriteOffCancelRecord writeOffCancelRecord = paymentWriteOffCancelRecordMapper.selectByPrimaryKey(id);
        if(null == writeOffCancelRecord){
            throw new BizException(Code.ERROR, "核销核销记录不能为空");
        }
        //查询核销记录表
        PaymentWriteOffRecord writeOffRecord = paymentWriteOffRecordMapper.selectByPrimaryKey(writeOffCancelRecord.getPaymentWriteOffRecordId());
        if(null == writeOffRecord){
            throw new BizException(Code.ERROR, "核销记录不能为空");
        }
        //付款记录
        PaymentRecord record = paymentRecordService.getById(writeOffRecord.getPaymentRecordId());
        if(null == record){
            throw new BizException(Code.ERROR, "付款记录不能为空");
        }
        //付款申请单
        PaymentApply paymentApply = paymentApplyService.findById(record.getPaymentApplyId());
        if(null == paymentApply){
            throw new BizException(Code.ERROR, "付款申请单不能为空");
        }
        if (isSuccess) {
            //恢复发票占用金额
            PaymentInvoiceDto invoiceDto = paymentInvoiceService.getById(writeOffRecord.getPaymentInvoiceId());
            invoiceDto.setSurplusAmount(invoiceDto.getSurplusAmount().add(writeOffRecord.getAmount()));
            invoiceDto.setTotalPayIncludedPrice(invoiceDto.getTotalPayIncludedPrice().subtract(writeOffRecord.getAmount()));
            paymentInvoiceService.update(invoiceDto);

            //恢复付款记录核销金额和状态，核销状态(0未核销/3部分核销/4已核销)
            record.setWriteOffAmount(record.getWriteOffAmount().subtract(writeOffRecord.getAmount()));
            if(record.getWriteOffAmount().compareTo(BigDecimal.ZERO) == 0){
                record.setPaymentStatus(PaymentRecordStatus.NOT.code());//未核销
            } else if(record.getWriteOffAmount().compareTo(BigDecimal.ZERO) > 0 && record.getWriteOffAmount().compareTo(record.getAmount()) < 0){
                record.setPaymentStatus(PaymentRecordStatus.PART.code());//部分核销
            } else if(record.getWriteOffAmount().compareTo(record.getAmount()) == 0){
                record.setPaymentStatus(PaymentRecordStatus.DONE.code());//已核销
            }

            //判断付款记录是否已经核销结束
            if(isPrepayAllForRecord(record)){
                record.setPaymentStatus(PaymentRecordStatus.DONE.code());
            }

            //更新撤销状态
            writeOffRecord.setCancelStatus(PaymentWriteOffRecordCancelStatus.SUCCESS.code());//撤销成功
            writeOffRecord.setSyncMessage(msg);
            paymentWriteOffRecordMapper.updateByPrimaryKeySelective(writeOffRecord);

            writeOffCancelRecord.setCancelStatus(PaymentWriteOffRecordCancelStatus.SUCCESS.code());//撤销成功
            writeOffCancelRecord.setSyncMessage(msg);
            paymentWriteOffCancelRecordMapper.updateByPrimaryKeySelective(writeOffCancelRecord);

            /** 迭代220415 功能调整为 同步成功与否不影响核销结果，以下逻辑转移到了 新增核销记录时 **/
            //修改付款记录核销状态--部分核销
//            record.setPaymentStatus(PaymentRecordStatus.PART.code());
//            //判断付款记录是否已经核销结束
//            if(isPrepayAllForRecord(record)){
//                record.setPaymentStatus(PaymentRecordStatus.DONE.code());
//            }
//            //修改预付款申请记录为部分核销
//            paymentApply.setWriteOffStatus(2);
//            //判断预付款是否完全核销
//            if(isPrepayAllForApply(paymentApply)){
//                paymentApply.setWriteOffStatus(3);
//            }
//            paymentApplyService.update(paymentApply);
            record.setPaymentMsg(msg);
            paymentRecordService.save(record, SystemContext.getUserId());

        } else {
            dealFailCancelPrepay(writeOffCancelRecord, writeOffRecord, record, msg);
        }
        return null;
    }

    /**
     * 判断预付款申请是否已经完全核销
     * @param paymentApply
     * @return
     */
//    public Boolean isPrepayAllForApply(PaymentApply paymentApply){
//        if(null == paymentApply){
//            return false;
//        }
//        if(!PaymentApplyGcebErpStatus.SUCCESS.getCode(
//
//        ).equals(paymentApply.getGcebStatus())){
//            return false;
//        }
//        //根据预付款查询付款记录
//        PaymentRecordExample recordExample = new PaymentRecordExample();
//        recordExample.createCriteria().andDeletedFlagEqualTo(0).andPaymentApplyIdEqualTo(paymentApply.getId());
//        List<PaymentRecord> recordList = paymentRecordMapper.selectByExample(recordExample);
//        BigDecimal totalPrepayAmount = BigDecimal.valueOf(0.00);
//        for(PaymentRecord record : recordList){
//            if("1".equals(record.getPaymentStatus())){
//                totalPrepayAmount.add(record.getWriteOffAmount());
//            }
//        }
//        return totalPrepayAmount.compareTo(paymentApply.getReallyPayIncludedPrice()) == 0;
//    }

    /**
     * 判断付款记录是否已经完全核销
     * @param record
     * @return
     */
    public Boolean isPrepayAllForRecord(PaymentRecord record){
        if(null == record){
            return false;
        }
        //根据所有核销记录
        PaymentWriteOffRecordExample writeOffRecordExample = new PaymentWriteOffRecordExample();
        writeOffRecordExample.createCriteria().andDeletedFlagEqualTo(DeletedFlag.VALID.code())
                .andPaymentRecordIdEqualTo(record.getId())
//                .andStatusEqualTo(PaymentWriteOffRecordStatus.SYNCED.code())
                .andCancelDateIsNull();
        List<PaymentWriteOffRecord> writeOffRecords = paymentWriteOffRecordMapper.selectByExample(writeOffRecordExample);
        BigDecimal totalPrepayAmount = BigDecimal.valueOf(0.00);
        for(PaymentWriteOffRecord writeOffRecord : writeOffRecords){
            totalPrepayAmount.add(writeOffRecord.getAmount());
        }
        return totalPrepayAmount.compareTo(record.getAmount()) == 0;
    }

    @Override
    public List<PaymentRecord> selectList(PaymentRecord query) {
        List<PaymentRecord> relList = paymentRecordMapper.selectByExample(buildExample(query));
        return relList;
    }

    @Override
    public PageInfo<PaymentRecord> selectPage(PaymentRecord query) {
        return null;
    }

    public PaymentRecordExample buildExample(PaymentRecord query){
        PaymentRecordExample example = new PaymentRecordExample();
        PaymentRecordExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(0);
        if(null != query.getId()){
            criteria.andIdEqualTo(query.getId());
        }
        if(null != query.getPaymentApplyId()){
            criteria.andPaymentApplyIdEqualTo(query.getPaymentApplyId());
        }
        if(null != query.getApInvoiceCode()){
            criteria.andApInvoiceCodeEqualTo(query.getApInvoiceCode());
        }
        return example;
    }

    public PaymentRecordExample buildExample(PaymentRecordQuery query){
        PaymentRecordExample example = new PaymentRecordExample();
        PaymentRecordExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(0);

        if(null != query.getId()){
            criteria.andIdEqualTo(query.getId());
        }
        if(null != query.getPaymentApplyId()){
            criteria.andPaymentApplyIdEqualTo(query.getPaymentApplyId());
        }
        if(null != query.getApInvoiceCode()){
            criteria.andApInvoiceCodeEqualTo(query.getApInvoiceCode());
        }
        if(null != query.getPaymentStatusList() && query.getPaymentStatusList().size()>0){
            criteria.andPaymentStatusIn(query.getPaymentStatusList());
        }
        return example;
    }
}
