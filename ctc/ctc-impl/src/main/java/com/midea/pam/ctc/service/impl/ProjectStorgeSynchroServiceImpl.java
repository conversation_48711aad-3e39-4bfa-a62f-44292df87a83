package com.midea.pam.ctc.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.mcomponent.core.util.Assert;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.EsbResponse;
import com.midea.pam.common.basedata.entity.StorageInventory;
import com.midea.pam.common.ctc.dto.ProjectProfitDto;
import com.midea.pam.common.ctc.entity.ProjectStorgeSynchro;
import com.midea.pam.common.ctc.entity.ProjectStorgeSynchroExample;
import com.midea.pam.common.ctc.entity.ResendExecute;
import com.midea.pam.common.enums.CommonStatus;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.ext.service.impl.AbstractCommonBusinessService;
import com.midea.pam.ctc.mapper.ProjectStorgeSynchroMapper;
import com.midea.pam.ctc.service.EsbService;
import com.midea.pam.ctc.service.ProjectProfitService;
import com.midea.pam.ctc.service.ProjectStorgeSynchroService;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ProjectStorgeSynchroServiceImpl extends AbstractCommonBusinessService<ProjectProfitDto> implements ProjectStorgeSynchroService{
    @Resource
    private ProjectStorgeSynchroMapper projectStorgeSynchroMapper;
    @Resource
    private ProjectProfitService projectProfitService;
    @Resource
    private EsbService esbService;
    @Resource
    private RestTemplate restTemplate;

    @Override
    public int insert(ProjectStorgeSynchro record) {
        return projectStorgeSynchroMapper.insert(record);
    }

    @Override
    public int insertSelective(ProjectStorgeSynchro record) {
        return projectStorgeSynchroMapper.insertSelective(record);
    }

    @Override
    public List<ProjectStorgeSynchro> selectByExample(ProjectStorgeSynchroExample example) {
        return projectStorgeSynchroMapper.selectByExample(example);
    }

    @Override
    public ProjectStorgeSynchro selectByPrimaryKey(Long id) {
        return projectStorgeSynchroMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(ProjectStorgeSynchro record) {
        return projectStorgeSynchroMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(ProjectStorgeSynchro record) {
        return projectStorgeSynchroMapper.updateByPrimaryKey(record);
    }

    /**
     * 创建子库货位到外围系统.
     */
    @Override
    public void synchro() {
        final ProjectStorgeSynchroExample example = new ProjectStorgeSynchroExample();
        example.createCriteria().andDeletedFlagEqualTo(false).andStatusEqualTo(CommonStatus.TODO.getCode());
        List<ProjectStorgeSynchro> synchros = selectByExample(example);
        if (ListUtils.isNotEmpty(synchros)) {
            for (ProjectStorgeSynchro synchro : synchros) {
                try {
                    final ProjectProfitDto profitDto = projectProfitService.findProfitDetail(synchro.getProjectId());
                    if (null == profitDto) continue;
                    if (null == profitDto.getStorageId()) continue;
                    profitDto.setStorageInventories(getCreateInventory(profitDto.getStorageId(), true));
                    if (ListUtils.isEmpty(profitDto.getStorageInventories())) {
                        continue;
                    }
                    esbService.callCUXESBCOMMONINAPIPKGPortType(profitDto);
                } catch (Exception e) {
                    synchro.setStatus(CommonStatus.ERROR.getCode());
                } finally {
                    synchro.setStatus(CommonStatus.DONE.getCode());
                    updateByPrimaryKeySelective(synchro);
                }
            }
        }
    }


    /**
     * 获取库存组织下的子库信息.
     * @param organizationId 库存组织
     * @param locatorFlag 是否创建货位
     * @return 子库信息
     */
    public List<StorageInventory> getCreateInventory(final Long organizationId, final Boolean locatorFlag) {
        final Map<String, Object> param = new HashMap<>();
        param.put("organizationId", organizationId);
        param.put("locatorFlag", locatorFlag);
        final String url = StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/storageInventory/listInventoryByCondition", param);
        final String res = restTemplate.getForEntity(url , String.class).getBody();
        final DataResponse<List<StorageInventory>> data = JSON.parseObject(res, new TypeReference<DataResponse<List<StorageInventory>>>(){});
        return data != null ? data.getData() : null;
    }

    @Override
    public void init(final ResendExecute resendExecute) {
        final ProjectProfitDto profitDto = projectProfitService.findProfitDetail(Long.parseLong(resendExecute.getApplyNo()));
        profitDto.setStorageInventories(getCreateInventory(profitDto.getStorageId(), true));
        setInfo(profitDto);
    }

    @Override
    public void validate(ResendExecute resendExecute) {
        Assert.notNull(info, "项目不存在");
        Assert.notNull(info.getStorageId(), "项目未设置库存组织");
        Assert.notNull(info.getStorageInventories(), "找不到需要创建货位的子库");
    }

    /**
     * 执行发送报文.
     *
     * @param resendExecute
     */
    @Override
    public EsbResponse execute(ResendExecute resendExecute) {
        return esbService.callCUXESBCOMMONINAPIPKGPortType(info);
    }
}
