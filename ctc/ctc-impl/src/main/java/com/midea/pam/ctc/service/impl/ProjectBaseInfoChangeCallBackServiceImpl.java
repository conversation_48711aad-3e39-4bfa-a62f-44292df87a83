package com.midea.pam.ctc.service.impl;

import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.enums.ProjectStatus;
import com.midea.pam.common.gateway.entity.FormInstance;
import com.midea.pam.common.gateway.entity.MipCallbackLog;
import com.midea.pam.common.util.DistributedCASLock;
import com.midea.pam.common.util.ExceptionUtils;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.service.ProjectBaseInfoChangeCallBackService;
import com.midea.pam.ctc.service.ProjectBusinessService;
import com.midea.pam.ctc.service.event.ProjectBaseInfoChangeCallBackApprovalEvent;
import com.midea.pam.ctc.workflowcallback.WorkflowCallbackService;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

public class ProjectBaseInfoChangeCallBackServiceImpl implements ProjectBaseInfoChangeCallBackService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectBaseInfoChangeCallBackServiceImpl.class);

    private static final Long MAX_WAIT_TIME = (long) 1000 * 60 * 10;//10分钟

    @Resource
    private ProjectBusinessService projectBusinessService;

    @Resource
    private WorkflowCallbackService workflowCallbackService;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void approvaling(ProjectDto projectDto) {
        Long formInstanceId = projectDto.getFormInstanceId();
        String fdInstanceId = projectDto.getFdInstanceId();
        String formUrl = projectDto.getFormUrl();
        String eventName = projectDto.getEventName();
        String handlerId = projectDto.getHandlerId();
        Long companyId = projectDto.getCompanyId();
        Long createUserId = projectDto.getCreateUserId();
        logger.info("项目基本信息变更审批提交回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        String lockName = String.format("ProjectBaseInfoChangeCallBack_approvaling_%s_%s_%s_%s", formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    projectBusinessService.changeHeadStatus(ProjectStatus.APPROVALING, formInstanceId);

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName, formInstance,
                            false);
                } catch (ApplicationBizException e) {
                    logger.info("项目基本信息变更审批提交回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("项目基本信息变更审批提交回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("项目基本信息变更审批提交回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Override
    public void approved(ProjectDto projectDto) {
        applicationEventPublisher.publishEvent(new ProjectBaseInfoChangeCallBackApprovalEvent(this, projectDto));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void refused(ProjectDto projectDto) {
        Long formInstanceId = projectDto.getFormInstanceId();
        String fdInstanceId = projectDto.getFdInstanceId();
        String formUrl = projectDto.getFormUrl();
        String eventName = projectDto.getEventName();
        String handlerId = projectDto.getHandlerId();
        Long companyId = projectDto.getCompanyId();
        Long createUserId = projectDto.getCreateUserId();
        logger.info("项目基本信息变更审批驳回回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        String lockName = String.format("ProjectBaseInfoChangeCallBack_refused_%s_%s_%s_%s", formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    projectBusinessService.changeHeadStatus(ProjectStatus.REFUSE, formInstanceId);

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName,
                            formInstance, false);
                } catch (ApplicationBizException e) {
                    logger.info("项目基本信息变更审批驳回回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("项目基本信息变更审批驳回回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("项目基本信息变更审批驳回回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void returned(ProjectDto projectDto) {
        Long formInstanceId = projectDto.getFormInstanceId();
        String fdInstanceId = projectDto.getFdInstanceId();
        String formUrl = projectDto.getFormUrl();
        String eventName = projectDto.getEventName();
        String handlerId = projectDto.getHandlerId();
        Long companyId = projectDto.getCompanyId();
        Long createUserId = projectDto.getCreateUserId();
        logger.info("项目基本信息变更审批撤回回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        String lockName = String.format("ProjectBaseInfoChangeCallBack_returned_%s_%s_%s_%s", formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    projectBusinessService.changeHeadStatus(ProjectStatus.RETURN, formInstanceId);

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName,
                            formInstance, false);
                } catch (ApplicationBizException e) {
                    logger.info("项目基本信息变更审批撤回回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("项目基本信息变更审批撤回回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("项目基本信息变更审批撤回回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void abandon(ProjectDto projectDto) {
        Long formInstanceId = projectDto.getFormInstanceId();
        String fdInstanceId = projectDto.getFdInstanceId();
        String formUrl = projectDto.getFormUrl();
        String eventName = projectDto.getEventName();
        String handlerId = projectDto.getHandlerId();
        Long companyId = projectDto.getCompanyId();
        Long createUserId = projectDto.getCreateUserId();
        logger.info("项目基本信息变更审批作废回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        String lockName = String.format("ProjectBaseInfoChangeCallBack_abandon_%s_%s_%s_%s", formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getDeleteAndAbandonFormInstance(formInstanceId, formUrl);

                    projectBusinessService.changeHeadStatus(ProjectStatus.INVALID, formInstanceId);

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName,
                            formInstance, false);
                } catch (ApplicationBizException e) {
                    logger.info("项目基本信息变更审批作废回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("项目基本信息变更审批作废回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("项目基本信息变更审批作废回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(ProjectDto projectDto) {
        Long formInstanceId = projectDto.getFormInstanceId();
        String fdInstanceId = projectDto.getFdInstanceId();
        String formUrl = projectDto.getFormUrl();
        String eventName = projectDto.getEventName();
        String handlerId = projectDto.getHandlerId();
        Long companyId = projectDto.getCompanyId();
        Long createUserId = projectDto.getCreateUserId();
        logger.info("项目基本信息变更审批删除回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        String lockName = String.format("ProjectBaseInfoChangeCallBack_delete_%s_%s_%s_%s", formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getDeleteAndAbandonFormInstance(formInstanceId, formUrl);

                    projectBusinessService.changeHeadStatus(ProjectStatus.CANCEL, formInstanceId);

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName,
                            formInstance, false);
                } catch (ApplicationBizException e) {
                    logger.info("项目基本信息变更审批删除回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("项目基本信息变更审批删除回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("项目基本信息变更审批删除回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

}
