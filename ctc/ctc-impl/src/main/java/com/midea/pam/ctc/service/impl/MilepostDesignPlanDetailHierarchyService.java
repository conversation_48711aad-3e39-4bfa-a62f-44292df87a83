package com.midea.pam.ctc.service.impl;

import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetail;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 里程碑详细设计方案层级计算服务
 * 用于计算层级深度和最终数量
 */
@Service
public class MilepostDesignPlanDetailHierarchyService {

    private static final Logger logger = LoggerFactory.getLogger(MilepostDesignPlanDetailHierarchyService.class);

    @Autowired
    private MilepostDesignPlanDetailMapper milepostDesignPlanDetailMapper;

    /**
     * 计算并更新单个记录的层级深度和最终数量
     * @param detail 要计算的记录
     */
    public void calculateAndUpdateHierarchy(MilepostDesignPlanDetail detail) {
        if (detail == null || detail.getId() == null) {
            return;
        }

        // 计算层级深度
        Integer levelDepth = calculateLevelDepth(detail);
        detail.setLevelDepth(levelDepth);

        // 计算最终数量
        BigDecimal finalNumber = calculateFinalNumber(detail);
        detail.setFinalNumber(finalNumber);

        // 更新数据库
        milepostDesignPlanDetailMapper.updateByPrimaryKeySelective(detail);

        logger.info("Updated hierarchy for detail ID: {}, levelDepth: {}, finalNumber: {}", 
                   detail.getId(), levelDepth, finalNumber);
    }

    /**
     * 批量计算并更新层级深度和最终数量
     * @param details 要计算的记录列表
     */
    public void calculateAndUpdateHierarchyBatch(List<MilepostDesignPlanDetail> details) {
        if (details == null || details.isEmpty()) {
            return;
        }

        // 构建父子关系映射
        Map<Long, List<MilepostDesignPlanDetail>> childrenMap = new HashMap<>();
        Map<Long, MilepostDesignPlanDetail> detailMap = new HashMap<>();
        
        for (MilepostDesignPlanDetail detail : details) {
            detailMap.put(detail.getId(), detail);
            if (detail.getParentId() != null && detail.getParentId() != -1) {
                childrenMap.computeIfAbsent(detail.getParentId(), k -> new ArrayList<>()).add(detail);
            }
        }

        // 递归计算每个节点
        for (MilepostDesignPlanDetail detail : details) {
            if (detail.getParentId() == null || detail.getParentId() == -1) {
                // 根节点
                calculateHierarchyRecursive(detail, 1, detail.getNumber(), detailMap, childrenMap);
            }
        }

        // 批量更新数据库
        for (MilepostDesignPlanDetail detail : details) {
            milepostDesignPlanDetailMapper.updateByPrimaryKeySelective(detail);
        }

        logger.info("Updated hierarchy for {} details", details.size());
    }

    /**
     * 递归计算层级深度和最终数量
     * @param detail 当前节点
     * @param depth 当前深度
     * @param parentFinalNumber 父节点的最终数量
     * @param detailMap 详情映射
     * @param childrenMap 子节点映射
     */
    private void calculateHierarchyRecursive(MilepostDesignPlanDetail detail, 
                                           int depth, 
                                           BigDecimal parentFinalNumber,
                                           Map<Long, MilepostDesignPlanDetail> detailMap,
                                           Map<Long, List<MilepostDesignPlanDetail>> childrenMap) {
        
        // 设置层级深度
        detail.setLevelDepth(depth);
        
        // 计算最终数量：当前数量 × 父级最终数量
        BigDecimal currentNumber = detail.getNumber() != null ? detail.getNumber() : BigDecimal.ONE;
        BigDecimal finalNumber = currentNumber.multiply(parentFinalNumber);
        detail.setFinalNumber(finalNumber);

        // 递归处理子节点
        List<MilepostDesignPlanDetail> children = childrenMap.get(detail.getId());
        if (children != null && !children.isEmpty()) {
            for (MilepostDesignPlanDetail child : children) {
                calculateHierarchyRecursive(child, depth + 1, finalNumber, detailMap, childrenMap);
            }
        }
    }

    /**
     * 计算单个记录的层级深度
     * @param detail 记录
     * @return 层级深度
     */
    private Integer calculateLevelDepth(MilepostDesignPlanDetail detail) {
        if (detail.getParentId() == null || detail.getParentId() == -1) {
            return 1; // 根节点
        }

        // 递归查找父节点深度
        MilepostDesignPlanDetail parent = milepostDesignPlanDetailMapper.selectByPrimaryKey(detail.getParentId());
        if (parent == null) {
            return 1; // 如果找不到父节点，视为根节点
        }

        Integer parentDepth = parent.getLevelDepth();
        if (parentDepth == null) {
            // 如果父节点深度未计算，先计算父节点
            parentDepth = calculateLevelDepth(parent);
            parent.setLevelDepth(parentDepth);
            milepostDesignPlanDetailMapper.updateByPrimaryKeySelective(parent);
        }

        return parentDepth + 1;
    }

    /**
     * 计算单个记录的最终数量
     * @param detail 记录
     * @return 最终数量
     */
    private BigDecimal calculateFinalNumber(MilepostDesignPlanDetail detail) {
        BigDecimal currentNumber = detail.getNumber() != null ? detail.getNumber() : BigDecimal.ONE;
        
        if (detail.getParentId() == null || detail.getParentId() == -1) {
            return currentNumber; // 根节点的最终数量就是自身数量
        }

        // 递归计算父节点的最终数量
        MilepostDesignPlanDetail parent = milepostDesignPlanDetailMapper.selectByPrimaryKey(detail.getParentId());
        if (parent == null) {
            return currentNumber; // 如果找不到父节点，返回自身数量
        }

        BigDecimal parentFinalNumber = parent.getFinalNumber();
        if (parentFinalNumber == null) {
            // 如果父节点最终数量未计算，先计算父节点
            parentFinalNumber = calculateFinalNumber(parent);
            parent.setFinalNumber(parentFinalNumber);
            milepostDesignPlanDetailMapper.updateByPrimaryKeySelective(parent);
        }

        return currentNumber.multiply(parentFinalNumber);
    }

    /**
     * 级联更新子节点的层级深度和最终数量
     * 当父节点的数量发生变化时调用
     * @param parentId 父节点ID
     */
    public void cascadeUpdateChildren(Long parentId) {
        if (parentId == null) {
            return;
        }

        // 查找所有子节点
        List<MilepostDesignPlanDetail> children = findDirectChildren(parentId);
        if (children.isEmpty()) {
            return;
        }

        // 获取父节点信息
        MilepostDesignPlanDetail parent = milepostDesignPlanDetailMapper.selectByPrimaryKey(parentId);
        if (parent == null) {
            return;
        }

        // 递归更新所有子节点
        for (MilepostDesignPlanDetail child : children) {
            calculateAndUpdateHierarchy(child);
            cascadeUpdateChildren(child.getId()); // 递归更新孙子节点
        }

        logger.info("Cascade updated children for parent ID: {}, affected children: {}", parentId, children.size());
    }

    /**
     * 查找直接子节点
     * @param parentId 父节点ID
     * @return 子节点列表
     */
    private List<MilepostDesignPlanDetail> findDirectChildren(Long parentId) {
        // 这里需要根据实际的查询方法来实现
        // 暂时使用示例代码，实际需要根据项目中的Mapper方法来调用
        try {
            // 假设有一个根据parentId查询的方法
            // return milepostDesignPlanDetailExtMapper.selectByParentId(parentId);
            return new ArrayList<>(); // 临时返回空列表
        } catch (Exception e) {
            logger.error("Error finding children for parent ID: {}", parentId, e);
            return new ArrayList<>();
        }
    }
}