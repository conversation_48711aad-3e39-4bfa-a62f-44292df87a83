package com.midea.pam.ctc.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.midea.mcomponent.core.util.Assert;
import com.midea.pam.common.basedata.dto.LaborCostRankRealMonthDetailDto;
import com.midea.pam.common.basedata.dto.LaborExternalCostDto;
import com.midea.pam.common.basedata.entity.OperatingUnit;
import com.midea.pam.common.basedata.entity.Unit;
import com.midea.pam.common.basedata.entity.UnitOuRel;
import com.midea.pam.common.ctc.dto.AssetDeprnDto;
import com.midea.pam.common.ctc.dto.BusiSceneNonSaleDetailDto;
import com.midea.pam.common.ctc.dto.CostCollectionDto;
import com.midea.pam.common.ctc.dto.DifferenceShareResultDetailDto;
import com.midea.pam.common.ctc.dto.FeeCostDetailDto;
import com.midea.pam.common.ctc.dto.LaborCostDetailDto;
import com.midea.pam.common.ctc.dto.MaterialActualCostDetailDto;
import com.midea.pam.common.ctc.dto.MaterialOutsourceCostDetailDto;
import com.midea.pam.common.ctc.dto.PaymentInvoiceDto;
import com.midea.pam.common.ctc.dto.ProjectAssetDeprnCostDetailDto;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.dto.ProjectProfitDto;
import com.midea.pam.common.ctc.dto.PurchaseContractDTO;
import com.midea.pam.common.ctc.dto.PurchaseContractProgressDto;
import com.midea.pam.common.ctc.dto.VendorPenaltyDetailDto;
import com.midea.pam.common.ctc.entity.CarryoverBill;
import com.midea.pam.common.ctc.entity.CostCollection;
import com.midea.pam.common.ctc.entity.CostCollectionExample;
import com.midea.pam.common.ctc.entity.DifferenceShareResultDetail;
import com.midea.pam.common.ctc.entity.HroWorkingHourBillCostDetail;
import com.midea.pam.common.ctc.entity.LaborCostDetail;
import com.midea.pam.common.ctc.entity.MaterialOutsourceCostDetail;
import com.midea.pam.common.ctc.entity.MaterialOutsourceCostDetailExample;
import com.midea.pam.common.ctc.entity.MaterialOutsourcingContractConfig;
import com.midea.pam.common.ctc.entity.MaterialOutsourcingContractConfigExample;
import com.midea.pam.common.ctc.entity.PaymentInvoice;
import com.midea.pam.common.ctc.entity.PaymentInvoiceExample;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectAssetDeprnCostDetail;
import com.midea.pam.common.ctc.entity.ProjectAssetDeprnCostDetailExample;
import com.midea.pam.common.ctc.entity.ProjectExample;
import com.midea.pam.common.ctc.entity.ProjectType;
import com.midea.pam.common.ctc.entity.PurchaseContractProgress;
import com.midea.pam.common.ctc.entity.PurchaseContractProgressExample;
import com.midea.pam.common.ctc.entity.VendorPenaltyCostDetail;
import com.midea.pam.common.ctc.entity.WorkingHour;
import com.midea.pam.common.ctc.entity.WorkingHourExample;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.OrgCustomDictOrgFrom;
import com.midea.pam.common.enums.PaymentInvoiceSourceEnum;
import com.midea.pam.common.enums.PaymentInvoiceStatusEnum;
import com.midea.pam.common.enums.UserType;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.Builder;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.ObjUtils;
import com.midea.pam.common.util.Symbol;
import com.midea.pam.ctc.common.enums.CommonAccountStatus;
import com.midea.pam.ctc.common.enums.CostCollectionEnum;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.enums.DeletedFlagEnum;
import com.midea.pam.ctc.common.enums.MaterialOutsourceCostDetailType;
import com.midea.pam.ctc.common.enums.PurchaseContractProgressCollectionStatus;
import com.midea.pam.ctc.common.enums.PurchaseContractProgressStatus;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.common.utils.ListUtil;
import com.midea.pam.ctc.contract.service.PurchaseContractService;
import com.midea.pam.ctc.mapper.CarryoverBillMapper;
import com.midea.pam.ctc.mapper.CostCollectionExtMapper;
import com.midea.pam.ctc.mapper.CostCollectionMapper;
import com.midea.pam.ctc.mapper.CostRatioConfigDetailExtMapper;
import com.midea.pam.ctc.mapper.CostRatioConfigMapper;
import com.midea.pam.ctc.mapper.HroWorkingHourBillCostDetailExtMapper;
import com.midea.pam.ctc.mapper.MaterialOutsourceCostDetailMapper;
import com.midea.pam.ctc.mapper.MaterialOutsourcingContractConfigMapper;
import com.midea.pam.ctc.mapper.PaymentInvoiceMapper;
import com.midea.pam.ctc.mapper.ProjectAssetDeprnCostDetailExtMapper;
import com.midea.pam.ctc.mapper.ProjectAssetDeprnCostDetailMapper;
import com.midea.pam.ctc.mapper.ProjectAssetRsExtMapper;
import com.midea.pam.ctc.mapper.ProjectMapper;
import com.midea.pam.ctc.mapper.ProjectTypeMapper;
import com.midea.pam.ctc.mapper.PurchaseContractProgressMapper;
import com.midea.pam.ctc.mapper.VendorPenaltyCostDetailExtMapper;
import com.midea.pam.ctc.mapper.WorkingHourMapper;
import com.midea.pam.ctc.service.BusiSceneNonSaleService;
import com.midea.pam.ctc.service.CostCollectionService;
import com.midea.pam.ctc.service.FeeCostDetailService;
import com.midea.pam.ctc.service.HroWorkingHourBillService;
import com.midea.pam.ctc.service.LaborCostDetailService;
import com.midea.pam.ctc.service.MaterialActualCostDetailService;
import com.midea.pam.ctc.service.OrganizationCustomDictService;
import com.midea.pam.ctc.service.ProjectProfitService;
import com.midea.pam.ctc.service.ProjectService;
import com.midea.pam.ctc.service.ProjectTypeService;
import com.midea.pam.ctc.service.VendorPenaltyService;
import com.midea.pam.ctc.service.WorkingHourService;
import com.midea.pam.ctc.service.helper.LaborCostRealMonthHelper;
import com.midea.pam.ctc.share.service.DifferenceShareResultDetailService;
import com.midea.pam.framework.core.exception.Guard;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.midea.pam.common.util.DateUtil.FORMAT_YEAR_MONTH;

/**
 * @program: pam
 * @description: CostCollectionServiceImpl
 * @author: gaojh1
 * @create: 2019-3-12 10:51
 **/
public class CostCollectionServiceImpl implements CostCollectionService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private CostCollectionMapper costCollectionMapper;
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    MaterialActualCostDetailService materialActualCostDetailService;
    @Resource
    private DifferenceShareResultDetailService differenceShareResultDetailService;
    @Resource
    private LaborCostDetailService laborCostDetailService;
    @Resource
    private ProjectService projectService;
    @Resource
    private FeeCostDetailService feeCostDetailService;
    @Resource
    private CostCollectionExtMapper costCollectionExtMapper;
    @Resource
    private ProjectProfitService projectProfitService;
    @Resource
    private LaborCostRealMonthHelper laborCostRealMonthHelper;
    @Resource
    private WorkingHourService workingHourService;
    @Resource
    private WorkingHourMapper workingHourMapper;
    @Resource
    private OrganizationCustomDictService organizationCustomDictService;
    @Resource
    private PurchaseContractService purchaseContractService;
    @Resource
    private CostRatioConfigMapper costRatioConfigMapper;
    @Resource
    private CostRatioConfigDetailExtMapper costRatioConfigDetailExtMapper;
    @Resource
    private MaterialOutsourcingContractConfigMapper materialOutsourcingContractConfigMapper;
    @Resource
    private PaymentInvoiceMapper paymentInvoiceMapper;
    @Resource
    private MaterialOutsourceCostDetailMapper materialOutsourceCostDetailMapper;
    @Resource
    private CarryoverBillMapper carryoverBillMapper;
    @Resource
    private ProjectTypeMapper projectTypeMapper;
    @Resource
    private MaterialOutsourcingContractConfigMapper contractConfigMapper;
    @Resource
    private PurchaseContractProgressMapper purchaseContractProgressMapper;
    @Resource
    private HroWorkingHourBillService hroWorkingHourBillService;
    @Resource
    private HroWorkingHourBillCostDetailExtMapper hroWorkingHourBillCostDetailExtMapper;
    @Resource
    private VendorPenaltyService vendorPenaltyService;
    @Resource
    private VendorPenaltyCostDetailExtMapper vendorPenaltyCostDetailExtMapper;
    @Resource
    private ProjectTypeService projectTypeService;
    @Resource
    private ProjectAssetRsExtMapper projectAssetRsExtMapper;
    @Resource
    private ProjectAssetDeprnCostDetailMapper projectAssetDeprnCostDetailMapper;
    @Resource
    private ProjectAssetDeprnCostDetailExtMapper projectAssetDeprnCostDetailExtMapper;
    @Resource
    private BusiSceneNonSaleService busiSceneNonSaleService;


    @Override
    public CostCollection findById(Long id) {
        return costCollectionMapper.selectByPrimaryKey(id);
    }

    /**
     * 保存归集数据
     *
     * @param dto  : 费用归集数据
     * @param type : 费用类型：1：物料实际成本，2：内/外部人工成本，3：项目费用成本，4：物料差异成本，5：物料外包成本，6：材料罚扣成本，7：资产折旧成本
     */
    @Override
    public void save(CostCollectionDto dto, int type) {
        // 归结表数据被结转后，因为数据更新导致结转状态变更为未结转，容易产生成本重复结转
        // 判断归集表数据是否被已结转，已结转的重新生成
        CostCollectionExample example = new CostCollectionExample();
        //外部 无需结转
        if (null != dto.getStatus() && 3 == dto.getStatus()) {
            CostCollectionExample.Criteria criteria3 = example.createCriteria();
            criteria3.andProjectIdEqualTo(dto.getProjectId())
                    .andCollectionDateEqualTo(dto.getCollectionDate())
                    .andCostDateEqualTo(dto.getCostDate())
                    .andCarryStatusEqualTo(2)
                    .andStatusEqualTo(3)
                    .andDeletedFlagEqualTo(Boolean.FALSE);
        } else {
            CostCollectionExample.Criteria criteria = example.createCriteria();
            criteria.andProjectIdEqualTo(dto.getProjectId())
                    .andCollectionDateEqualTo(dto.getCollectionDate())
                    .andCostDateEqualTo(dto.getCostDate())
                    .andCarryStatusIsNull()
                    .andDeletedFlagEqualTo(Boolean.FALSE);

            CostCollectionExample.Criteria criteria2 = example.or();
            criteria2.andProjectIdEqualTo(dto.getProjectId())
                    .andCollectionDateEqualTo(dto.getCollectionDate())
                    .andCostDateEqualTo(dto.getCostDate())
                    .andCarryStatusEqualTo(0)
                    .andDeletedFlagEqualTo(Boolean.FALSE);
        }


        List<CostCollection> collectionList = costCollectionMapper.selectByExample(example);
        CostCollection costCollection = new CostCollection();

        // todo 233为什么数据库不加个默认值..?
        BeanUtils.copyProperties(dto, costCollection);
        if (CollectionUtils.isEmpty(collectionList)) {
            if (ObjectUtils.isEmpty(costCollection.getMaterialActualCost()))
                costCollection.setMaterialActualCost(BigDecimal.ZERO);
            if (ObjectUtils.isEmpty(costCollection.getInnerLaborCost()))
                costCollection.setInnerLaborCost(BigDecimal.ZERO);
            if (ObjectUtils.isEmpty(costCollection.getOuterLaborCost()))
                costCollection.setOuterLaborCost(BigDecimal.ZERO);
            if (ObjectUtils.isEmpty(costCollection.getFeeCost()))
                costCollection.setFeeCost(BigDecimal.ZERO);
            if (ObjectUtils.isEmpty(costCollection.getMaterialOutsourceCost()))
                costCollection.setMaterialOutsourceCost(BigDecimal.ZERO);
            if (ObjectUtils.isEmpty(costCollection.getMaterialDifferenceCost()))
                costCollection.setMaterialDifferenceCost(BigDecimal.ZERO);
            if (ObjectUtils.isEmpty(costCollection.getMaterialDifferenceCost()))
                costCollection.setMaterialDifferenceCost(BigDecimal.ZERO);
            if (ObjectUtils.isEmpty(costCollection.getMaterialPenaltyCost()))
                costCollection.setMaterialPenaltyCost(BigDecimal.ZERO);
            if (ObjectUtils.isEmpty(costCollection.getAssetDeprnCost()))
                costCollection.setAssetDeprnCost(BigDecimal.ZERO);
            costCollection.setCurrency("CNY");//需求要求写死
            costCollection.setDeletedFlag(Boolean.FALSE);
            costCollectionMapper.insert(costCollection);
        } else {
            CostCollection existCostCollection = collectionList.get(0);
            costCollection.setId(existCostCollection.getId());
            // 当天归集数据归集后，手工归集产生相同cost_date,collection_date的成本数据时候会覆盖之前的数据

            if (type == 1 && costCollection.getMaterialActualCost() != null)
                costCollection.setMaterialActualCost(
                        BigDecimalUtils.add(costCollection.getMaterialActualCost(), existCostCollection.getMaterialActualCost()));
            if (type == 2 && costCollection.getInnerLaborCost() != null)
                costCollection.setInnerLaborCost(
                        BigDecimalUtils.add(costCollection.getInnerLaborCost(), existCostCollection.getInnerLaborCost()));
            if (type == 2 && costCollection.getOuterLaborCost() != null)
                costCollection.setOuterLaborCost(
                        BigDecimalUtils.add(costCollection.getOuterLaborCost(), existCostCollection.getOuterLaborCost()));
            if (type == 3 && costCollection.getFeeCost() != null)
                costCollection.setFeeCost(BigDecimalUtils.add(costCollection.getFeeCost(), existCostCollection.getFeeCost()));
            if (type == 5 && costCollection.getMaterialOutsourceCost() != null)
                costCollection.setMaterialOutsourceCost(BigDecimalUtils.add(costCollection.getMaterialOutsourceCost(), existCostCollection.getMaterialOutsourceCost()));
            if (type == 4 && costCollection.getMaterialDifferenceCost() != null)
                costCollection.setMaterialDifferenceCost(BigDecimalUtils.add(costCollection.getMaterialDifferenceCost(), existCostCollection.getMaterialDifferenceCost()));
            if (type == 6 && costCollection.getMaterialPenaltyCost() != null)
                costCollection.setMaterialPenaltyCost(BigDecimalUtils.add(costCollection.getMaterialPenaltyCost(), existCostCollection.getMaterialPenaltyCost()));
            if (type == 7 && costCollection.getAssetDeprnCost() != null)
                costCollection.setAssetDeprnCost(BigDecimalUtils.add(costCollection.getAssetDeprnCost(), existCostCollection.getAssetDeprnCost()));

            costCollectionMapper.updateByPrimaryKeySelective(costCollection);
        }
        dto.setId(costCollection.getId());
    }

    @Override
    public PageInfo<CostCollectionDto> page(CostCollectionDto query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<CostCollection> costCollections = costCollectionExtMapper.page(query);
        return BeanConverter.convertPage(costCollections, CostCollectionDto.class);
    }

    @Override
    public List<CostCollectionDto> list(CostCollectionDto query) {
        List<CostCollection> list = costCollectionMapper.selectByExample(buildCondition(query));
        if (ListUtils.isNotEmpty(list)) {
            return BeanConverter.copy(list, CostCollectionDto.class);
        }
        return Lists.newArrayList();
    }

    @Override
    public List<CostCollectionDto> listWithDetail(CostCollectionDto query) {
        List<CostCollectionDto> dtos = this.list(query);
        for (CostCollectionDto dto : dtos) {
            packageDto(dto);
        }
        return dtos;
    }

    private void packageDto(CostCollectionDto dto) {
        if (dto != null) {
            //封装费用成本明细数
            List<FeeCostDetailDto> feeCostDetailDtos = feeCostDetailService.queryByCollection(dto.getId());
            if (!CollectionUtils.isEmpty(feeCostDetailDtos)) {
                dto.setFeeCostDetailDtoSize(feeCostDetailDtos.size());
            } else {
                dto.setFeeCostDetailDtoSize(0);
            }
            //封装人工成本明细数
            List<LaborCostDetail> laborCostDetails = laborCostDetailService.queryByCollection(dto.getId());
            if (!CollectionUtils.isEmpty(laborCostDetails)) {
                dto.setLaborCostDetailDtoSize(laborCostDetails.size());

                final List<LaborCostDetailDto> laborCostDetailDtos = BeanConverter.copy(laborCostDetails, LaborCostDetailDto.class);
                dto.setInnerLaborCostDetailDtos(laborCostDetailDtos);
            } else {
                dto.setLaborCostDetailDtoSize(0);
            }
            //封装物料实际成本明细数
            List<MaterialActualCostDetailDto> materialActualCostDetailDtos = materialActualCostDetailService.queryByCollectionId(dto.getId());
            if (!CollectionUtils.isEmpty(materialActualCostDetailDtos)) {
                dto.setMaterialActualCostDetailDtoSize(materialActualCostDetailDtos.size());
            } else {
                dto.setMaterialActualCostDetailDtoSize(0);
            }
        }
    }

    private CostCollectionExample buildCondition(CostCollectionDto query) {
        CostCollectionExample example = new CostCollectionExample();
        CostCollectionExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(DeletedFlag.VALID.code());

        if (ListUtil.isPresent(query.getIds())) {
            criteria.andIdIn(query.getIds());
        }
        if (query.getCollectionDateStart() != null) {
            criteria.andCollectionDateGreaterThanOrEqualTo(DateUtil.getBeginningOfDay(query.getCollectionDateStart()));
        }
        if (query.getCollectionDateEnd() != null) {
            criteria.andCollectionDateLessThanOrEqualTo(DateUtil.getBeginningOfDay(query.getCollectionDateEnd()));
        }
        if (query.getCostDateStart() != null) {
            criteria.andCostDateGreaterThanOrEqualTo(DateUtil.getBeginningOfDay(query.getCostDateStart()));
        }
        if (query.getCostDateEnd() != null) {
            criteria.andCostDateLessThanOrEqualTo(DateUtil.getBeginningOfDay(query.getCostDateEnd()));
        }
        if (query.getProjectId() != null) {
            criteria.andProjectIdEqualTo(query.getProjectId());
        }
        if (query.getCarryStatus() != null) {
            criteria.andCarryStatusEqualTo(query.getCarryStatus());
        }
        return example;
    }


    /**
     * 成本归集
     *
     * @param ouId(业务实体id)
     * @param collectionDate(归集日期)
     */
    @Override
    public void collectionByOu(Long ouId, Date collectionDate) {
        //找出符合归集条件的项目
        ProjectExample example = new ProjectExample();
        ProjectExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE).andOuIdIsNotNull();
        if (!ObjectUtils.isEmpty(ouId)) {
            criteria.andOuIdEqualTo(ouId);
        }

        List<Project> projectList = projectMapper.selectByExample(example);

        if (!CollectionUtils.isEmpty(projectList)) {
            // 获取所有使用单位人力费率配置
            Map<Long, Map<String, BigDecimal>> unitMap = getLaborCostDetailMap();

            // 归集开始
            projectList.parallelStream().forEach(project -> {
                ProjectDto projectDto = projectService.findDetail(project.getId());
                BeanUtils.copyProperties(project, projectDto);
                this.collection(projectDto, collectionDate, unitMap);
            });
        }
    }

    public Map<Long, Map<String, BigDecimal>> getLaborCostDetailMap() {
        // 获取所有的人力成本费率（数量不大），避免多次查询
        final List<LaborCostRankRealMonthDetailDto> validDetails = laborCostRealMonthHelper.getValidDetails(null, null, null);
        final List<LaborExternalCostDto> laborCostTypes = laborCostRealMonthHelper.getLaborExternalCost(null);
        // 使用单位下人力职级费率
        Map<Long, Map<String, BigDecimal>> unitMap = new HashMap<>();
        validDetails.forEach(laborCostRankRealMonthDetailDto -> {
            final Long bizUnitId = laborCostRankRealMonthDetailDto.getBizUnitId();
            final String month = laborCostRankRealMonthDetailDto.getMonth();
            final String levelName = laborCostRankRealMonthDetailDto.getLevelName();
            final String laborCostTypeCode = laborCostRankRealMonthDetailDto.getLaborCostTypeCode();
            final BigDecimal unitPrice = laborCostRankRealMonthDetailDto.getUnitPrice();

            Map<String, BigDecimal> unitPriceMap = unitMap.computeIfAbsent(bizUnitId, k -> new HashMap<>());

            // 月份 + 费率类型 + 职级 构成唯一主键
            String key = month + "-" + laborCostTypeCode + "-" + levelName;
            unitPriceMap.put(key.toUpperCase(), unitPrice);
        });
        laborCostTypes.forEach(LaborExternalCostDto -> {
            final Long bizUnitId = LaborExternalCostDto.getBizUnitId();
            final String levelName = LaborExternalCostDto.getName();
            final BigDecimal unitPrice = LaborExternalCostDto.getExternalProjectCost();

            Map<String, BigDecimal> unitPriceMap = unitMap.computeIfAbsent(bizUnitId, k -> new HashMap<>());
            //  职级
            String key = levelName;
            unitPriceMap.put(key.toUpperCase(), unitPrice);
        });
        return unitMap;
    }

    /**
     * 成本归集
     *
     * @param project(项目)
     * @param collectionDate(归集日期)
     */
    @Override
    public void collection(ProjectDto project, Date collectionDate, Map<Long, Map<String, BigDecimal>> unitMap) {
        CostCollectionDto collectionDto = new CostCollectionDto();
        collectionDto.setProjectId(project.getId());
        collectionDto.setProjectCode(project.getCode());
        collectionDto.setProjectName(project.getName());
        collectionDto.setProjectType(project.getTypeName());
        collectionDto.setType(CostCollectionEnum.NORMAL_TYPE.getCode());//正常
        collectionDto.setOuId(project.getOuId());
        collectionDto.setOuName(CacheDataUtils.findOuById(collectionDto.getOuId()).getOperatingUnitName());
        collectionDto.setCollectionDate(collectionDate);
        collectionDto.setCarryStatus(0);

        //设置成本方法
        ProjectProfitDto profitDetail = projectProfitService.findProfitDetail(project.getId());
        if (profitDetail != null) {
            collectionDto.setCostMethodMain(profitDetail.getCostMethodMain());
        }

        //1.物料实际成本归集计算
        try {
            logger.info("1.0项目：{}物料实际成本归集计算开始", project.getId());
            this.saveMaterialActualCost(collectionDto);
        } catch (Exception e) {
            logger.error("1.0项目：{}物料实际成本归集计算异常", project.getId(), e);
        }

        //1.1物料外包成本归集计算【按发票入账结转】
        try {
            logger.info("1.1项目：{}物料外包成本归集计算【按发票入账结转】开始", project.getId());
            this.saveMaterialOutsourceCost(collectionDto);
        } catch (Exception e) {
            logger.error("1.1项目：{}物料外包成本归集计算【按发票入账结转】异常", project.getId(), e);
        }
        //1.2物料外包成本归集计算【按合同进度入账结转】
        try {
            logger.info("1.2项目：{}物料外包成本归集计算【按合同进度入账结转】开始", project.getId());
            this.saveProgressExecuteCost(collectionDto);
        } catch (Exception e) {
            logger.error("1.2项目：{}物料外包成本归集计算【按合同进度入账结转】归集异常", project.getId(), e);
        }
        //2.人工成本（内/外部）归集计算
        try {
            logger.info("2.项目：{}人工成本（内/外部）归集计算开始", project.getId());
            this.saveLaborCost(collectionDto, unitMap);
        } catch (Exception e) {
            logger.error("2.项目：{}人工成本（内/外部）归集计算异常", project.getId(), e);
        }
        //3.项目费用成本归集计算
        try {
            logger.info("3.项目：{}项目费用成本归集计算开始", project.getId());
            this.saveFeeCost(collectionDto);
        } catch (Exception e) {
            logger.error("3.项目：{}项目费用成本归集计算异常", project.getId(), e);
        }
        //4.物料成本差异归集计算
        try {
            logger.info("4.项目：{}物料成本差异归集计算开始", project.getId());
            this.saveMaterialDifferenceCost(collectionDto);
        } catch (Exception e) {
            logger.error("4.项目：{}物料成本差异归集计算异常", project.getId(), e);
        }
        //5.无成本发生则生成默认数据
        try {
            logger.info("5.项目：{}无成本发生则生成默认数据开始", project.getId());
            this.saveDefaultCollection(collectionDto);
        } catch (Exception e) {
            logger.error("5.项目：{}无成本发生则生成默认数据异常", project.getId(), e);
        }
        //6.点工对账单归集
        try {
            logger.info("6.项目：{}点工对账单归集开始", project.getId());
            this.saveHroWorkingHourBillCollection(collectionDto);
        } catch (Exception e) {
            logger.error("6.项目：{}点工对账单归集异常", project.getId(), e);
        }
        //7.供应商罚扣归集开始
        try {
            logger.info("7.项目：{}供应商罚扣归集开始", project.getId());
            this.saveVendorPenaltyCollection(collectionDto);
        } catch (Exception e) {
            logger.error("7.项目：{}供应商罚扣归集异常", project.getId(), e);
        }
        //8.资产折旧归集开始
        try {
            logger.info("8.项目：{}资产折旧归集开始", project.getId());
            this.saveAssetDeprnCollection(collectionDto);
        } catch (Exception e) {
            logger.error("8.项目：{}资产折旧归集开始", project.getId(), e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    private void saveAssetDeprnCollection(CostCollectionDto collectionDto) {
        Long projectId = collectionDto.getProjectId();
        //查询已关联项目的资产折旧数据
        List<AssetDeprnDto> assetDeprnList = projectAssetRsExtMapper.queryProjectAssetDeprnById(projectId);
        if (CollectionUtils.isEmpty(assetDeprnList)) {
            return;
        }

        //查询项目已归集资产折旧数据
        ProjectAssetDeprnCostDetailExample example = new ProjectAssetDeprnCostDetailExample();
        example.createCriteria().andProjectIdEqualTo(projectId).andDeletedFlagEqualTo(Boolean.FALSE);
        Map<String, List<ProjectAssetDeprnCostDetail>> costDetailMap = projectAssetDeprnCostDetailMapper.selectByExample(example).stream().collect(Collectors.groupingBy(s -> buildAssetDeprnKey(s.getAssetNumber(), s.getPeriodName())));

        /**
         * 【维度】项目id + 资产number + 折旧区间
         *
         * 1. 如果【project_asset_deprn_cost_detail】表不存在，生成归集数据
         *
         * 2. 如果【project_asset_deprn_cost_detail】表存在，对比折旧金额合计值：
         * 	    2.1 如果不一致，相减得到差值，作为资产折旧成本，生成归集数据；
         * 	    2.2 如果一致，不处理；
         */
        List<ProjectAssetDeprnCostDetail> insertList = new ArrayList<>();
        for (AssetDeprnDto dto : assetDeprnList) {
            //折旧金额
            BigDecimal deprnAmount = Optional.ofNullable(dto.getDeprnAmount()).orElse(BigDecimal.ZERO);
            List<ProjectAssetDeprnCostDetail> costDetails = costDetailMap.get(buildAssetDeprnKey(dto.getAssetNumber(), dto.getPeriodName()));
            if (costDetails == null) {
                collectAssetDeprn(collectionDto, dto, deprnAmount, insertList);
            } else {
                //已归集金额汇总
                BigDecimal collectedAmount = costDetails.stream().map(s -> s.getDeprnAmount()).reduce(BigDecimal.ZERO, BigDecimalUtils::add);
                //差值
                BigDecimal diff = deprnAmount.subtract(collectedAmount);
                if (diff.compareTo(BigDecimal.ZERO) != 0
                        || (deprnAmount.compareTo(BigDecimal.ZERO) == 0 && costDetails.size() % 2 == 0)) //折旧金额为0，关联，取消关联，再关联的场景特殊处理一下
                    collectAssetDeprn(collectionDto, dto, diff, insertList);
            }
        }
        if (!CollectionUtils.isEmpty(insertList)) {
            List<List<ProjectAssetDeprnCostDetail>> lists = ListUtils.splistList(insertList, 200);
            lists.forEach(list -> projectAssetDeprnCostDetailExtMapper.batchInsert(list));
        }
    }

    public void collectAssetDeprn(CostCollectionDto collectionDto, AssetDeprnDto dto, BigDecimal amount, List<ProjectAssetDeprnCostDetail> insertList) {
        CostCollectionDto costCollection = BeanConverter.copy(collectionDto, CostCollectionDto.class);
        costCollection.setCostDate(DateUtil.parseDate(dto.getPeriodName(), FORMAT_YEAR_MONTH));
        costCollection.setAssetDeprnCost(amount);
        //归集头
        this.save(costCollection, 7);
        //归集明细
        ProjectAssetDeprnCostDetail projectAssetDeprnCostDetail = Builder.of(ProjectAssetDeprnCostDetail::new)
                .with(ProjectAssetDeprnCostDetail::setCostCollectionId, costCollection.getId())
                .with(ProjectAssetDeprnCostDetail::setProjectId, costCollection.getProjectId())
                .with(ProjectAssetDeprnCostDetail::setAssetNumber, dto.getAssetNumber())
                .with(ProjectAssetDeprnCostDetail::setDescription, dto.getDescription())
                .with(ProjectAssetDeprnCostDetail::setPeriodName, dto.getPeriodName())
                .with(ProjectAssetDeprnCostDetail::setDeprnAmount, dto.getDeprnAmount())
                .with(ProjectAssetDeprnCostDetail::setDeprnSubject, generateDeprnSubject(dto))
                .with(ProjectAssetDeprnCostDetail::setStatus, CommonAccountStatus.NOT.code()) //未入账
                .with(ProjectAssetDeprnCostDetail::setDeletedFlag, Boolean.FALSE)
                .build();
        insertList.add(projectAssetDeprnCostDetail);
    }

    /**
     *  贷方科目获取逻辑：
     *
     *  1=按资产基本信息中的科目段获取
     *  2=按非销售场景的“资产折旧成本入账”中的贷方科目获取
     *
     * @return 贷方科目，默认方式1
     */
    public String generateDeprnSubject(AssetDeprnDto dto) {
        List<BusiSceneNonSaleDetailDto> busiSceneNonSaleDetailDtos = busiSceneNonSaleService.getBusiSceneDetailListByName("资产折旧成本入账", dto.getOuId());
        if (!CollectionUtils.isEmpty(busiSceneNonSaleDetailDtos)) {
            String accountGroupCredit = busiSceneNonSaleDetailDtos.get(0).getAccountGroupCredit();
            Guard.notNullOrEmpty(accountGroupCredit, "非销售业务场景的贷方科目没有配置");
            return accountGroupCredit;
        }
        List<String> list = new ArrayList<>();
        list.add(dto.getCompanyCode()); //OU段
        list.add(dto.getCostCentre()); //成本中心段
        list.add(dto.getSubject()); //科目段
        list.add("0"); //区域，默认0
        list.add("0"); //产品，默认0
        list.add("0"); //往来，默认0
        list.add("0"); //备用，默认0
        return StringUtils.join(list, Symbol.PERIOD);
    }

    public String buildAssetDeprnKey(String assetNumber, String periodName) {
        return String.format("buildAssetDeprnKey_%s_%s", assetNumber, periodName);
    }

    /**
     * 生成负数的资产折旧归集记录
     *
     * @param projectId
     * @param assetNumberList
     */
    public void saveAssetDeprnCollectionNegative(Long projectId, List<String> assetNumberList) {
        if (projectId == null || CollectionUtils.isEmpty(assetNumberList)) {
            return;
        }
        List<ProjectAssetDeprnCostDetail> costDetailList = projectAssetDeprnCostDetailExtMapper.queryNegativeAssetDeprnCost(projectId, assetNumberList);
        //取消关联资产的折旧数据还没归集
        if (CollectionUtils.isEmpty(costDetailList)) {
            return;
        }

        Project project = projectMapper.selectByPrimaryKey(projectId);
        Asserts.notNull(project, ErrorCode.CTC_PROJECT_NOT_FIND);
        ProjectType projectType = projectTypeMapper.selectByPrimaryKey(project.getType());

        //归集头
        CostCollection collection = new CostCollectionDto();
        collection.setProjectId(project.getId());
        collection.setProjectCode(project.getCode());
        collection.setProjectName(project.getName());
        if (null != projectType) {
            collection.setProjectType(projectType.getName());
            collection.setCostMethodMain(projectType.getCostMethod());
        }
        collection.setType(CostCollectionEnum.BUDGET_TYPE.getCode());//预算变更
        collection.setOuId(project.getOuId());
        collection.setOuName(CacheDataUtils.findOuNameById(project.getOuId()));
        collection.setCollectionDate(new Date());
        collection.setCarryStatus(0);
        collection.setMaterialActualCost(BigDecimal.ZERO);
        collection.setMaterialPenaltyCost(BigDecimal.ZERO);
        collection.setMaterialOutsourceCost(BigDecimal.ZERO);
        collection.setMaterialDifferenceCost(BigDecimal.ZERO);
        collection.setInnerLaborCost(BigDecimal.ZERO);
        collection.setOuterLaborCost(BigDecimal.ZERO);
        collection.setFeeCost(BigDecimal.ZERO);
        collection.setCurrency("CNY");
        collection.setDeletedFlag(Boolean.FALSE);
        //资产折旧金额
        BigDecimal assetDeprnCost = costDetailList.stream().map(ProjectAssetDeprnCostDetail::getDeprnAmount).reduce(BigDecimal.ZERO, BigDecimalUtils::add);
        collection.setCostDate(new Date());
        collection.setAssetDeprnCost(assetDeprnCost);
        costCollectionMapper.insertSelective(collection);

        //归集明细
        for (ProjectAssetDeprnCostDetail costDetail : costDetailList) {
            costDetail.setCostCollectionId(collection.getId());
            costDetail.setStatus(CommonAccountStatus.NOT.code()); //未入账
            costDetail.setDeletedFlag(Boolean.FALSE);
        }
        List<List<ProjectAssetDeprnCostDetail>> lists = ListUtils.splistList(costDetailList, 200);
        lists.forEach(list -> projectAssetDeprnCostDetailExtMapper.batchInsert(list));
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveVendorPenaltyCollection(CostCollectionDto collectionDto) {
        List<VendorPenaltyDetailDto> penaltyDetailDtoList = vendorPenaltyService.getCollectionPenaltyDetail(collectionDto);
        if (ListUtils.isEmpty(penaltyDetailDtoList)) {
            return;
        }
        completeVendorPenaltyCollection(collectionDto, penaltyDetailDtoList);
    }

    private void completeVendorPenaltyCollection(CostCollectionDto collectionDto, List<VendorPenaltyDetailDto> penaltyDetailDtoList) {
        List<VendorPenaltyCostDetail> costDetails = new ArrayList<>(penaltyDetailDtoList.size());
        penaltyDetailDtoList.stream().collect(Collectors.groupingBy(e -> DateUtils.getShortDate(e.getPenaltyCreateTime())))
                .forEach((costDate, details) -> {
                    BigDecimal totalPenaltyCost = details.stream().map(VendorPenaltyDetailDto::getOccurCost)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    CostCollectionDto costCollection = BeanConverter.copy(collectionDto, CostCollectionDto.class);
                    // MR2023030285467 由PAM同步到ERP罚扣的日期调整为当前日期
                    costCollection.setCostDate(new Date());
                    costCollection.setMaterialPenaltyCost(totalPenaltyCost);
                    this.save(costCollection, 6);
                    details.forEach(e -> {
                        VendorPenaltyCostDetail costDetail = new VendorPenaltyCostDetail();
                        costDetail.setCollectionId(costCollection.getId());
                        costDetail.setPenaltyDetailId(e.getId());
                        costDetail.setPenaltyCode(e.getPenaltyCode());
                        costDetail.setVendorCode(e.getVendorCode());
                        costDetail.setVendorName(e.getVendorName());
                        costDetail.setCurrency(e.getCurrency());
                        costDetail.setConversionRate(e.getConversionRate());
                        costDetail.setAmount(e.getInvoiceAmount());
                        costDetail.setProjectCost(e.getOccurCost());
                        costDetail.setDeletedFlag(Boolean.FALSE);
                        costDetails.add(costDetail);
                    });
                });
        vendorPenaltyCostDetailExtMapper.batchInsert(costDetails);
    }

    @Override
    public void collectVendorPenaltyCost(List<VendorPenaltyDetailDto> penaltyDetailDtoList) {
        if (ListUtils.isEmpty(penaltyDetailDtoList)) {
            return;
        }
        ProjectExample projectExample = new ProjectExample();
        projectExample.createCriteria()
                .andCodeEqualTo(penaltyDetailDtoList.get(0).getProjectCode())
                .andDeletedFlagEqualTo(Boolean.FALSE);
        Project project = projectMapper.selectByExample(projectExample).get(0);
        ProjectType projectType = projectTypeService.selectByPrimaryKey(project.getType());
        CostCollectionDto collectionDto = new CostCollectionDto();
        collectionDto.setProjectId(project.getId());
        collectionDto.setProjectCode(project.getCode());
        collectionDto.setProjectName(project.getName());
        collectionDto.setProjectType(projectType.getName());
        collectionDto.setType(CostCollectionEnum.NORMAL_TYPE.getCode());
        collectionDto.setOuId(project.getOuId());
        OperatingUnit ou = CacheDataUtils.findOuById(collectionDto.getOuId());
        if (ou != null) {
            collectionDto.setOuName(ou.getOperatingUnitName());
        }
        collectionDto.setCollectionDate(DateUtils.getShortDate(new Date()));
        collectionDto.setCarryStatus(0);

        //设置成本方法
        ProjectProfitDto profitDetail = projectProfitService.findProfitDetail(project.getId());
        if (profitDetail != null) {
            collectionDto.setCostMethodMain(profitDetail.getCostMethodMain());
        }
        completeVendorPenaltyCollection(collectionDto, penaltyDetailDtoList);
    }

    @Override
    public void costCollectionDelete(Long ouId, Date endDate) {
        if (ouId != null) {
            costCollectionExtMapper.deleteZeroCostCollection(ouId, endDate);
            return;
        }
        List<Long> ouIdList = costCollectionExtMapper.selectDistinctOuId();
        if (ListUtils.isEmpty(ouIdList)) {
            return;
        }
        // 按照ou_id循环删除cost_collection表中fee_cost=0并且carry_status=0的一个月前的数据
        ouIdList.forEach(itemId -> {
            costCollectionExtMapper.deleteZeroCostCollection(itemId, endDate);
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveHroWorkingHourBillCollection(CostCollectionDto collectionDto) {
        List<HroWorkingHourBillCostDetail> billCostDetails = hroWorkingHourBillService.generateCollectionDetail(collectionDto);
        if (ListUtils.isEmpty(billCostDetails)) {
            return;
        }
        Map<Date, List<HroWorkingHourBillCostDetail>> costDetailMap = billCostDetails.stream().collect(Collectors.groupingBy(HroWorkingHourBillCostDetail::getEndDate));
        costDetailMap.forEach((costDate, details) -> {
            BigDecimal totalBillCost = details.stream().map(HroWorkingHourBillCostDetail::getBillCost)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            CostCollectionDto costCollection = BeanConverter.copy(collectionDto, CostCollectionDto.class);
            costCollection.setCostDate(costDate);
            costCollection.setOuterLaborCost(totalBillCost);
            this.save(costCollection, 2);
            details.forEach(e -> e.setCostCollectionId(costCollection.getId()));
        });
        hroWorkingHourBillCostDetailExtMapper.batchSave(billCostDetails);
    }

    private void saveProgressExecuteCost(CostCollectionDto collectionDto) {
        Project project = projectMapper.selectByPrimaryKey(collectionDto.getProjectId());
        UnitOuRel unitOuRel = CacheDataUtils.findUnitOuRelByOuId(project.getOuId());
        Long unitId = unitOuRel.getUnitId();
        Long companyId = CacheDataUtils.getTopUnitIdByUnitId(unitId);//公司id
        if (null == companyId) {
            logger.info("项目公司为空:" + unitId);
            return;
        }

        List<PurchaseContractDTO> purchaseContractDTOS = queryPurchaseContract(companyId, project.getId());
        //为空直接return
        if (ListUtils.isEmpty(purchaseContractDTOS)) return;

        Map<Long, PurchaseContractDTO> purchaseContractMap = new HashMap<>();
        List<PurchaseContractProgressDto> progressDtos = new ArrayList<>();
        purchaseContractDTOS
                .stream()
                .filter(s ->
                        ObjUtils.ofNullable(costRatioConfigDetailExtMapper.findDetail2(s.getId(), companyId))
                                .accept(i -> i != null && "03".equals(i.getCostCarryForwardMethod())) //按合同进度入账结转
                                .get()
                ).forEach(t -> {

                    PurchaseContractProgressExample progressExample = new PurchaseContractProgressExample();
                    progressExample.createCriteria()
                            .andPurchaseContractIdEqualTo(t.getId())
                            .andProgressStatusEqualTo(PurchaseContractProgressStatus.PASS.getCode())
                            .andCollectionStatusEqualTo(PurchaseContractProgressCollectionStatus.UNCOLLECTED.getCode())
                            .andDeletedFlagEqualTo(DeletedFlag.VALID.code());
                    List<PurchaseContractProgress> progressList = purchaseContractProgressMapper.selectByExample(progressExample);
                    if (ListUtil.isPresent(progressList)) {
                        //更新采购合同进度为已收集
                        progressList.forEach(s -> {
                            s.setCollectionStatus(PurchaseContractProgressCollectionStatus.COLLECTED.getCode());
                            purchaseContractProgressMapper.updateByPrimaryKeySelective(s);
                        });
                        List<PurchaseContractProgressDto> progressDtoList = BeanConverter.copy(progressList, PurchaseContractProgressDto.class);
                        progressDtos.addAll(progressDtoList);
                        purchaseContractMap.put(t.getId(), t);
                    }
                });

        //插入归集表和明细表
        if (ListUtils.isNotEmpty(progressDtos)) {
            progressDtos.forEach(i -> {
                PurchaseContractDTO contract = purchaseContractMap.get(i.getPurchaseContractId());
                //插入归集表
                CostCollection costCollection = this.collectProgressExcuse(collectionDto, project, i);
                //插明细表
                MaterialOutsourceCostDetail build = Builder.of(MaterialOutsourceCostDetail::new)
                        .with(MaterialOutsourceCostDetail::setCostCollectionId, costCollection.getId())
                        .with(MaterialOutsourceCostDetail::setPurchaseContractId, contract.getId())
                        .with(MaterialOutsourceCostDetail::setPurchaseContractCode, contract.getCode())
                        .with(MaterialOutsourceCostDetail::setPurchaseContractName, contract.getName())
                        .with(MaterialOutsourceCostDetail::setVendorId, contract.getVendorId())
                        .with(MaterialOutsourceCostDetail::setVendorCode, contract.getVendorCode())
                        .with(MaterialOutsourceCostDetail::setVendorSiteCode, contract.getVendorSiteCode())
                        .with(MaterialOutsourceCostDetail::setVendorName, contract.getVendorName())
                        .with(MaterialOutsourceCostDetail::setCurrency, contract.getCurrency())
                        .with(MaterialOutsourceCostDetail::setDeletedFlag, DeletedFlagEnum.VALID.code())
                        .with(MaterialOutsourceCostDetail::setPurchaseContractProgressId, i.getId())
                        .with(MaterialOutsourceCostDetail::setExecuteAmount, i.getExecuteAmount())
                        .with(MaterialOutsourceCostDetail::setProgressEndTime, i.getProgressEndTime())
                        .with(MaterialOutsourceCostDetail::setType, MaterialOutsourceCostDetailType.PURCHASE_CONTRACT_PROGRESS.getCode())
                        .build();
                materialOutsourceCostDetailMapper.insertSelective(build);
            });
        }
    }

    private void saveMaterialOutsourceCost(CostCollectionDto collectionDto) {
        Project project = projectMapper.selectByPrimaryKey(collectionDto.getProjectId());
        UnitOuRel unitOuRel = CacheDataUtils.findUnitOuRelByOuId(project.getOuId());
        Long unitId = unitOuRel.getUnitId();
        Long companyId = CacheDataUtils.getTopUnitIdByUnitId(unitId);//公司id
        if (null == companyId) {
            logger.info("项目公司为空:" + unitId);
            return;
        }

        List<PurchaseContractDTO> purchaseContractDTOS = queryPurchaseContract(companyId, project.getId());
        //为空直接return
        if (ListUtils.isEmpty(purchaseContractDTOS)) return;

        List<PaymentInvoiceDto> payments = new ArrayList<>();
        purchaseContractDTOS
                .stream()
                .filter(s ->
                        ObjUtils.ofNullable(costRatioConfigDetailExtMapper.findDetail2(s.getId(), companyId))
                                .accept(i -> i != null && "02".equals(i.getCostCarryForwardMethod()))
                                .get()
                ).forEach(t -> {
                    PaymentInvoiceExample paymentInvoiceExample = new PaymentInvoiceExample();
                    paymentInvoiceExample.createCriteria()
                            .andPurchaseContractIdEqualTo(t.getId())
                            .andStatusEqualTo(PaymentInvoiceStatusEnum.PASS.getCode())
                            .andErpStatusEqualTo(1)
                            .andCollectionStatusEqualTo(0)
                            .andSourceEqualTo(PaymentInvoiceSourceEnum.PAYMENT.getCode())
                            .andDeletedFlagEqualTo(false);
                    List<PaymentInvoice> paymentInvoices = paymentInvoiceMapper.selectByExample(paymentInvoiceExample);
                    //更新发票为已归集
                    paymentInvoices.forEach(s -> {
                        s.setCollectionStatus(1);
                        paymentInvoiceMapper.updateByPrimaryKey(s);
                    });
                    List<PaymentInvoiceDto> paymentInvoiceDtoList = BeanConverter.copy(paymentInvoices, PaymentInvoiceDto.class);
                    paymentInvoiceDtoList.forEach(s -> {
                        s.setPurchaseContractName(t.getName());
                        s.setVendorId(t.getVendorId());
                        s.setVendorCode(t.getVendorCode());
                        s.setVendorName(t.getVendorName());
                        s.setVendorSiteCode(t.getVendorSiteCode());
                        s.setConversionRate(t.getConversionRate());
                        s.setCurrency(t.getCurrency());
                    });
                    payments.addAll(paymentInvoiceDtoList);
                });

        //插入明细表和归集表
        if (ListUtils.isNotEmpty(payments)) {
            payments.forEach(i -> {
                //插入归集表
                CostCollection costCollection = this.saveMaterialCostOut(collectionDto, project, i);
                // 汇率
                BigDecimal conversionRate = Optional.ofNullable(i.getConversionRate()).orElse(BigDecimal.ONE);
                // 发票含税金额
                BigDecimal totalInvoiceIncludedPrice = Optional.ofNullable(i.getTotalInvoiceIncludedPrice()).orElse(BigDecimal.ZERO);
                // 发票税额
                BigDecimal taxAmount = Optional.ofNullable(i.getTaxAmount()).orElse(BigDecimal.ZERO);
                // 本位币金额
                BigDecimal localCurrencyAmount = totalInvoiceIncludedPrice.subtract(taxAmount).multiply(conversionRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                //插明细表
                MaterialOutsourceCostDetail build = Builder.of(MaterialOutsourceCostDetail::new)
                        .with(MaterialOutsourceCostDetail::setCostCollectionId, costCollection.getId())
                        .with(MaterialOutsourceCostDetail::setApInvoiceCode, i.getApInvoiceCode())
                        .with(MaterialOutsourceCostDetail::setPurchaseContractCode, i.getPurchaseContractCode())
                        .with(MaterialOutsourceCostDetail::setPurchaseContractName, i.getPurchaseContractName())
                        .with(MaterialOutsourceCostDetail::setInvoiceAmount, totalInvoiceIncludedPrice)
                        .with(MaterialOutsourceCostDetail::setTaxAmount, taxAmount)
                        .with(MaterialOutsourceCostDetail::setCreateAt, new Date())
                        .with(MaterialOutsourceCostDetail::setDeletedFlag, 0)
                        .with(MaterialOutsourceCostDetail::setVendorId, i.getVendorId())
                        .with(MaterialOutsourceCostDetail::setVendorCode, i.getVendorCode())
                        .with(MaterialOutsourceCostDetail::setVendorSiteCode, i.getVendorSiteCode())
                        .with(MaterialOutsourceCostDetail::setVendorName, i.getVendorName())
                        .with(MaterialOutsourceCostDetail::setInvoiceCurrency, i.getCurrency())
                        .with(MaterialOutsourceCostDetail::setLocalCurrency, "CNY") // todo 发票外包方案暂未实现，默认CNY
                        .with(MaterialOutsourceCostDetail::setLocalCurrencyAmount, localCurrencyAmount)
                        .with(MaterialOutsourceCostDetail::setConversionRate, conversionRate)
                        .with(MaterialOutsourceCostDetail::setPaymentInvoiceId, i.getId())
                        .with(MaterialOutsourceCostDetail::setInvoiceDate, i.getAuditDate() != null ? i.getAuditDate() : i.getInvoiceDate())
                        .with(MaterialOutsourceCostDetail::setType, MaterialOutsourceCostDetailType.PAYMENT_INVOICE.getCode())
                        .build();
                materialOutsourceCostDetailMapper.insertSelective(build);
            });
        }
    }

    /**
     * 生成默认数据
     *
     * @param dto
     */
    private void saveDefaultCollection(CostCollectionDto dto) {
        CostCollectionExample example = new CostCollectionExample();
        example.createCriteria().
                andProjectIdEqualTo(dto.getProjectId()).
                andCollectionDateEqualTo(dto.getCollectionDate());
        List<CostCollection> collectionList = costCollectionMapper.selectByExample(example);
        if (ListUtils.isEmpty(collectionList)) {
            dto.setCostDate(dto.getCollectionDate());
            dto.setMaterialActualCost(BigDecimal.ZERO);
            dto.setMaterialPenaltyCost(BigDecimal.ZERO);
            dto.setInnerLaborCost(BigDecimal.ZERO);
            dto.setOuterLaborCost(BigDecimal.ZERO);
            dto.setFeeCost(BigDecimal.ZERO);
            dto.setMaterialOutsourceCost(BigDecimal.ZERO);
            dto.setMaterialDifferenceCost(BigDecimal.ZERO);
            dto.setAssetDeprnCost(BigDecimal.ZERO);
            dto.setCurrency("CNY");//需求要求写死
            dto.setDeletedFlag(Boolean.FALSE);
            CostCollection costCollection = new CostCollection();
            BeanUtils.copyProperties(dto, costCollection);
            costCollectionMapper.insert(costCollection);
        }
    }

    /**
     * 物料实际成本计算
     *
     * @param collectionDto
     */
    private void saveMaterialActualCost(CostCollectionDto collectionDto) {
        Map<String, List<MaterialActualCostDetailDto>> map1 = new HashMap<>();
        List<MaterialActualCostDetailDto> actualCostDetailDtoList = materialActualCostDetailService.collection(collectionDto, collectionDto.getCollectionDate());
        if (!CollectionUtils.isEmpty(actualCostDetailDtoList)) {
            //按成本发生日期分组
            for (MaterialActualCostDetailDto costDetailDto : actualCostDetailDtoList) {
                // erp没有返回单价不参与归集
                if (costDetailDto.getActualAmount() == null
                        || costDetailDto.getActualCost() == null) {
                    continue;
                }
                String actualDate = DateUtils.formatDate(costDetailDto.getActualDate());
                if (map1.containsKey(actualDate)) {
                    map1.get(actualDate).add(costDetailDto);
                } else {
                    List<MaterialActualCostDetailDto> newList = new ArrayList<>();
                    newList.add(costDetailDto);
                    map1.put(actualDate, newList);
                }
            }
            //保存归集和实际成本
            for (List<MaterialActualCostDetailDto> costDetailDtoList : map1.values()) {
                CostCollectionDto newCollection = new CostCollectionDto();
                BeanUtils.copyProperties(collectionDto, newCollection);
                newCollection.setCostDate(costDetailDtoList.get(0).getActualDate());
                for (MaterialActualCostDetailDto dto : costDetailDtoList) {
                    //物料成本计算：数量*单位价格
                    BigDecimal totalCost = dto.getActualAmount().multiply(dto.getActualCost()).setScale(2, BigDecimal.ROUND_HALF_UP);
                    if (String.valueOf(2).equals(String.valueOf(dto.getType()))) {
                        //退料金额为负
                        totalCost = totalCost.multiply(new BigDecimal(-1)).setScale(2, BigDecimal.ROUND_HALF_UP);
                    }
                    newCollection.setMaterialActualCost(BigDecimalUtils.add(newCollection.getMaterialActualCost(), totalCost));
                }
                this.save(newCollection, 1);
                materialActualCostDetailService.save(costDetailDtoList, newCollection.getId());
            }
        }
    }

    private void saveMaterialDifferenceCost(CostCollectionDto collectionDto) {
        Map<String, List<DifferenceShareResultDetail>> map1 = new HashMap<>();
        List<DifferenceShareResultDetail> differenceShareResultDetails = differenceShareResultDetailService.collection(collectionDto.getProjectId());
        if (!CollectionUtils.isEmpty(differenceShareResultDetails)) {
            //按成本发生日期分组
            for (DifferenceShareResultDetail costDetailDto : differenceShareResultDetails) {
                String actualDate = DateUtils.formatDate(costDetailDto.getCostDate());
                if (map1.containsKey(actualDate)) {
                    map1.get(actualDate).add(costDetailDto);
                } else {
                    List<DifferenceShareResultDetail> newList = new ArrayList<>();
                    newList.add(costDetailDto);
                    map1.put(actualDate, newList);
                }
            }
            //保存归集和实际成本
            for (List<DifferenceShareResultDetail> costDetailDtoList : map1.values()) {
                CostCollectionDto newCollection = new CostCollectionDto();
                BeanUtils.copyProperties(collectionDto, newCollection);
                newCollection.setCostDate(costDetailDtoList.get(0).getCostDate());
                for (DifferenceShareResultDetail differenceShareResultDetail : costDetailDtoList) {
                    BigDecimal amount = differenceShareResultDetail.getAmount() == null ? BigDecimal.ZERO : differenceShareResultDetail.getAmount();
                    newCollection.setMaterialDifferenceCost(BigDecimalUtils.add(newCollection.getMaterialDifferenceCost(), amount));
                }
                this.save(newCollection, 4);
                differenceShareResultDetailService.updateCollectionId(costDetailDtoList, newCollection.getId());
            }
        }
    }

    /**
     * 人工成本（内/外部）计算
     *
     * @param collectionDto
     */
    private void saveLaborCost(CostCollectionDto collectionDto, Map<Long, Map<String, BigDecimal>> unitMap) {
        Map<String, List<LaborCostDetail>> map1 = new HashMap<>();
        List<LaborCostDetail> laborCostDetailList = laborCostDetailService.collection(collectionDto, collectionDto.getCollectionDate(), unitMap);
        if (!CollectionUtils.isEmpty(laborCostDetailList)) {
            // MR2023030285467 由填报日期分组调整为审批通过日期
            for (LaborCostDetail laborCostDetail : laborCostDetailList) {
                String approveTime = DateUtils.formatDate(laborCostDetail.getSystemApplyDate());
                if (map1.containsKey(approveTime)) {
                    map1.get(approveTime).add(laborCostDetail);
                } else {
                    List<LaborCostDetail> newList = new ArrayList<>();
                    newList.add(laborCostDetail);
                    map1.put(approveTime, newList);
                }
            }
            //保存归集和人工成本
            for (List<LaborCostDetail> laborCostDetails : map1.values()) {
                CostCollectionDto newCollection = new CostCollectionDto();
                BeanUtils.copyProperties(collectionDto, newCollection);
                newCollection.setCostDate(laborCostDetails.get(0).getSystemApplyDate());
                //总人工成本计算
                BigDecimal innerLaborCost = new BigDecimal(0);
                BigDecimal outerLaborCost = new BigDecimal(0);
                BigDecimal recruitOuterLaborCost = new BigDecimal(0);
                for (LaborCostDetail laborCostDetail : laborCostDetails) {
                    if (UserType.INTERNAL.code().equals(laborCostDetail.getUserType())) {
                        innerLaborCost = innerLaborCost.add(laborCostDetail.getCostTotal());//内部
                    } else if (UserType.HRO.code().equals(laborCostDetail.getUserType())) {
                        outerLaborCost = outerLaborCost.add(laborCostDetail.getCostTotal());//人力点工
                    } else if (UserType.RECRUIT.code().equals(laborCostDetail.getUserType())) {
                        recruitOuterLaborCost = recruitOuterLaborCost.add(laborCostDetail.getCostTotal());//自招外包
                    }
                }
                newCollection.setInnerLaborCost(innerLaborCost);
                newCollection.setOuterLaborCost(recruitOuterLaborCost);
                this.save(newCollection, 2);

                /*
                  add 外部人力成本工时生成归集表的数据时，单独生成一条成本归集数据，
                  此条成本归集数据不参与项目成本结转，不能与其他类型的成本在同一条成本归集数据中；结转状态为“不可结转”
                 */
                CostCollectionDto outerLaborCostCollection = new CostCollectionDto();
                BeanUtils.copyProperties(collectionDto, outerLaborCostCollection);
                collectionDto.setId(null);
                outerLaborCostCollection.setOuterLaborCost(outerLaborCost);
                outerLaborCostCollection.setCarryStatus(2); //TODO 需求是状态改为不可结转,不过这个这字段是bool类型,设置为已结转不行的话就需要修改类型... 状态2为不无需入账
                outerLaborCostCollection.setCostDate(laborCostDetails.get(0).getSystemApplyDate());
                outerLaborCostCollection.setStatus(3);
                this.save(outerLaborCostCollection, 2);

                laborCostDetailService.batchSave(newCollection.getId(), laborCostDetails.stream()
                        .filter(s -> UserType.INTERNAL.code().equals(s.getUserType()) || UserType.RECRUIT.code().equals(s.getUserType()))
                        .collect(Collectors.toList()));
                laborCostDetailService.batchSave(outerLaborCostCollection.getId(), laborCostDetails.stream()
                        .filter(s -> UserType.HRO.code().equals(s.getUserType()))
                        .collect(Collectors.toList()));
            }

            // 更新工时表
            laborCostDetailList.forEach(laborCostDetail -> {
                final Long workingHourId = laborCostDetail.getWorkingHourId();
                final BigDecimal costMoney = laborCostDetail.getCostMoney();

                WorkingHour workingHour = new WorkingHour();
                workingHour.setId(workingHourId);
                workingHour.setActualCostMoney(costMoney);

                workingHourMapper.updateByPrimaryKeySelective(workingHour);
            });
        }
    }

    private void saveFeeCost(CostCollectionDto collectionDto) {
        Map<String, List<FeeCostDetailDto>> map1 = new HashMap<>();
        List<FeeCostDetailDto> feeCostDetailDtoList = feeCostDetailService.collection(collectionDto, collectionDto.getCollectionDate());
        if (!CollectionUtils.isEmpty(feeCostDetailDtoList)) {
            //按填报日期分组
            for (FeeCostDetailDto feeCostDetailDto : feeCostDetailDtoList) {
                String glDate = DateUtils.formatDate(feeCostDetailDto.getGlDate());
                if (map1.containsKey(glDate)) {
                    map1.get(glDate).add(feeCostDetailDto);
                } else {
                    List<FeeCostDetailDto> newList = new ArrayList<>();
                    newList.add(feeCostDetailDto);
                    map1.put(glDate, newList);
                }
            }
            //保存归集和费用成本
            for (List<FeeCostDetailDto> feeCostDetailDtos : map1.values()) {
                CostCollectionDto newCollection = new CostCollectionDto();
                BeanUtils.copyProperties(collectionDto, newCollection);
                newCollection.setCostDate(feeCostDetailDtos.get(0).getGlDate());
                //总费用成本计算
                BigDecimal totalAmount = new BigDecimal(0);
                for (FeeCostDetailDto dto : feeCostDetailDtos) {
                    totalAmount = totalAmount.add(dto.getInvoiceAmount());
                }
                newCollection.setFeeCost(totalAmount);
                this.save(newCollection, 3);
                feeCostDetailService.batchSave(newCollection.getId(), feeCostDetailDtos);
            }
        }
    }

    @Override
    public Integer updateCollection(Long projectId, Date collectionDateStart, Date collectionDateEnd) {
        CostCollectionExample example = new CostCollectionExample();
        example.createCriteria().andProjectIdEqualTo(projectId).
                andCollectionDateBetween(collectionDateStart, collectionDateEnd).
                andDeletedFlagEqualTo(Boolean.FALSE);
        List<Date> dateList = DateUtils.getBetweenDates(collectionDateStart, collectionDateEnd);
        List<CostCollection> collectionList = costCollectionMapper.selectByExample(example);
        List<Date> reCollectionList = new ArrayList<>();
        reCollectionList.add(new Date());//当天的日期必须重新归集
        //检查范围内的日期是否缺少数据
        for (Date checkDate : dateList) {
            boolean isCollected = false;
            for (CostCollection collection : collectionList) {
                if (checkDate.equals(collection.getCollectionDate())) {
                    isCollected = true;
                    break;
                }
            }
            if (!isCollected) {
                reCollectionList.add(checkDate);
            }
        }
        //执行归集
        ProjectDto projectDto = projectService.findDetail(projectId);
        // 获取所有使用单位人力费率配置
        Map<Long, Map<String, BigDecimal>> unitMap = getLaborCostDetailMap();

        for (Date collectionDate : reCollectionList) {
            this.collection(projectDto, collectionDate, unitMap);
        }
        return reCollectionList.size();
    }

    @Override
    public CostCollectionDto queryCostCollectionById(Long collectionId) {
        CostCollectionDto costCollectionDto = new CostCollectionDto();

        //归集头信息
        CostCollection costCollection = costCollectionMapper.selectByPrimaryKey(collectionId);
        BeanUtils.copyProperties(costCollection, costCollectionDto);

        //物料实际成本
        List<MaterialActualCostDetailDto> materialActualCostDetailDtos = materialActualCostDetailService.queryByCollectionId(collectionId);
        if (!CollectionUtils.isEmpty(materialActualCostDetailDtos)) {
            costCollectionDto.setMaterialActualCostDetailDtos(materialActualCostDetailDtos);
        }

        //物料成本-差异分摊
        List<DifferenceShareResultDetailDto> differenceShareResultDetailDtos = differenceShareResultDetailService.queryByCollectionId(costCollection.getProjectId(), collectionId);
        if (!CollectionUtils.isEmpty(differenceShareResultDetailDtos)) {
            costCollectionDto.setDifferenceShareResultDetailDtos(differenceShareResultDetailDtos);
        }

        //物料外包成本【按发票入账结转】
        MaterialOutsourceCostDetailExample materialOutsourceCostDetailExample = new MaterialOutsourceCostDetailExample();
        materialOutsourceCostDetailExample.createCriteria().andCostCollectionIdEqualTo(costCollection.getId()).andTypeEqualTo(MaterialOutsourceCostDetailType.PAYMENT_INVOICE.getCode());
        List<MaterialOutsourceCostDetail> materialOutsourceCostDetails = materialOutsourceCostDetailMapper.selectByExample(materialOutsourceCostDetailExample);
        if (materialOutsourceCostDetails.size() > 0) {
            costCollectionDto.setMaterialOutsourceCostDetailDtos(BeanConverter.convert(materialOutsourceCostDetails, MaterialOutsourceCostDetailDto.class));
        }

        //物料外包成本【按合同进度入账结转】
        materialOutsourceCostDetailExample = new MaterialOutsourceCostDetailExample();
        materialOutsourceCostDetailExample.createCriteria().andCostCollectionIdEqualTo(costCollection.getId()).andTypeEqualTo(MaterialOutsourceCostDetailType.PURCHASE_CONTRACT_PROGRESS.getCode());
        List<MaterialOutsourceCostDetail> materialOutsourceCostDetailProgressList = materialOutsourceCostDetailMapper.selectByExample(materialOutsourceCostDetailExample);
        if (materialOutsourceCostDetailProgressList.size() > 0) {
            costCollectionDto.setMaterialOutsourceCostDetailProgressDtos(BeanConverter.convert(materialOutsourceCostDetailProgressList, MaterialOutsourceCostDetailDto.class));
        }

        //人工成本（内部）
        List<LaborCostDetailDto> innerLaborCostDetailDtos = laborCostDetailService.queryDetailByCollectionIdAndType(collectionId, Arrays.asList("1"));
        if (!CollectionUtils.isEmpty(innerLaborCostDetailDtos)) {
            costCollectionDto.setInnerLaborCostDetailDtos(innerLaborCostDetailDtos);
        }

        //人工成本（外部）
        List<LaborCostDetailDto> outerLaborCostDetailDtos = laborCostDetailService.queryDetailByCollectionIdAndType(collectionId, Arrays.asList("2", "3"));
        if (!CollectionUtils.isEmpty(outerLaborCostDetailDtos)) {
            costCollectionDto.setOuterlaborCostDetailDtos(outerLaborCostDetailDtos);
        }

        //费用成本
        List<FeeCostDetailDto> feeCostDetailDtos = feeCostDetailService.queryByCollection(collectionId);
        if (!CollectionUtils.isEmpty(feeCostDetailDtos)) {
            costCollectionDto.setFeeCostDetailDtos(feeCostDetailDtos);
        }

        // 工时对账单成本
        costCollectionDto.setHroWorkingHourBillCostDetailDtos(hroWorkingHourBillService.queryByCollection(collectionId));

        // 材料罚扣
        costCollectionDto.setVendorPenaltyCostDetails(vendorPenaltyService.queryByCollection(collectionId));

        // 资产折旧成本
        ProjectAssetDeprnCostDetailExample projectAssetDeprnCostDetailExample = new ProjectAssetDeprnCostDetailExample();
        projectAssetDeprnCostDetailExample.createCriteria().andCostCollectionIdEqualTo(collectionId).andDeletedFlagEqualTo(Boolean.FALSE);
        List<ProjectAssetDeprnCostDetail> projectAssetDeprnCostDetailList = projectAssetDeprnCostDetailMapper.selectByExample(projectAssetDeprnCostDetailExample);
        costCollectionDto.setProjectAssetDeprnCostDetails(BeanConverter.copy(projectAssetDeprnCostDetailList, ProjectAssetDeprnCostDetailDto.class));

        return costCollectionDto;
    }

    @Override
    public CostCollection sum(Long projectId) {
        Map param = new HashMap<>();
        param.put("projectId", projectId);
        final CostCollectionDto sumCostCollection = costCollectionExtMapper.sum(param);
        return sumCostCollection;
    }

    @Override
    public void restartCollectionByOu(Long ouId, Date collectionDate) {
        final List<LaborCostDetail> laborCostDetails = laborCostDetailService.findNeedRestartCollection();
        if (!CollectionUtils.isEmpty(laborCostDetails)) {
            final List<LaborCostRankRealMonthDetailDto> validDetails = laborCostRealMonthHelper.getValidDetails(null, null, null);

            // 获取所有使用单位人力费率配置
            Map<Long, Map<String, BigDecimal>> unitMap = getLaborCostDetailMap();

            Map<Long, List<LaborCostDetail>> projectLaborCostDetailMap = new HashMap<>();
            laborCostDetails.forEach(laborCostDetail -> {
                final Long projectId = laborCostDetail.getProjectId();
                List<LaborCostDetail> projectLaborCostDetails = projectLaborCostDetailMap.get(projectId);
                if (projectLaborCostDetails == null) {
                    projectLaborCostDetails = new ArrayList<>();
                    projectLaborCostDetailMap.put(projectId, projectLaborCostDetails);
                }

                projectLaborCostDetails.add(laborCostDetail);
            });

            final Set<Map.Entry<Long, List<LaborCostDetail>>> entries = projectLaborCostDetailMap.entrySet();
            for (Map.Entry<Long, List<LaborCostDetail>> entry : entries) {
                final Long projectId = entry.getKey();
                final List<LaborCostDetail> projectLaborCostDetails = entry.getValue();

                final Project project = projectService.selectByPrimaryKey(projectId);
                final Long unitId = project.getUnitId();
                final Unit unit = CacheDataUtils.findUnitById(unitId);
                final Long bizUnitId = unit.getParentId();

                // 人力费率
                final Map<String, BigDecimal> unitPriceMap = unitMap.get(bizUnitId);

                // 归集变化值
                Map<Long, BigDecimal> collectionNeedUpdateMap = new HashMap<>();

                projectLaborCostDetails.forEach(laborCostDetail -> {
                    final Date applyDate = laborCostDetail.getApplyDate();
                    final Long workingHourId = laborCostDetail.getWorkingHourId();
                    final Long costCollectionId = laborCostDetail.getCostCollectionId();

                    WorkingHourExample example = new WorkingHourExample();
                    final WorkingHourExample.Criteria criteria = example.createCriteria();
                    criteria.andIdEqualTo(workingHourId);
                    final List<WorkingHour> workingHours = workingHourService.selectByExample(example);
                    if (!CollectionUtils.isEmpty(workingHours)) {
                        final WorkingHour workingHour = workingHours.get(0);
                        final String level = workingHour.getLevel();
                        final String applyMonth = DateUtils.format(applyDate, DateUtils.FORMAT_YEAR_MONTH);

                        final BigDecimal unitPrice = unitPriceMap.get(applyMonth + "-" + level);
                        if (unitPrice != null && unitPrice.compareTo(BigDecimal.ZERO) > 0) {

                            laborCostDetail.setCostMoney(unitPrice);
                            final BigDecimal costTotal = unitPrice.divide(new BigDecimal("8")).multiply(laborCostDetail.getActualWorkingHours());
                            laborCostDetail.setCostTotal(costTotal);

                            // 变化的值
                            BigDecimal addValue = collectionNeedUpdateMap.getOrDefault(costCollectionId, BigDecimal.ZERO);
                            collectionNeedUpdateMap.put(costCollectionId, addValue.add(costTotal));
                        }
                    }

                });

                // 是否需要更新成本归集的值
                final Set<Map.Entry<Long, BigDecimal>> collectionEntries = collectionNeedUpdateMap.entrySet();
                for (Map.Entry<Long, BigDecimal> collectionEntry : collectionEntries) {
                    final Long collectionId = collectionEntry.getKey();
                    final BigDecimal addValue = collectionEntry.getValue();

                    final CostCollection costCollection = costCollectionMapper.selectByPrimaryKey(collectionId);
                    if (costCollection != null) {
                        final BigDecimal innerLaborCost = costCollection.getInnerLaborCost();
                        final BigDecimal newInnerLaborCost = innerLaborCost.add(addValue);

                        costCollection.setInnerLaborCost(newInnerLaborCost);
                        costCollectionMapper.updateByPrimaryKeySelective(costCollection);

                        logger.info("重新归集：{}，变更前：{}，变更后：{}，变更新增人力成本：{},",
                                collectionId, newInnerLaborCost, costCollection.getInnerLaborCost(), addValue);
                    }
                }
            }
        }
    }

    @Override
    public List<CostCollectionDto> sumByCarryoverBillId(Collection<Long> carryoverBillIds) {
        final List<CostCollectionDto> costCollectionDtos = costCollectionExtMapper.sumByCarryoverBillId(carryoverBillIds);
        return costCollectionDtos;
    }

    @Override
    public List<CostCollectionDto> queryCollectionSummaryExcludeSumWorkingHour(CostCollectionDto query) {
        final List<CostCollectionDto> costCollectionDtos = costCollectionExtMapper.queryCollectionSummaryExcludeSumWorkingHour(query);
        return costCollectionDtos;
    }

    @Override
    public int releaseCostCollectionByCarryoverBillId(Long carryoverBillId) {
        int n = 0;
        if (carryoverBillId != null) {
            Boolean flag = checkPurchaseContract(carryoverBillId); // 查询是否按发票入账结转 或者 按合同进度入账结转
            if (flag) {
                n = costCollectionExtMapper.releaseCostCollectionByCarryoverBillId_PI(carryoverBillId);
            } else {
                n = costCollectionExtMapper.releaseCostCollectionByCarryoverBillId(carryoverBillId);
            }
            costCollectionExtMapper.releaseCostCollectionRelByCarryoverBillId(carryoverBillId);
        }
        return n;
    }

    private Boolean checkPurchaseContract(Long carryoverBillId) {
        CarryoverBill carryoverBill = carryoverBillMapper.selectByPrimaryKey(carryoverBillId);
        UnitOuRel unitOuRel = CacheDataUtils.findUnitOuRelByOuId(carryoverBill.getOuId());
        Long unitId = unitOuRel.getUnitId();
        Long companyId = CacheDataUtils.getTopUnitIdByUnitId(unitId);//公司id
        String organizationCustomDictName = "采购合同类型";
        List<String> typeNames = new ArrayList<>();
        Set<String> typeNameSet = organizationCustomDictService.queryByName(organizationCustomDictName, companyId, OrgCustomDictOrgFrom.getOrgCustomDictOrgFrom(OrgCustomDictOrgFrom.COMPANY.code()));

        if (!org.springframework.util.CollectionUtils.isEmpty(typeNameSet)) {
            typeNames.addAll(typeNameSet);
        }
        List<PurchaseContractDTO> purchaseContractDTOS = purchaseContractService.findPurchaseContractListByProjectAndTypeNames(carryoverBill.getProjectId(), typeNames, true);
        MaterialOutsourcingContractConfig contractConfig = null;
        if (ListUtils.isNotEmpty(purchaseContractDTOS)) {
            contractConfig = costRatioConfigDetailExtMapper.findDetail2(purchaseContractDTOS.get(0).getId(), companyId);
        }

        if (null != contractConfig && ("02".equals(contractConfig.getCostCarryForwardMethod()) || "03".equals(contractConfig.getCostCarryForwardMethod()))) {
            return true;
        }
        return false;
    }

    public CostCollection collectProgressExcuse(CostCollectionDto collectionDto, Project project, PurchaseContractProgressDto dto) {
        CostCollection costCollection = new CostCollectionDto();
        costCollection.setProjectId(project.getId());
        costCollection.setProjectCode(project.getCode());
        costCollection.setProjectName(project.getName());
        ProjectType type = projectTypeMapper.selectByPrimaryKey(project.getType());
        if (null != type) {
            costCollection.setProjectType(type.getName());
            costCollection.setCostMethodMain(type.getCostMethod());
        }
        costCollection.setStatus(0); //入账状态：未入账
        costCollection.setType(CostCollectionEnum.NORMAL_TYPE.getCode());
        costCollection.setOuId(project.getOuId());
        costCollection.setOuName(CacheDataUtils.findOuById(collectionDto.getOuId()).getOperatingUnitName());
        costCollection.setCollectionDate(new Date());
        costCollection.setCarryStatus(0); //结转状态：未结转
        costCollection.setMaterialActualCost(BigDecimal.ZERO);
        costCollection.setInnerLaborCost(BigDecimal.ZERO);
        costCollection.setOuterLaborCost(BigDecimal.ZERO);
        costCollection.setFeeCost(BigDecimal.ZERO);
        costCollection.setMaterialDifferenceCost(BigDecimal.ZERO);
        costCollection.setMaterialDifferenceCost(BigDecimal.ZERO);
        costCollection.setCurrency("CNY");
        costCollection.setDeletedFlag(DeletedFlag.VALID.code());
        // 汇率
        BigDecimal conversionRate = Optional.ofNullable(dto.getConversionRate()).orElse(BigDecimal.ONE);
        // 归集金额为：当期进度执行金额（不含税）* 合同进度执行部分的汇率
        costCollection.setMaterialOutsourceCost(BigDecimalUtils.multiplyAndScale(dto.getExecuteAmount(), conversionRate));
        // 成本发生日期取：合同进度执行结束时间
        // MR2023030285467调整修改为“采购合同进展”审批通过日期
        costCollection.setCostDate(dto.getApproveAt() != null ? dto.getApproveAt() : dto.getProgressEndTime());
        costCollectionMapper.insert(costCollection);

        return costCollection;
    }

    @Override
    public CostCollection saveMaterialCostOut(CostCollectionDto collectionDto, Project project, PaymentInvoiceDto paymentInvoice) {
        CostCollection costCollection = new CostCollectionDto();
        costCollection.setProjectId(project.getId());
        costCollection.setProjectCode(project.getCode());
        costCollection.setProjectName(project.getName());
        ProjectType type = projectTypeMapper.selectByPrimaryKey(project.getType());
        if (null != type) {
            costCollection.setProjectType(type.getName());
            costCollection.setCostMethodMain(type.getCostMethod());
        }
        costCollection.setStatus(0);
        costCollection.setType(CostCollectionEnum.NORMAL_TYPE.getCode());
        costCollection.setOuId(project.getOuId());
        costCollection.setOuName(CacheDataUtils.findOuNameById(project.getOuId()));
        costCollection.setCollectionDate(new Date());
        costCollection.setCarryStatus(0);
        costCollection.setMaterialActualCost(BigDecimal.ZERO);
        costCollection.setInnerLaborCost(BigDecimal.ZERO);
        costCollection.setOuterLaborCost(BigDecimal.ZERO);
        costCollection.setFeeCost(BigDecimal.ZERO);
        costCollection.setMaterialDifferenceCost(BigDecimal.ZERO);
        costCollection.setCurrency("CNY");
        costCollection.setDeletedFlag(Boolean.FALSE);
        // 合同汇率
        BigDecimal conversionRate = Optional.ofNullable(paymentInvoice.getConversionRate()).orElse(BigDecimal.ONE);
        // 发票金额（不含税）
        BigDecimal invoiceAmount = BigDecimalUtils.subtract(paymentInvoice.getTotalInvoiceIncludedPrice(), paymentInvoice.getTaxAmount());
        // 归集金额为：发票金额（不含税）* 合同汇率
        costCollection.setMaterialOutsourceCost(invoiceAmount.multiply(conversionRate).setScale(2, BigDecimal.ROUND_HALF_UP));
        costCollection.setCostDate(paymentInvoice.getAuditDate());
        costCollectionMapper.insert(costCollection);

        return costCollection;
    }

    private List<PurchaseContractDTO> queryPurchaseContract(Long companyId, Long projectId) {
        List<PurchaseContractDTO> purchaseContractDTOS = null;
        if (companyId != null && projectId != null) {
            MaterialOutsourcingContractConfigExample example = new MaterialOutsourcingContractConfigExample();
            MaterialOutsourcingContractConfigExample.Criteria criteria = example.createCriteria();
            criteria.andUnitIdEqualTo(companyId).andDeletedFlagEqualTo(false);
            List<MaterialOutsourcingContractConfig> contractConfigs = contractConfigMapper.selectByExample(example);
            List<String> typeNames = contractConfigs.stream().map(MaterialOutsourcingContractConfig::getOutsourcingContractTypeName).collect(Collectors.toList());
            if (ListUtil.isPresent(typeNames)) {
                purchaseContractDTOS = purchaseContractService.findPurchaseContractListByProjectAndTypeNames(projectId, typeNames, true);
            }
        }
        return purchaseContractDTOS;
    }
}
