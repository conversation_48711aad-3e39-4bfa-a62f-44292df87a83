package com.midea.pam.ctc.eam.service.impl.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.pam.common.base.EsbResponse;
import com.midea.pam.common.basedata.dto.OrganizationRelDto;
import com.midea.pam.common.basedata.dto.VendorSiteBankDto;
import com.midea.pam.common.basedata.entity.OperatingUnit;
import com.midea.pam.common.basedata.entity.OrganizationRel;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.dto.ContractDTO;
import com.midea.pam.common.ctc.dto.ContractHisDTO;
import com.midea.pam.common.ctc.dto.EamPaymentApplyInfoDto;
import com.midea.pam.common.ctc.dto.EamPurchaseInfoDto;
import com.midea.pam.common.ctc.dto.InvoiceApplyHeaderDto;
import com.midea.pam.common.ctc.dto.InvoiceReceivableDto;
import com.midea.pam.common.ctc.dto.SdpTradeResultResponseEleDto;
import com.midea.pam.common.ctc.eam.PmsAdviserInfo;
import com.midea.pam.common.ctc.eam.PmsDeviceInfo;
import com.midea.pam.common.ctc.eam.PmsPayInfo;
import com.midea.pam.common.ctc.eam.PmsPayStatus;
import com.midea.pam.common.ctc.eam.PmsPurInfo;
import com.midea.pam.common.ctc.eam.PmsResult;
import com.midea.pam.common.ctc.entity.BusinessApplyRel;
import com.midea.pam.common.ctc.entity.BusinessApplyRelExample;
import com.midea.pam.common.ctc.entity.Contract;
import com.midea.pam.common.ctc.entity.ContractEampurchaseRelation;
import com.midea.pam.common.ctc.entity.ContractEampurchaseRelationExample;
import com.midea.pam.common.ctc.entity.ContractExample;
import com.midea.pam.common.ctc.entity.EamPaymentApplyInfo;
import com.midea.pam.common.ctc.entity.EamPaymentApplyInfoExample;
import com.midea.pam.common.ctc.entity.EamPurchaseAdviserInfo;
import com.midea.pam.common.ctc.entity.EamPurchaseAdviserInfoExample;
import com.midea.pam.common.ctc.entity.EamPurchaseDeviceInfo;
import com.midea.pam.common.ctc.entity.EamPurchaseDeviceInfoExample;
import com.midea.pam.common.ctc.entity.EamPurchaseInfo;
import com.midea.pam.common.ctc.entity.EamPurchaseInfoExample;
import com.midea.pam.common.ctc.entity.EamPurchasePayInfo;
import com.midea.pam.common.ctc.entity.EamPurchasePayInfoExample;
import com.midea.pam.common.ctc.entity.InvoiceReceivable;
import com.midea.pam.common.ctc.entity.InvoiceReceivableExample;
import com.midea.pam.common.ctc.entity.RdmSettlementSheet;
import com.midea.pam.common.ctc.entity.ResendExecute;
import com.midea.pam.common.ctc.excelVo.EamPaymentApplyExcelVo;
import com.midea.pam.common.enums.BusinessTypeEnums;
import com.midea.pam.common.enums.CommonStatus;
import com.midea.pam.common.enums.EmailStatus;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.InvoiceApplyStatusEnum;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.NoticeBusinessType;
import com.midea.pam.common.enums.ResponseCodeEnums;
import com.midea.pam.common.esb.constant.EsbConstant;
import com.midea.pam.common.esb.vo.ERPMassQueryReturnVo;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.gateway.entity.Email;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.DistributedCASLock;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.eam.service.service.EamInvokeHelperService;
import com.midea.pam.ctc.eam.service.service.EamPurchaseInfoService;
import com.midea.pam.ctc.mapper.BusinessApplyRelMapper;
import com.midea.pam.ctc.mapper.ContractEampurchaseRelationMapper;
import com.midea.pam.ctc.mapper.ContractExtMapper;
import com.midea.pam.ctc.mapper.ContractMapper;
import com.midea.pam.ctc.mapper.EamPaymentApplyInfoExtMapper;
import com.midea.pam.ctc.mapper.EamPaymentApplyInfoMapper;
import com.midea.pam.ctc.mapper.EamPurchaseAdviserInfoMapper;
import com.midea.pam.ctc.mapper.EamPurchaseDeviceInfoMapper;
import com.midea.pam.ctc.mapper.EamPurchaseInfoExtMapper;
import com.midea.pam.ctc.mapper.EamPurchaseInfoMapper;
import com.midea.pam.ctc.mapper.EamPurchasePayInfoMapper;
import com.midea.pam.ctc.mapper.InvoiceApplyHeaderMapper;
import com.midea.pam.ctc.mapper.InvoiceReceivableMapper;
import com.midea.pam.ctc.mapper.PaymentInvoiceMapper;
import com.midea.pam.ctc.mapper.PaymentPlanMapper;
import com.midea.pam.ctc.mapper.RdmSettlementSheetMapper;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.EsbService;
import com.midea.pam.ctc.service.NoticeService;
import com.midea.pam.ctc.service.ResendExecuteService;
import com.midea.pam.ctc.service.SdpCarrierServicel;
import com.midea.pam.ctc.service.SdpService;
import com.midea.pam.ctc.service.config.MilepostNoticeProperties;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.midea.pam.common.util.StringUtils.buildGetUrl;

/**
 * <AUTHOR>
 * @date 2020-4-8
 * @description
 */
public class EamPurchaseInfoServiceImpl implements EamPurchaseInfoService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    EamPurchaseInfoService eamPurchaseInfoService;

    @Resource
    RdmSettlementSheetMapper rdmSettlementSheetMapper;

    @Resource
    EamPurchaseDeviceInfoMapper eamPurchaseDeviceInfoMapper;

    @Resource
    private EamPurchaseInfoMapper purchaseInfoMapper;

    @Resource
    private InvoiceApplyHeaderMapper invoiceApplyHeaderMapper;

    @Resource
    private EamPaymentApplyInfoMapper eamPaymentApplyInfoMapper;

    @Resource
    private EamPaymentApplyInfoExtMapper eamPaymentApplyInfoExtMapper;

    @Resource
    private InvoiceReceivableMapper invoiceReceivableMapper;

    @Resource
    private ContractExtMapper contractExtMapper;

    @Resource
    private ResendExecuteService resendExecuteService;

    @Resource
    private PaymentPlanMapper paymentPlanMapper;

    @Resource
    private EamPurchaseAdviserInfoMapper adviserInfoMapper;

    @Resource
    private PaymentInvoiceMapper paymentInvoiceMapper;

    @Resource
    private EamPaymentApplyInfoMapper ApplyInfoMapper;

    @Resource
    private EamPurchasePayInfoMapper payInfoMapper;

    @Resource
    private EamPurchaseDeviceInfoMapper deviceInfoMapper;

    @Resource
    private EamPurchaseInfoExtMapper eamPurchaseInfoExtMapper;

    @Resource
    private ContractEampurchaseRelationMapper contractEampurchaseRelationMapper;

    @Resource
    private ContractMapper contractMapper;

    @Resource
    private EamPurchaseAdviserInfoMapper eamPurchaseAdviserInfoMapper;

    @Resource
    private EsbService esbService;

    @Resource
    private EamInvokeHelperService eamInvokeHelperService;

    @Resource
    private BasedataExtService basedataExtService;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private MilepostNoticeProperties milepostNoticeProperties;

    @Resource
    private NoticeService noticeService;

    @Resource
    private BusinessApplyRelMapper businessApplyRelMapper;

    @Resource
    private EamPurchasePayInfoMapper eamPurchasePayInfoMapper;

    @Resource
    private SdpCarrierServicel sdpCarrierServicel;

    @Resource
    private SdpService sdpService;

    @Value("${route.contractUrl}")
    private String contractUrl;

    @Override
    public int deleteByPrimaryKey(Long id) {
        return purchaseInfoMapper.deleteByPrimaryKey(id);
    }

    @Override
    public PageInfo<EamPaymentApplyInfoDto> pageEamPaymentApplyInfo(EamPaymentApplyInfoDto eamPaymentApplyInfoDto, Integer pageSize, Integer pageNum) {
        PageHelper.startPage(pageNum, pageSize);
        List<EamPaymentApplyInfoDto> list = eamPurchaseInfoExtMapper.selectEamPaymentApplyInfo(eamPaymentApplyInfoDto);
        if (ListUtils.isNotEmpty(list)) {
            for (EamPaymentApplyInfoDto paymentApplyInfoDto : list) {
                UserInfo userById = CacheDataUtils.findUserById(Long.valueOf(paymentApplyInfoDto.getPamManagerId()));
                paymentApplyInfoDto.setPamProjectManager(userById.getName());
            }
        }
        PageInfo<EamPaymentApplyInfoDto> page = BeanConverter.convertPage(list, EamPaymentApplyInfoDto.class);
        return page;
    }

    @Override
    public int insert(EamPurchaseInfo record) {
        return purchaseInfoMapper.insert(record);
    }

    @Override
    public int insertSelective(EamPurchaseInfo record) {
        return purchaseInfoMapper.insertSelective(record);
    }

    @Override
    public List<EamPurchaseInfo> selectByExample(EamPurchaseInfoExample example) {
        return purchaseInfoMapper.selectByExample(example);
    }

    @Override
    public EamPurchaseInfo selectByPrimaryKey(Long id) {
        return purchaseInfoMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(EamPurchaseInfo record) {
        return purchaseInfoMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(EamPurchaseInfo record) {
        return purchaseInfoMapper.updateByPrimaryKey(record);
    }

    @Override
    public String geamPamSyncInvoice(InvoiceApplyHeaderDto applyHeader) {
        logger.info("*************** 发票同步EAM开始 ***************");
        //查找符合条件的开票申请
        InvoiceApplyHeaderDto headerDto = BeanConverter.copy(invoiceApplyHeaderMapper.selectByPrimaryKey(applyHeader.getId()), InvoiceApplyHeaderDto.class);
        if (headerDto != null) {
            InvoiceReceivableExample receivableExample = new InvoiceReceivableExample();
            receivableExample.createCriteria().andDeletedFlagEqualTo(0)
                    .andApplyHeaderIdEqualTo(headerDto.getId());
            List<InvoiceReceivable> invoiceReceivableList = invoiceReceivableMapper.selectByExampleWithBLOBs(receivableExample);
            //如果有一张发票不是开票成功就不同步
            for (InvoiceReceivable invoiceReceivable : invoiceReceivableList) {
                if (!"开票成功".equals(invoiceReceivable.getStatus())) {
                    throw new BizException(ErrorCode.ERROR, "该笔开票申请存在未开票成功的应收发票");
                }
            }
            if (headerDto.getSettlementSheetId() != null) {
                RdmSettlementSheet settlementSheet = rdmSettlementSheetMapper.selectByPrimaryKey(headerDto.getSettlementSheetId());
                headerDto.setPoStatementNumber(settlementSheet.getWf150id());//PO结算单编号
            }
            //发票明细
            headerDto.setInvoiceReceivableDtoList(BeanConverter.copy(invoiceReceivableList, InvoiceReceivableDto.class));
            return pamSyncInvoice(headerDto);
        }
        logger.info("*************** 发票同步EAM结束 ***************");
        return "没有可同步的开票信息";
    }

    @Override
    public String pamSyncInvoice(InvoiceApplyHeaderDto headerDto) {
        //PAM开票信息同步EAM
        String responsemessage = eamInvokeHelperService.pamSyncInvoice(headerDto);
        if (StringUtils.isNotEmpty(responsemessage)) {
            headerDto.setEamMsg(responsemessage);
            headerDto.setEamStatus(InvoiceApplyStatusEnum.HEADER_ERROR.getCode());
        } else {
            responsemessage = "已开票信息同步成功";
            headerDto.setEamMsg(responsemessage);
            headerDto.setEamStatus(InvoiceApplyStatusEnum.HEADER_SUCCESS.getCode());
        }
        invoiceApplyHeaderMapper.updateByPrimaryKeySelective(headerDto);
        return responsemessage;
    }

    @Override
    public String callGEAMPmsPayStatus(String startDate, String endDate, String pamInvoiceCode, String modelKey) {
        if (StringUtils.isEmpty(modelKey)) {
            getEamPaymentApply(startDate, endDate, null, "TZHT");
            getEamPaymentApply(startDate, endDate, null, "HN");
            return getEamPaymentApply(startDate, endDate, null, "ITCT");
        }
        return getEamPaymentApply(startDate, endDate, pamInvoiceCode, modelKey);
    }

    @Override
    public EsbResponse checkInvoiceServer(InvoiceApplyHeaderDto applyHeader) {
        EsbResponse esbResponse = eamInvokeHelperService.checkInvoice(applyHeader);
        return esbResponse;
    }

    @Override
    public String callSyncInvoiceStatur(InvoiceApplyHeaderDto applyHeader) {
        PmsResult pmsResult = eamInvokeHelperService.syncInvoiceStatus(applyHeader);
        return pmsResult.getMessage();
    }

    @Override
    public String getEamPurchaseInfo(String startDate, String endDate, String modelKey) {
//        EsbResponse esbResponse = esbService.callGEAMPmsPurServer(startDate, endDate, null, modelKey);
        List<PmsPurInfo> pmsPurInfoList = eamInvokeHelperService.getPmsPurServer(startDate, endDate, null, modelKey);
        if (CollectionUtils.isNotEmpty(pmsPurInfoList)) {
            for (PmsPurInfo pmsPurInfo : pmsPurInfoList) {
                EamPurchaseInfo eamPurchaseInfo = new EamPurchaseInfo();
                eamPurchaseInfo.setProjectId(pmsPurInfo.getProjectId()); //RDM项目id
                eamPurchaseInfo.setProjectCode(pmsPurInfo.getProjectCode()); //项目编号
                eamPurchaseInfo.setProjectName(pmsPurInfo.getProjectName()); //项目名称
                eamPurchaseInfo.setAuditing(pmsPurInfo.getAuditing()); //单据状态
                eamPurchaseInfo.setContractCode(pmsPurInfo.getContractCode()); //合同单号
                eamPurchaseInfo.setContractId(pmsPurInfo.getContractId()); //合同主键
                eamPurchaseInfo.setContractName(pmsPurInfo.getContractName()); //合同名称
                eamPurchaseInfo.setDatacontractNumber(pmsPurInfo.getDatacontractNumber()); //法务合同号
                eamPurchaseInfo.setConDept(pmsPurInfo.getConDept()); //我方签约主体
                eamPurchaseInfo.setModelkey(pmsPurInfo.getModelKey()); //模块标识
                eamPurchaseInfo.setContractType(pmsPurInfo.getContractType()); //合同类型
                eamPurchaseInfo.setIsPerson(Objects.equals("1", pmsPurInfo.getIsPerson()) ? true : false); //是否人力外包
                eamPurchaseInfo.setProviderCode(pmsPurInfo.getProviderCode()); //供应商编码
                eamPurchaseInfo.setProviderName(pmsPurInfo.getProviderName()); //供应商名称
                eamPurchaseInfo.setVendorSiteCode(pmsPurInfo.getVendorSiteCode()); //供应商地点
                eamPurchaseInfo.setBankName(pmsPurInfo.getBankName()); //供应商开户银行
                eamPurchaseInfo.setBankAccountNum(pmsPurInfo.getBankAccountNum()); //供应商银行账号
                eamPurchaseInfo.setOuName(pmsPurInfo.getOuName()); //OU组织名称
                eamPurchaseInfo.setOuId(pmsPurInfo.getOuId()); //OU组织ID
                eamPurchaseInfo.setPaidMoneyCny(StringUtils.isNotBlank(pmsPurInfo.getPaidMoneyCny()) ? new BigDecimal(pmsPurInfo.getPaidMoneyCny()) : null); //已付款金额(万元)
                eamPurchaseInfo.setNopayMoneyCny(StringUtils.isNotBlank(pmsPurInfo.getNopayMoneyCny()) ? new BigDecimal(pmsPurInfo.getNopayMoneyCny()) : null); //未付款金额(万元)
                eamPurchaseInfo.setCurrency(pmsPurInfo.getCurrency()); //币种
                eamPurchaseInfo.setTravelExpenses(StringUtils.isNotBlank(pmsPurInfo.getTravelExpenses()) ? new BigDecimal(pmsPurInfo.getTravelExpenses()) : null); //差旅费
                eamPurchaseInfo.setExRate(StringUtils.isNotBlank(pmsPurInfo.getExRate()) ? new BigDecimal(pmsPurInfo.getExRate()) : null); //汇率
                eamPurchaseInfo.setEditDate(pmsPurInfo.getEditDate() != null ? pmsPurInfo.getEditDate() : null); //申请时间
                eamPurchaseInfo.setEditUserCode(pmsPurInfo.getEditUserCode()); //申请人
                eamPurchaseInfo.setApplyCode(pmsPurInfo.getApplyCode()); //采购申请单号
                eamPurchaseInfo.setApplyName(pmsPurInfo.getApplyName()); //采购申请名称
                eamPurchaseInfo.setApplyArea(pmsPurInfo.getApplyArea()); //采购范围及基本内容
                eamPurchaseInfo.setAttribute1(pmsPurInfo.getAttribute1());
                eamPurchaseInfo.setAttribute2(pmsPurInfo.getAttribute2());
                eamPurchaseInfo.setAttribute3(pmsPurInfo.getAttribute3());
                eamPurchaseInfo.setAttribute4(pmsPurInfo.getAttribute4());
                eamPurchaseInfo.setAttribute5(pmsPurInfo.getAttribute5());
                eamPurchaseInfo.setDeletedFlag(Boolean.FALSE);
                eamPurchaseInfo.setEamVersions(pmsPurInfo.getEamVersions());
                if (StringUtils.isEmpty(eamPurchaseInfo.getContractCode())) {
                    continue;
                }
                //判断是否更新
                EamPurchaseInfoExample purchaseInfoExample = new EamPurchaseInfoExample();
                purchaseInfoExample.createCriteria().andContractCodeEqualTo(eamPurchaseInfo.getContractCode()).andDeletedFlagEqualTo(Boolean.FALSE);
                List<EamPurchaseInfo> purchaseInfoList = selectByExample(purchaseInfoExample);
                if (CollectionUtils.isNotEmpty(purchaseInfoList)) {
                    EamPurchaseInfo oldEamPurchaseInfo = purchaseInfoList.get(0);
                    eamPurchaseInfo.setId(oldEamPurchaseInfo.getId());
                    eamPurchaseInfo.setCreateAt(oldEamPurchaseInfo.getCreateAt());
                    eamPurchaseInfo.setCreateBy(oldEamPurchaseInfo.getCreateBy());
                    updateByPrimaryKey(eamPurchaseInfo);

                    //删除所有顾问明细
                    EamPurchaseAdviserInfoExample adviserInfoExample = new EamPurchaseAdviserInfoExample();
                    adviserInfoExample.createCriteria().andPurchaseIdEqualTo(eamPurchaseInfo.getId());
                    List<EamPurchaseAdviserInfo> adviserInfoList = adviserInfoMapper.selectByExample(adviserInfoExample);
                    if (CollectionUtils.isNotEmpty(adviserInfoList)) {
                        for (EamPurchaseAdviserInfo adviserInfo : adviserInfoList) {
                            adviserInfoMapper.deleteByPrimaryKey(adviserInfo.getId());
                        }
                    }
                    //删除所有付款明细
                    EamPurchasePayInfoExample payInfoExample = new EamPurchasePayInfoExample();
                    payInfoExample.createCriteria().andPurchaseIdEqualTo(eamPurchaseInfo.getId());
                    List<EamPurchasePayInfo> payInfoList = payInfoMapper.selectByExample(payInfoExample);
                    if (CollectionUtils.isNotEmpty(payInfoList)) {
                        for (EamPurchasePayInfo payInfo : payInfoList) {
                            payInfoMapper.deleteByPrimaryKey(payInfo.getId());
                        }
                    }
                    //删除所有设备清单
                    EamPurchaseDeviceInfoExample deviceInfoExample = new EamPurchaseDeviceInfoExample();
                    deviceInfoExample.createCriteria().andPurchaseIdEqualTo(eamPurchaseInfo.getId());
                    List<EamPurchaseDeviceInfo> deviceInfoList = deviceInfoMapper.selectByExample(deviceInfoExample);
                    if (CollectionUtils.isNotEmpty(deviceInfoList)) {
                        for (EamPurchaseDeviceInfo deviceInfo : deviceInfoList) {
                            deviceInfoMapper.deleteByPrimaryKey(deviceInfo.getId());
                        }
                    }
                } else {
                    insert(eamPurchaseInfo);
                    //判断是否是采购合同变更
                    if (StringUtils.isNotBlank(eamPurchaseInfo.getAttribute2())) {
                        //邮件通知
                        sendEmaliRemind(eamPurchaseInfo.getId());
                    }
                }

                //顾问明细
                List<PmsAdviserInfo> adviserInfoList = pmsPurInfo.getPmsAdviserInfo();
                if (CollectionUtils.isNotEmpty(adviserInfoList)) {
                    for (PmsAdviserInfo pmsAdviserInfo : adviserInfoList) {
                        EamPurchaseAdviserInfo adviserInfo = new EamPurchaseAdviserInfo();
                        adviserInfo.setPurchaseId(eamPurchaseInfo.getId());
                        adviserInfo.setAdviser(pmsAdviserInfo.getAdviser()); //顾问名称
                        adviserInfo.setUserCode(pmsAdviserInfo.getUserCode()); //顾问账号
                        adviserInfo.setProviderCode(pmsAdviserInfo.getProviderCode()); //供应商编码
                        adviserInfo.setProviderName(pmsAdviserInfo.getProviderName()); //供应商名称
                        adviserInfo.setArea(pmsAdviserInfo.getArea()); //领域
                        adviserInfo.setAdviserLevel(pmsAdviserInfo.getAdviserLevel()); //级别
                        adviserInfo.setPrice(StringUtils.isNotBlank(pmsAdviserInfo.getPrice()) ? new BigDecimal(pmsAdviserInfo.getPrice()) : null); //单价
                        adviserInfo.setStartDate(pmsAdviserInfo.getStartDate() != null ? pmsAdviserInfo.getStartDate() : null); //外包开始日期
                        adviserInfo.setEndDate(pmsAdviserInfo.getEndDate() != null ? pmsAdviserInfo.getEndDate() : null); //外包结束日期
                        adviserInfo.setApplyDays(StringUtils.isNotBlank(pmsAdviserInfo.getApplyDays()) ? new BigDecimal(pmsAdviserInfo.getApplyDays()) : null); //采购人天
                        adviserInfo.setMoney(StringUtils.isNotBlank(pmsAdviserInfo.getMoney()) ? new BigDecimal(pmsAdviserInfo.getMoney()) : null); //费用
                        adviserInfo.setTravelExpenses(StringUtils.isNotBlank(pmsAdviserInfo.getTravelExpenses()) ? new BigDecimal(pmsAdviserInfo.getTravelExpenses()) : null); //差旅费
                        adviserInfo.setAttribute1(pmsAdviserInfo.getAttribute1()); //扩展字段1
                        adviserInfo.setAttribute2(pmsAdviserInfo.getAttribute2()); //扩展字段2
                        adviserInfo.setAttribute3(pmsAdviserInfo.getAttribute3()); //扩展字段3
                        adviserInfo.setAttribute4(pmsAdviserInfo.getAttribute4()); //扩展字段4
                        adviserInfo.setAttribute5(pmsAdviserInfo.getAttribute5()); //扩展字段5
                        adviserInfo.setDeletedFlag(Boolean.FALSE);
                        adviserInfoMapper.insert(adviserInfo);
                    }
                }

                //付款明细
                List<PmsPayInfo> payInfoList = pmsPurInfo.getPmsPayInfo();
                if (CollectionUtils.isNotEmpty(payInfoList)) {
                    for (PmsPayInfo pmsPayInfo : payInfoList) {
                        EamPurchasePayInfo payInfo = new EamPurchasePayInfo();
                        payInfo.setPurchaseId(eamPurchaseInfo.getId());
                        payInfo.setPayStage(pmsPayInfo.getPayStage()); //付款期数
                        payInfo.setPayRate(StringUtils.isNotBlank(pmsPayInfo.getPayRate()) ? new BigDecimal(pmsPayInfo.getPayRate()) : null); //付款比例
                        payInfo.setApplyPayCny(StringUtils.isNotBlank(pmsPayInfo.getApplyPayCny()) ? new BigDecimal(pmsPayInfo.getApplyPayCny()) : null); //付款金额（CNY万元）
                        payInfo.setApplyPay(StringUtils.isNotBlank(pmsPayInfo.getApplyPay()) ? new BigDecimal(pmsPayInfo.getApplyPay()) : null); //付款金额（原币万元）
                        payInfo.setFineMoney(StringUtils.isNotBlank(pmsPayInfo.getFineMoney()) ? new BigDecimal(pmsPayInfo.getFineMoney()) : null); //扣款金额（原币万元）
                        payInfo.setPayCond(pmsPayInfo.getPayCond()); //付款条件
                        payInfo.setStartSettlementDate(pmsPayInfo.getStartSettlementDate() != null ? pmsPayInfo.getStartSettlementDate() : null); //结算开始日期
                        payInfo.setEndSettlementDate(pmsPayInfo.getEndSettlementDate() != null ? pmsPayInfo.getEndSettlementDate() : null); //结算结束日期
                        payInfo.setPayDesc(pmsPayInfo.getPayDesc()); //其他付款条件说明
                        payInfo.setIsPay(Objects.equals("1", pmsPayInfo.getIsPay()) ? true : false); //是否已付款
                        payInfo.setApplyCode(pmsPayInfo.getApplyCode()); //付款申请单号
                        payInfo.setAttribute1(pmsPayInfo.getAttribute1()); //扩展字段1
                        payInfo.setAttribute2(pmsPayInfo.getAttribute2()); //扩展字段2
                        payInfo.setAttribute3(pmsPayInfo.getAttribute3()); //扩展字段3
                        payInfo.setAttribute4(pmsPayInfo.getAttribute4()); //扩展字段4
                        payInfo.setAttribute5(pmsPayInfo.getAttribute5()); //扩展字段5
                        payInfo.setDeletedFlag(Boolean.FALSE);
                        payInfoMapper.insert(payInfo);
                    }
                }

                //设备清单
                List<PmsDeviceInfo> deviceInfoList = pmsPurInfo.getPmsDeviceInfo();
                if (CollectionUtils.isNotEmpty(deviceInfoList)) {
                    for (PmsDeviceInfo pmsDeviceInfo : deviceInfoList) {
                        EamPurchaseDeviceInfo deviceInfo = new EamPurchaseDeviceInfo();
                        deviceInfo.setPurchaseId(eamPurchaseInfo.getId());
                        deviceInfo.setDeviceName(pmsDeviceInfo.getDeviceName()); //设备统称
                        deviceInfo.setDeviceType(pmsDeviceInfo.getDeviceType()); //型号
                        deviceInfo.setPurUnit(pmsDeviceInfo.getPurUnit()); //单位
                        deviceInfo.setPurNum(StringUtils.isNotBlank(pmsDeviceInfo.getPurNum()) ? new BigDecimal(pmsDeviceInfo.getPurNum()) : null); //数量
                        deviceInfo.setAttribute1(pmsDeviceInfo.getAttribute1()); //扩展字段1
                        deviceInfo.setAttribute2(pmsDeviceInfo.getAttribute2()); //扩展字段2
                        deviceInfo.setAttribute3(pmsDeviceInfo.getAttribute3()); //扩展字段3
                        deviceInfo.setAttribute4(pmsDeviceInfo.getAttribute4()); //扩展字段4
                        deviceInfo.setAttribute5(pmsDeviceInfo.getAttribute5()); //扩展字段5
                        deviceInfo.setDeletedFlag(Boolean.FALSE);
                        deviceInfoMapper.insert(deviceInfo);
                    }
                }

            }
        }

        return null;
    }

    @Override
    public String getEamPaymentApply(String startDate, String endDate, String pamInvoiceCode, String modelKey) {
//        EsbResponse esbResponse = esbService.callGEAMPmsPayStatus(startDate, endDate, pamInvoiceCode, modelKey);
        List<PmsPayStatus> pmsPayStatusList = eamInvokeHelperService.getPmsPayStatus(startDate, endDate, pamInvoiceCode, modelKey);
        if (CollectionUtils.isNotEmpty(pmsPayStatusList)) {
            //根据拉取的合同信息数据获取EAM接口版本
            List<String> contractCodeList = pmsPayStatusList.stream().map(PmsPayStatus::getAttribute1).collect(Collectors.toList());
            EamPurchaseInfoExample eamPurchaseInfoExample = new EamPurchaseInfoExample();
            eamPurchaseInfoExample.createCriteria().andContractCodeIn(contractCodeList).andDeletedFlagEqualTo(Boolean.FALSE);
            List<EamPurchaseInfo> eamPurchaseInfos = eamPurchaseInfoService.selectByExample(eamPurchaseInfoExample);
            Map<String, Integer> contractCodeWitheamVersionsMap = eamPurchaseInfos.stream().collect(Collectors.toMap(EamPurchaseInfo::getContractCode, EamPurchaseInfo::getEamVersions));
            for (PmsPayStatus pmsPayStatus : pmsPayStatusList) {
                //当且仅当 合同信息存储的版本与付款申请状态拉取接口的版本一致才处理
                //pamInvoiceCode为空的数据不处理
                if (Objects.equals(MapUtils.getInteger(contractCodeWitheamVersionsMap, pmsPayStatus.getAttribute1()), pmsPayStatus.getEamVersions())
                        && StringUtils.isNotBlank(pmsPayStatus.getPamInvoiceCode())) {
                    EamPaymentApplyInfo paymentApplyInfo = new EamPaymentApplyInfo();
                    paymentApplyInfo.setApplyCode(pmsPayStatus.getApplyCode());
                    paymentApplyInfo.setPamInvoiceCode(pmsPayStatus.getPamInvoiceCode());
                    paymentApplyInfo.setContractCode(pmsPayStatus.getAttribute1());
                    paymentApplyInfo.setSeqId(pmsPayStatus.getSeqId());
                    paymentApplyInfo.setEditUserCode(pmsPayStatus.getEditUserCode());
                    paymentApplyInfo.setEditUser(pmsPayStatus.getEditUser());
                    if (StringUtils.isNotEmpty(pmsPayStatus.getAuditing())) {
                        paymentApplyInfo.setAuditing(Integer.parseInt(pmsPayStatus.getAuditing()));
                    }
                    if (StringUtils.isNotEmpty(pmsPayStatus.getEmsSyncStatus())) {
                        paymentApplyInfo.setEmsSyncStatus(Integer.parseInt(pmsPayStatus.getEmsSyncStatus()));
                    }
                    if (pmsPayStatus.getEmsSyncDate() != null) {
                        paymentApplyInfo.setEmsSyncDate(pmsPayStatus.getEmsSyncDate());
                    }
                    if (StringUtils.isNotEmpty(pmsPayStatus.getEmsBizStatus())) {
                        paymentApplyInfo.setEmsBizStatus(Integer.parseInt(pmsPayStatus.getEmsBizStatus()));
                    }
                    paymentApplyInfo.setApInvoiceCode(pmsPayStatus.getApInvoiceCode());
                    paymentApplyInfo.setEmsPayCode(pmsPayStatus.getEmsPayCode());
                    paymentApplyInfo.setErrorReason(pmsPayStatus.getErrorReason());
                    paymentApplyInfo.setErpCode(pmsPayStatus.getErpCode());
                    paymentApplyInfo.setDeletedFlag(Boolean.FALSE);
                    // 判断是否更新
                    EamPaymentApplyInfoExample eamPaymentApplyInfoExample = new EamPaymentApplyInfoExample();
                    // 无需排查deleteflag，因为根据开票申请单号查询，如果生成了新的付款单，会作废旧的
                    eamPaymentApplyInfoExample.createCriteria().andApplyCodeEqualTo(paymentApplyInfo.getApplyCode())
                            .andPamInvoiceCodeEqualTo(paymentApplyInfo.getPamInvoiceCode());
                    List<EamPaymentApplyInfo> eamPaymentApplyInfoList = eamPaymentApplyInfoMapper.selectByExample(eamPaymentApplyInfoExample);
                    if (ListUtils.isNotEmpty(eamPaymentApplyInfoList)) {
                        EamPaymentApplyInfo eamPaymentApplyInfo = eamPaymentApplyInfoList.get(0);
                        paymentApplyInfo.setId(eamPaymentApplyInfo.getId());
                        paymentApplyInfo.setDeletedFlag(eamPaymentApplyInfo.getDeletedFlag());
                        paymentApplyInfo.setCreateAt(eamPaymentApplyInfo.getCreateAt());
                        paymentApplyInfo.setCreateBy(eamPaymentApplyInfo.getCreateBy());
                        eamPaymentApplyInfoMapper.updateByPrimaryKey(paymentApplyInfo);
                    } else {
                        eamPaymentApplyInfoMapper.insert(paymentApplyInfo);
                    }
                }
            }
            // 根据开票申请单号查询，如果生成了新的付款单，作废旧的
            eamPaymentApplyInfoExtMapper.batchDeleteEamPaymentApply();
        }
        return null;
    }

    @Override
    public String handleEamPurchaseInfo() {
        // 并发时候的key值
        String lockKey = Constants.DistributedLockKey.HANDLE_EAM_SYNC;
        String result = "同步成功";
        ResendExecute resendExecute = null;
        try {
            if (DistributedCASLock.lock(lockKey, BusinessTypeEnums.EAM_PURCHASE_INFO.getCode(), 1000 * 60L * 5, 1000 * 60L * 5)) {
                //检测是否有接口正在同步
                if (resendExecuteService.countResentExecute(BusinessTypeEnums.EAM_PURCHASE_INFO.getCode()) > 0) {
                    result = "采购申请正在同步中，请5分钟后再操作。";
                } else {
                    //插入接口发送记录
                    resendExecute = resendExecuteService.saveResentExecute(BusinessTypeEnums.EAM_PURCHASE_INFO.getCode());
                    Date yesterday = DateUtils.subtractDay(DateUtils.getCurrentDate(), 1);
                    String startDate = DateUtils.format(yesterday, "yyyy-MM-dd");
                    String endDate = DateUtils.format(new Date(), "yyyy-MM-dd");
                    getEamPurchaseInfo(startDate, endDate, "ITCT");
                }
            } else {
                result = "采购申请正在同步中，请5分钟后再操作。";
            }
        } catch (Exception e) {
            if (resendExecute != null) {
                resendExecute.setStatus(CommonStatus.ERROR.getCode());
                resendExecute.setResponCode(ResponseCodeEnums.FAULT.getCode());
                resendExecute.setResponMsg(e.toString());
                resendExecuteService.updateByPrimaryKeySelective(resendExecute);
            }
            result = "采购申请同步异常，请稍后重试。";
            logger.error("手工同步EAM采购申请异常", e);
        } finally {
            DistributedCASLock.unLock(lockKey, BusinessTypeEnums.EAM_PURCHASE_INFO.getCode());
        }
        return result;
    }

    @Override
    public PageInfo<EamPurchaseInfoDto> list(EamPurchaseInfoDto eamPurchaseInfoDto) {
        Map<String, Object> param = new HashMap<>();
        if (eamPurchaseInfoDto.getProjectName() != null) {
            param.put("projectName", eamPurchaseInfoDto.getProjectName());
        }
        if (eamPurchaseInfoDto.getManagerName() != null) {
            param.put("managerName", eamPurchaseInfoDto.getManagerName());
        }
        if (StringUtils.isNotEmpty(eamPurchaseInfoDto.getContractCode())) {
            param.put("contractCode", eamPurchaseInfoDto.getContractCode());
        }

        if (StringUtils.isNotEmpty(eamPurchaseInfoDto.getContractName())) {
            param.put("contractName", eamPurchaseInfoDto.getContractName());
        }
        if (eamPurchaseInfoDto.getCrmCode() != null) {
            param.put("crmCode", eamPurchaseInfoDto.getCrmCode().substring(2));
        }
        if (StringUtils.isNotEmpty(eamPurchaseInfoDto.getDatacontractNumber())) {
            param.put("datacontractNumber", eamPurchaseInfoDto.getDatacontractNumber());
        }
        if (StringUtils.isNotEmpty(eamPurchaseInfoDto.getOuName())) {
            param.put("ouName", eamPurchaseInfoDto.getOuName());
        }
        if (eamPurchaseInfoDto.getOperatingUnitName() != null) {
            param.put("operatingUnitName", eamPurchaseInfoDto.getOperatingUnitName().substring(3, 9));
        }
        if (StringUtils.isNotEmpty(eamPurchaseInfoDto.getModelkey())) {
            param.put("modelKey", eamPurchaseInfoDto.getModelkey());
        }
        if (StringUtils.isNotEmpty(eamPurchaseInfoDto.getOrMain()) && eamPurchaseInfoDto.getOrMain().equals("false")) {
            param.put("orMain", eamPurchaseInfoDto.getOrMain());
        }
        if (StringUtils.isNotBlank(eamPurchaseInfoDto.getEamPurchasePayIds())) {
            final List<Long> eampurchaseIdList = Arrays.stream(eamPurchaseInfoDto.getEamPurchasePayIds().split(","))
                    .map(s -> Long.parseLong(s.trim()))
                    .collect(Collectors.toList());
            param.put("eamPurchasePayIds", eampurchaseIdList);
        }
        PageHelper.startPage(eamPurchaseInfoDto.getPageNum(), eamPurchaseInfoDto.getPageSize());
        //通过网关跨域查找用户信息
        List<EamPurchaseInfoDto> list = eamPurchaseInfoExtMapper.findList(param);
        if (CollectionUtils.isNotEmpty(list)) {
            for (EamPurchaseInfoDto purchaseInfoDto : list) {
                Long purchaseInfoDtoId = purchaseInfoDto.getId();
                EamPurchaseAdviserInfoExample adviserInfoExample = new EamPurchaseAdviserInfoExample();
                adviserInfoExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andPurchaseIdEqualTo(purchaseInfoDtoId);
                List<EamPurchaseAdviserInfo> eamPurchaseAdviserInfoList = eamPurchaseAdviserInfoMapper.selectByExample(adviserInfoExample);
                if (CollectionUtils.isNotEmpty(eamPurchaseAdviserInfoList)) {
                    final Optional<EamPurchaseAdviserInfo> min = eamPurchaseAdviserInfoList.stream().min(Comparator.comparing(EamPurchaseAdviserInfo::getStartDate));
                    if (min.isPresent()) {
                        EamPurchaseAdviserInfo eamPurchaseAdviserInfoStart = min.get();
                        purchaseInfoDto.setAdviserStartDate(eamPurchaseAdviserInfoStart.getStartDate());
                    }
                    final Optional<EamPurchaseAdviserInfo> max = eamPurchaseAdviserInfoList.stream().max(Comparator.comparing(EamPurchaseAdviserInfo::getEndDate));
                    if (max.isPresent()) {
                        EamPurchaseAdviserInfo eamPurchaseAdviserInfoEnd = max.get();
                        purchaseInfoDto.setAdviserEndDate(eamPurchaseAdviserInfoEnd.getEndDate());
                    }
                }
            }
        }
        PageInfo<EamPurchaseInfoDto> page = BeanConverter.convertPage(list, EamPurchaseInfoDto.class);
        return page;
    }

    @Override
    public void eamPurchaseDataCheck(ContractDTO contractDTO, ContractHisDTO contractHisDTO) {
        String ouName = "";
        Long ouId = null;
        BigDecimal amount = null;
        if (ObjectUtils.isEmpty(contractDTO)) {
            ouName = contractHisDTO.getOuName();
            ouId = contractHisDTO.getOuId();
            amount = contractHisDTO.getAmount(); //主合同含税金额
            final List<ContractHisDTO> childrenContractHisDtoList = contractHisDTO.getChildrenContractHiss();
            Map<Long, String> eampurchaseIdAmountMap = new HashMap<>();
            BigDecimal sonAmount = BigDecimal.ZERO;
            for (ContractHisDTO hisDTO : childrenContractHisDtoList) {
                sonAmount = sonAmount.add(hisDTO.getAmount());
                final String eampurchaseIds = hisDTO.getEampurchaseId();
                if (StringUtils.isNotBlank(eampurchaseIds)) {
                    final List<Long> eampurchaseIdList = Arrays.stream(eampurchaseIds.split(","))
                            .map(s -> Long.parseLong(s.trim()))
                            .collect(Collectors.toList());
                    EamPurchaseInfoExample eamPurchaseInfoExample = new EamPurchaseInfoExample();
                    eamPurchaseInfoExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andIdIn(eampurchaseIdList).andAuditingEqualTo("3");
                    //查该子合同对应的采购申请
                    final List<EamPurchaseInfo> eamPurchaseInfoList = purchaseInfoMapper.selectByExample(eamPurchaseInfoExample);
                    if (ListUtils.isNotEmpty(eamPurchaseInfoList)) {
                        for (EamPurchaseInfo eamPurchaseInfo : eamPurchaseInfoList) {
                            eampurchaseIdAmountMap.put(eamPurchaseInfo.getId(), eamPurchaseInfo.getAttribute1());
                        }
                    }
                    checkOu(eamPurchaseInfoList, ouId, ouName);
                }
            }

            if (!eampurchaseIdAmountMap.isEmpty()) {
                BigDecimal eamPurchaseTotalAmount = BigDecimal.ZERO;
                for (Map.Entry<Long, String> mapEntry : eampurchaseIdAmountMap.entrySet()) {
                    String eamPurchaseAmountStr = mapEntry.getValue();
                    BigDecimal eamPurchaseAmount = BigDecimal.valueOf(Double.valueOf(eamPurchaseAmountStr));
                    eamPurchaseTotalAmount = eamPurchaseTotalAmount.add(eamPurchaseAmount);
                }
                eamPurchaseTotalAmount = eamPurchaseTotalAmount.multiply(BigDecimal.valueOf(10000));
                if (BigDecimalUtils.equals(amount, sonAmount) && BigDecimalUtils.equals(amount, eamPurchaseTotalAmount)) {
                    logger.info("金额与采购申请校验成功");
                } else {
//                    throw new MipException("主合同金额（含税）必须等于关联采购申请总额且采购申请金额必须完全分配到子合同");
                    //由于子合同可能包含没有关联采购申请的子合同，那么这就会导致 子合同的总金额不会和关联的采购申请合同的金额一致，所以不做校验了 added by dengfei added at ************
                    logger.info("不校验了！");
                }
            }
        } else {
            Long parentId = contractDTO.getId();
            Contract parentContract = contractMapper.selectByPrimaryKey(parentId);
            if (parentContract.getOuId() != null) {
                final OperatingUnit operatingUnit = CacheDataUtils.findOuById(parentContract.getOuId());
                if (operatingUnit != null) {
                    ouName = operatingUnit.getOperatingUnitName();
                }
            }

            ouId = parentContract.getOuId();
            amount = parentContract.getAmount();//主合同含税金额
            final List<ContractDTO> childrenContractDtoList = contractDTO.getChildrenContracts();
            Map<Long, String> eampurchaseIdAmountMap = new HashMap<>();
            BigDecimal sonAmount = BigDecimal.ZERO;
            for (ContractDTO dto : childrenContractDtoList) {
                sonAmount = sonAmount.add(dto.getAmount());
                final String eampurchaseIds = dto.getEampurchaseId();
                if (StringUtils.isNotBlank(eampurchaseIds)) {
                    final List<Long> eampurchaseIdList = Arrays.stream(eampurchaseIds.split(","))
                            .map(s -> Long.parseLong(s.trim()))
                            .collect(Collectors.toList());
                    EamPurchaseInfoExample eamPurchaseInfoExample = new EamPurchaseInfoExample();
                    eamPurchaseInfoExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andIdIn(eampurchaseIdList).andAuditingEqualTo("3");
                    //查该子合同对应的采购申请
                    final List<EamPurchaseInfo> eamPurchaseInfoList = purchaseInfoMapper.selectByExample(eamPurchaseInfoExample);

                    if (ListUtils.isNotEmpty(eamPurchaseInfoList)) {
                        for (EamPurchaseInfo eamPurchaseInfo : eamPurchaseInfoList) {
                            eampurchaseIdAmountMap.put(eamPurchaseInfo.getId(), eamPurchaseInfo.getAttribute1());
                        }
                    }
                    checkOu(eamPurchaseInfoList, ouId, ouName);
                }
            }

            if (!eampurchaseIdAmountMap.isEmpty()) {
                BigDecimal eamPurchaseTotalAmount = BigDecimal.ZERO;
                for (Map.Entry<Long, String> mapEntry : eampurchaseIdAmountMap.entrySet()) {
                    String eamPurchaseAmountStr = mapEntry.getValue();
                    BigDecimal eamPurchaseAmount = BigDecimal.valueOf(Double.valueOf(eamPurchaseAmountStr));
                    eamPurchaseTotalAmount = eamPurchaseTotalAmount.add(eamPurchaseAmount);
                }
                eamPurchaseTotalAmount = eamPurchaseTotalAmount.multiply(BigDecimal.valueOf(10000));
                if (BigDecimalUtils.equals(amount, sonAmount) && BigDecimalUtils.equals(amount, eamPurchaseTotalAmount)) {
                    logger.info("金额与采购申请校验成功");
                } else {
//                    throw new MipException("主合同金额（含税）必须等于关联采购申请总额且采购申请金额必须完全分配到子合同");
                    //由于子合同可能包含没有关联采购申请的子合同，那么这就会导致 子合同的总金额不会和关联的采购申请合同的金额一致，所以不做校验了 added by dengfei added at ************
                    logger.info("不做校验了");
                }
            }

        }
    }

    @Override
    public void saveContractEamPurchaseRel(List<Long> childrenContractIdDelList, Map<Long, String> childrenContractIdAddMap, Boolean isHis) {
        if (CollectionUtils.isNotEmpty(childrenContractIdDelList)) {
            //失效掉旧的数据
            if (isHis) {
                eamPurchaseInfoExtMapper.deletecontractEamPurchaseHisRel(childrenContractIdDelList);
            } else {
                eamPurchaseInfoExtMapper.deletecontractEamPurchaseRel(childrenContractIdDelList);
            }
        }


        if (childrenContractIdAddMap != null) {
            Set<Map.Entry<Long, String>> entrySet = childrenContractIdAddMap.entrySet();
            for (Map.Entry<Long, String> entry : entrySet) {
                if (StringUtils.isNotBlank(entry.getValue())) {
                    Long contractId = entry.getKey();
                    String eamPurchaseIds = entry.getValue();
                    final List<Long> eampurchaseIdList = Arrays.stream(eamPurchaseIds.split(","))
                            .map(s -> Long.parseLong(s.trim()))
                            .collect(Collectors.toList());
                    eampurchaseIdList.forEach(eampurchaseId -> {
                        ContractEampurchaseRelation contractEampurchaseRelation = new ContractEampurchaseRelation();
                        final EamPurchaseInfo eamPurchaseInfo = purchaseInfoMapper.selectByPrimaryKey(eampurchaseId);
                        if (isHis != null && isHis.equals(Boolean.FALSE)) {
                            contractEampurchaseRelation.setContractId(contractId);
                        } else {
                            contractEampurchaseRelation.setContracthisId(contractId);
                        }
                        contractEampurchaseRelation.setEampurchaseId(eamPurchaseInfo.getId());
                        contractEampurchaseRelation.setApplyCode(eamPurchaseInfo.getApplyCode());
                        contractEampurchaseRelation.setProviderCode(eamPurchaseInfo.getProviderCode());
                        contractEampurchaseRelation.setProjectCode(eamPurchaseInfo.getProjectCode());
                        contractEampurchaseRelation.setDeletedFlag(Boolean.FALSE);
                        contractEampurchaseRelationMapper.insert(contractEampurchaseRelation);
                        logger.info("子合同:{},采购申请{},关系已经维护", contractId, eamPurchaseInfo.getId());
                    });
                }
            }
        }

    }

    /**
     * @param eamPurchaseInfoList 子合同关联的采购申请id
     * @param ouId                主合同的ouid
     * @param ouName              主合同的ouname
     */
    public void checkOu(List<EamPurchaseInfo> eamPurchaseInfoList, Long ouId, String ouName) {
        if (ListUtils.isNotEmpty(eamPurchaseInfoList)) {
            final List<String> providerCodeStrList = eamPurchaseInfoList.stream()
                    .map(eamPurchaseInfo -> eamPurchaseInfo.getProviderCode()).distinct().collect(Collectors.toList());
            if (ListUtils.isNotEmpty(providerCodeStrList)) {
                // 取最后六位数，也就是erp组织里面的公司代码
                final List<String> companyCodeStrList = providerCodeStrList.stream().map(s -> s.substring(s.length() - 6, s.length())).collect(Collectors.toList());
                final Map<String, Object> param = new HashMap<>();
                param.put("companyCodeList", companyCodeStrList);
                param.put("pageNum", 1);
                param.put("pageSize", 100);
                String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "organizationRel/list", param);
                String res = restTemplate.getForObject(url, String.class);
                PageInfo<OrganizationRelDto> data = JSON.parseObject(res, new TypeReference<PageInfo<OrganizationRelDto>>() {
                });
                final List<OrganizationRelDto> organizationRelDtoList = data.getList();
                if (ListUtils.isNotEmpty(organizationRelDtoList)) {
                    final List<Long> operatingUnitIdList = organizationRelDtoList.stream()
                            .map(o -> o.getOperatingUnitId()).distinct().collect(Collectors.toList());
                    if (ListUtils.isNotEmpty(operatingUnitIdList)) {
                        if (operatingUnitIdList.size() > 1) {
                            throw new MipException("主合同的OU是" + ouName + "，您选择的采购申请的业务实体与主合同的不一致，请修改");
                        } else {
                            final Long operatingUnitId = operatingUnitIdList.get(0);
                            if (!operatingUnitId.equals(ouId)) {
                                throw new MipException("主合同的OU是" + ouName + "，您选择的采购申请的业务实体与主合同的不一致，请修改");
                            }
                        }
                    }
                }
            }
        }

    }

    @Override
    public void sendEmaliRemind(Long eamApplyId) {
        List<Email> emails = new ArrayList<>();
        EamPurchaseInfo eamPurchaseInfo = purchaseInfoMapper.selectByPrimaryKey(eamApplyId);//变更后
        if (eamPurchaseInfo != null) {
            EamPurchaseInfoExample example = new EamPurchaseInfoExample();
            example.createCriteria().andContractCodeEqualTo(eamPurchaseInfo.getAttribute2());
            //查询变更前的采购申请
            List<EamPurchaseInfo> purchaseInfoList = purchaseInfoMapper.selectByExample(example);
            if (ListUtils.isNotEmpty(purchaseInfoList)) {
                EamPurchaseInfo purchaseInfo = purchaseInfoList.get(0);//变更前
                List<Contract> contractList = contractExtMapper.listContractByRdm(purchaseInfo.getId());
                if (ListUtils.isNotEmpty(contractList)) {
                    for (Contract contract : contractList) {
                        Email email = new Email();
                        email.setSubject("PAM系统合同变更提醒");
                        String content = buildHeader(contract.getId(), purchaseInfo.getApplyCode(),
                                purchaseInfo.getAttribute1(), eamPurchaseInfo.getAttribute1(), contract.getName(), contract.getCode());
                        email.setContent(content);
                        email.setLanguage("zh-CN");
                        email.setCreateAt(new Date());
                        email.setDeletedFlag(Boolean.FALSE);
                        email.setBusinessType(NoticeBusinessType.EAMPURCHASE_REMIND.getType());
                        email.setStatus(EmailStatus.TO_DO.getCode());
                        email.setFromAddress(milepostNoticeProperties.getEmail().getFrom());
                        Long contractManager = contract.getManager();
                        if (contractManager != null) {
                            UserInfo userInfo = CacheDataUtils.findUserById(contractManager);
                            if (null != userInfo && StringUtils.isNotBlank(userInfo.getUsername())) {
                                email.setReceiver(userInfo.getUsername());//项目经理
                                emails.add(email);
                            }
                        }
                        Long salesManager = contract.getSalesManager();
                        if (salesManager != null && !Objects.equals(salesManager, contractManager)) {
                            UserInfo userInfo = CacheDataUtils.findUserById(salesManager);
                            if (null != userInfo && StringUtils.isNotBlank(userInfo.getUsername())) {
                                Email emailSales = BeanConverter.copy(email, Email.class);
                                emailSales.setReceiver(userInfo.getUsername()); //销售经理
                                emails.add(emailSales);
                            }
                        }
                    }
                    // 邮件推送
                    noticeService.sendMail(emails);
                }
            }
        }
    }

    private String buildHeader(Long contractId, String applyCode, String oldPrice, String newPrice, String contractName, String contractCode) {
        String url = contractUrl + contractId;
        String urlStr = "<a href='" + url + "' target='_blank'>【PAM系统合同变更提醒】"
                + contractName + "_" + contractCode
                + " 关联的采购申请" + applyCode + "存在变更，金额由" + oldPrice + "变更为" + newPrice + ",请及时变更合同金额</a>";
        StringBuilder sb = new StringBuilder();
        sb.append("<html>");
        urlStr = "<div style='font-size: 12px;'>" + urlStr + "</div><br/>";
        sb.append(urlStr);
        sb.append("</html>");
        return sb.toString();
    }

    @Override
    public List<OrganizationRel> getPamOuAndCus(String rdmProjectCode, String type, String supplierCode) {
        EamPurchaseInfoExample example = new EamPurchaseInfoExample();
        example.createCriteria().andProjectCodeEqualTo(rdmProjectCode)
                .andProviderCodeEqualTo(supplierCode)
                .andDeletedFlagEqualTo(false);
        List<EamPurchaseInfo> purchaseInfoList = purchaseInfoMapper.selectByExample(example);
        if (ListUtils.isNotEmpty(purchaseInfoList)) {
            final Map<String, Object> param = new HashedMap();
            if ("customer".equals(type)) {
                param.put("operatingUnitName", purchaseInfoList.get(0).getOuName().substring(3, 9));
            }
            if ("ou".equals(type)) {
                Set<String> codeList = new HashSet<>();
                for (int i = 0; i < purchaseInfoList.size(); i++) {
                    codeList.add(purchaseInfoList.get(i).getProviderCode().substring(2));
                }
                String codeStr = StringUtils.join(codeList, ",");
                param.put("codeStr", codeStr);
            }
            String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "organizationRel/list", param);
            String res = restTemplate.getForObject(url, String.class);
            PageInfo<OrganizationRel> page = JSON.parseObject(res, new TypeReference<PageInfo<OrganizationRel>>() {
            });

            if (ListUtils.isNotEmpty(page.getList()) && "ou".equals(type)) {
                List<Long> ous = SystemContext.getOus();
                for (int i = 0; i < page.getList().size(); i++) {
                    if (!ous.contains(page.getList().get(i).getOperatingUnitId())) {
                        page.getList().remove(i);
                    }
                }

            }
            return page.getList();
        }
        return null;
    }

    /**
     * 获取关联交易供需关系数据
     *
     * @param time
     */
    @Override
    public void getBusinessApplyRelData(String time) {
        Map<String, String> paramMap = new HashMap();
//        paramMap.put(EsbConstant.ERP_IP_P03, "2010-01-01 00:00:00");
        paramMap.put(EsbConstant.ERP_SDP_P03, "2010-01-01 00:00:00");
        getBusinessApplyRelFromErp(paramMap, time);
    }

    /**
     * 获取关联交易号
     */
    @Override
    public String getSeqpkgPortType() {
//        EsbResponse seqResponse = esbService.callCUXICPGETSEQPKGPortType();
        EsbResponse seqResponse = sdpService.callCuxicpGetSeqPkgPortType();
        if (ResponseCodeEnums.SUCESS.getCode().equals(seqResponse.getResponsecode())) {
            String code = (String) seqResponse.getData();
            return code;
        } else {
            return seqResponse.getResponsemessage();
        }
    }

    @Transactional
    @Override
    public void getBusinessApplyRelFromErp(Map<String, String> paramMap, String time) {
        //调erp接口
//        List<ERPMassQueryReturnVo> returnItemList = esbService.callEsbMassQuery(EsbConstant.ERP_PIFACECODE_056, paramMap);
        List<SdpTradeResultResponseEleDto> returnItemList = sdpCarrierServicel.callSdpMassQuery(EsbConstant.ERP_PIFACECODE_056, paramMap);
        BusinessApplyRelExample businessApplyRelExample = new BusinessApplyRelExample();
        businessApplyRelExample.createCriteria().andHeaderIdIsNotNull();
        final List<BusinessApplyRel> businessApplyRelList = businessApplyRelMapper.selectByExample(businessApplyRelExample);
        if (CollectionUtils.isNotEmpty(businessApplyRelList)) {
            eamPurchaseInfoExtMapper.deleteBusinessApplyRel();
        }

        for (SdpTradeResultResponseEleDto item : returnItemList) {
            BusinessApplyRel businessApplyRel = new BusinessApplyRel();
            businessApplyRel.setHeaderId(item.getC1());
            businessApplyRel.setSupplierSystem(item.getC2()); // 供方系统
            businessApplyRel.setsOrgId(item.getC3()); // 供方OUID
            businessApplyRel.setsOrgCode(item.getC4());// 供方OU名称
            businessApplyRel.setsCustomersId(item.getC5()); // 客户ID
            businessApplyRel.setsCustomerNumber(item.getC6()); // 客户编码
            businessApplyRel.setsCustomersName(item.getC7()); // 客户名称
            businessApplyRel.setRequirementSystem(item.getC8());// 需方系统
            businessApplyRel.setrOrgId(item.getC9()); //需方OUID
            businessApplyRel.setrOrgCode(item.getC10()); // 需方OU名称
            businessApplyRel.setrVendorId(item.getC11()); // 供应商ID
            businessApplyRel.setrVendorNum(item.getC12()); // 供应商编号
            businessApplyRel.setrVendorName(item.getC13()); // 供应商名称
            businessApplyRel.setrVendorSiteId(item.getC14()); // 供应商地点ID
            businessApplyRel.setrVendorSite(item.getC15()); // 供应商地点
            businessApplyRel.setDeletedFlag(0);
            businessApplyRel.setCreateAt(new Date());
            businessApplyRelMapper.insert(businessApplyRel);
        }

    }

    @Override
    public List<EamPurchasePayInfo> payInfoList(Long eamPurchaseId) {
        EamPurchasePayInfoExample example = new EamPurchasePayInfoExample();
        example.createCriteria().andDeletedFlagEqualTo(false).andPurchaseIdEqualTo(eamPurchaseId);
        example.setOrderByClause("pay_stage asc");
        List<EamPurchasePayInfo> list = eamPurchasePayInfoMapper.selectByExample(example);
        list.stream().forEach(dto -> dto.setApplyPay(Optional.ofNullable(dto.getApplyPay()).orElse(BigDecimal.ZERO).multiply(BigDecimal.valueOf(10000))));
        return list;
    }

    @Override
    public List<BusinessApplyRel> getBusinessApplyRel(String ouId, String ouName, String crmCode) {
        /**
         * 判断客户是否走关联交易，需改成：
         * 从vendor_site_bank表取数，将客户CRM编码对应vendor_code，将业务实体id对应operating_unit_id
         * 然后再判断erp_icp_org_id，有值-走关联交易
         * erp_icp_org_id，无值-不走关联交易  added by dengfei  added at ********
         */
        List<BusinessApplyRel> businessApplyRels = new ArrayList<>();
        final List<VendorSiteBankDto> vendorSiteBankDtoList = basedataExtService.getVendorSiteBankDtoList(1l, crmCode, Long.valueOf(ouId));
        BusinessApplyRel businessApplyRel = new BusinessApplyRel();
        if (CollectionUtils.isNotEmpty(vendorSiteBankDtoList)) {
            businessApplyRel.setId(vendorSiteBankDtoList.get(0).getId());
            businessApplyRel.setrVendorName(vendorSiteBankDtoList.get(0).getVendorName());
            businessApplyRels.add(businessApplyRel);
        }
        return businessApplyRels;
    }

    @Override
    public Map<String, Object> exportEamPaymentApplyInfo(EamPaymentApplyInfoDto query) {
        Map<String, Object> resultMap = new HashMap<>();
        List<EamPaymentApplyInfoDto> list = eamPurchaseInfoExtMapper.selectEamPaymentApplyInfo(query);
        if (ListUtils.isNotEmpty(list)) {
            List<EamPaymentApplyExcelVo> eamPaymentApplyExcelVoList = BeanConverter.copy(list, EamPaymentApplyExcelVo.class);
            int n = 1;
            for (EamPaymentApplyExcelVo eamPaymentApplyExcelVo : eamPaymentApplyExcelVoList) {
                eamPaymentApplyExcelVo.setIndex(n++);
            }
            resultMap.put("eamPaymentApplyInfoList", eamPaymentApplyExcelVoList);
        } else {
            resultMap.put("eamPaymentApplyInfoList", Lists.newArrayList());
        }

        return resultMap;
    }

    @Override
    public EamPurchaseInfo findPurchasebyContract(Long contractId) {
        ContractEampurchaseRelationExample example = new ContractEampurchaseRelationExample();
        example.createCriteria().andDeletedFlagEqualTo(false).andContractIdEqualTo(contractId);
        List<ContractEampurchaseRelation> list = contractEampurchaseRelationMapper.selectByExample(example);
        if (ListUtils.isNotEmpty(list)) {
            EamPurchaseInfo eamPurchaseInfo = eamPurchaseInfoService.selectByPrimaryKey(list.get(0).getEampurchaseId());
            return eamPurchaseInfo;
        }
        return null;
    }

    @Override
    public List<EamPurchaseInfo> findEamPurchaseCancel(Long contractId) {
        ContractExample contractExample = new ContractExample();
        contractExample.createCriteria().andParentIdEqualTo(contractId).andDeletedFlagEqualTo(Boolean.FALSE).andEampurchaseIdIsNotNull();
        final List<Contract> contractList = contractMapper.selectByExample(contractExample);
        List<EamPurchaseInfo> eamPurchaseInfoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(contractList)) {
            for (Contract contract1 : contractList) {
                if (StringUtils.isNotEmpty(contract1.getEampurchaseId())) {
                    final List<Long> eamPurchaseIdList = Arrays.stream(contract1.getEampurchaseId().trim().split(",")).map(
                            Long::parseLong
                    ).collect(Collectors.toList());
                    EamPurchaseInfoExample eamPurchaseInfoExample = new EamPurchaseInfoExample();
                    eamPurchaseInfoExample.createCriteria().andIdIn(eamPurchaseIdList).andAuditingEqualTo("7").andDeletedFlagEqualTo(Boolean.FALSE);
                    final List<EamPurchaseInfo> purchaseInfos = purchaseInfoMapper.selectByExample(eamPurchaseInfoExample);
                    eamPurchaseInfoList.addAll(purchaseInfos);
                }
            }
        }
        return eamPurchaseInfoList;
    }

}
