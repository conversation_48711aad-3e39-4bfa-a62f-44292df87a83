package com.midea.pam.ctc.esb.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.midea.gpam.server.gpamresultreturnservice.v1.GpamHelperService;
import com.midea.gpam.server.gpamresultreturnservice.v1.ResultReturn;
import com.midea.pam.common.ctc.dto.PurchaseOrderDto;
import com.midea.pam.common.ctc.entity.EsbResultReturn;
import com.midea.pam.common.ctc.entity.EsbResultReturnRecord;
import com.midea.pam.common.ctc.entity.SdpLog;
import com.midea.pam.common.enums.SdpLogStatusEnum;
import com.midea.pam.common.enums.TopicCodeEnum;
import com.midea.pam.common.sdp.dto.PurchaseOrderFapSdpCallbackExtendJsonDto;
import com.midea.pam.common.sdp.dto.SdpCallBackBody;
import com.midea.pam.common.sdp.dto.SdpResponse;
import com.midea.pam.common.util.JsonUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.common.enums.PurchaseOrderSyncStatus;
import com.midea.pam.ctc.esb.service.EsbResultReturnRecordService;
import com.midea.pam.ctc.esb.service.EsbResultReturnService;
import com.midea.pam.ctc.mapper.PurchaseOrderMapper;
import com.midea.pam.ctc.mapper.ResendExecuteMapper;
import com.midea.pam.ctc.sdp.service.SdpLogService;
import com.midea.pam.ctc.service.PurchaseOrderService;
import com.midea.pam.ctc.service.ReceiptClaimService;
import com.midea.pam.ctc.service.SdpCallbackService;
import com.midea.pam.ctc.service.SdpCarrierServicel;
import com.midea.pam.ctc.transfer.service.CustomerTransferService;
import com.midea.pam.support.utils.BeanConverter;
import com.midea.sdp.dto.common.SdpResult;
import com.midea.sdp.dto.driver.SdpDataImportReplyToDto;
import com.midea.sdp.dto.driver.SdpDataTradeImportReplyToDto;
import deps.com.alibaba.fastjson.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;

/**
 * @program: pam
 * @description: SdpServiceImpl
 * @author: ygx
 * @create: 2024-5-13 10:00
 **/
public class SdpCallbackServiceImpl implements SdpCallbackService {

    private static final Logger logger = LoggerFactory.getLogger(SdpCallbackServiceImpl.class);

    private static String COMPLETED = "COMPLETED";

    @Value("${sdp.appKey:}")
    private String appKey;

    @Value("${sdp.appSecret:}")
    private String appSecret;

    @Value("${sdp.driverDataInUrl:}")
    private String driverDataInUrl;

    @Resource
    private SdpCarrierServicel sdpCarrierServicel;
    @Resource
    private ResendExecuteMapper resendExecuteMapper;
    @Resource
    private EsbResultReturnService esbResultReturnService;
    @Resource
    private EsbResultReturnRecordService esbResultReturnRecordService;
    @Resource
    private PurchaseOrderMapper purchaseOrderMapper;
    @Resource
    private PurchaseOrderService purchaseOrderService;
    @Resource
    private CustomerTransferService customerTransferService;
    @Resource
    private ReceiptClaimService receiptClaimService;
    @Resource
    private GpamHelperService gpamHelperService;
    @Resource
    private SdpLogService sdpLogService;


    @Override
    public SdpResult callback(SdpDataImportReplyToDto replyDto) {
        logger.info("SDP异步回调，流水号:{}，报文:{}", replyDto.getBusinessId(), JsonUtils.toString(replyDto));
        boolean isDataInCallBack = ListUtils.isNotEmpty(replyDto.getParams());
        if (!Objects.equals(replyDto.getResult(), Boolean.TRUE) || StringUtils.isEmpty(replyDto.getExtendJson()) && !isDataInCallBack) {
            logger.error("流水号:{}，回调异常！" + replyDto.getTopicCode());
            return SdpResult.error(replyDto.getMessage());
        }
        JSONArray extendJsonArray = null;
        if (!isDataInCallBack) {
            try {
                extendJsonArray = JSONObject.parseArray(replyDto.getExtendJson());
            } catch (Exception e) {
                return SdpResult.error("回调报文格式有误");
            }
        }

        switch (replyDto.getSystemCode()) {
            case "FAP":
                callbackFAP(replyDto.getTopicCode(), extendJsonArray);
                break;
            case "GERP":
                break;
            case "PAM":
                dataInCallBack(replyDto);
                break;
            default:
                logger.error("系统编码:{}，不支持的回调", replyDto.getSystemCode());
                break;
        }
        return SdpResult.success();
    }

    @Override
    public SdpResponse tradeCallback(SdpDataTradeImportReplyToDto tradeImportReplyToDto) {
        SdpResponse response = new SdpResponse();
        // 额外处理的接口卡名称集合
        List<String> processInterfaceNameExtra = Lists.newArrayList(TopicCodeEnum.FAP_ORDER_CREATE_INTERFACE.getInterfaceCode());
        try {
            if (Objects.equals(tradeImportReplyToDto.getResult(), Boolean.TRUE) && StringUtils.isNotEmpty(tradeImportReplyToDto.getExtendJson())) {
                logger.info("SDP异步回调，流水号:{}，报文:{}", tradeImportReplyToDto.getBusinessId(), JsonUtils.toString(tradeImportReplyToDto));
                EsbResultReturn esbResultReturn = new EsbResultReturn();
                String dataSource = null;
                //存入esb_result_return日志表
                esbResultReturn.setRequestId(tradeImportReplyToDto.getBusinessId());
                esbResultReturn.setInput(tradeImportReplyToDto.getExtendJson());
                esbResultReturn.setHandleFlag("N");
                esbResultReturnService.insert(esbResultReturn);

                List<SdpCallBackBody> list = JSON.parseArray(tradeImportReplyToDto.getExtendJson(), SdpCallBackBody.class);
                if (ListUtils.isNotEmpty(list)) {
                    dataSource = list.get(0).getDataSource();
                    esbResultReturn.setDataSource(dataSource);
                    if(processInterfaceNameExtra.contains(dataSource)){
                        if(TopicCodeEnum.FAP_ORDER_CREATE_INTERFACE.getInterfaceCode().equals(dataSource)){
                            processFapOrderCreateCallback(tradeImportReplyToDto, esbResultReturn);
                        }
                    }else{
                        for (SdpCallBackBody obj : list) {
                            List<String> sourceId1StrList = new ArrayList<>();
                            ResultReturn.SourceRecord.SourceRecordItem sourceRecordItem = BeanConverter.copy(obj, ResultReturn.SourceRecord.SourceRecordItem.class);
                            gpamHelperService.handle(sourceRecordItem, dataSource, esbResultReturn, sourceId1StrList);
                        }
                    }
                }
                response.setResponseType("N");
                response.setResponseCode("000000");
                response.setResponseMessage("SUCCESS");

                esbResultReturn.setHandleFlag("Y");
                esbResultReturnService.updateByPrimaryKeySelective(esbResultReturn);
            } else {
                logger.error("流水号:{}，SDP异步回调异常！" + tradeImportReplyToDto.getBusinessId());
                response.setResponseType("E");
                response.setResponseCode("000001");
                response.setResponseMessage("回调报文无法解析");
            }
        } catch (Exception ex) {
            logger.error("SdpCallbackService回调异常：", ex);
            response.setResponseType("E");
            response.setResponseCode("000001");
            response.setResponseMessage(ex.getStackTrace()[0].getClassName() + "/" + ex.getStackTrace()[0].getMethodName() + "/" + ex.getStackTrace()[0].getLineNumber()
                    + "/" + ex.getMessage());
        }
        return response;
    }

    public SdpResult callbackFAP(String topicCode, JSONArray extendJsonArray) {
        Iterator<Object> iterator = extendJsonArray.stream().iterator();
        while (iterator.hasNext()) {
            JSONObject jsonObject = (JSONObject) iterator.next();
            Long sourceId = jsonObject.getLong("sourceId");
            String callBackCode = jsonObject.getString("callBackCode");
            String callBackMsg = jsonObject.getString("callBackMsg");
            callBackMsg = callBackMsg != null && callBackMsg.length() > 1000 ? callBackMsg.substring(0, 1000) : callBackMsg;
            String serialNumber = jsonObject.getString("serialNumber");
            handleFAP(sourceId, topicCode, Objects.equals(callBackCode, "COMPLETED"), callBackMsg, serialNumber);
        }
        return new SdpResult();
    }

    public void handleFAP(Long applyNo, String topicCode, Boolean result, String callBackMsg, String serialNumber) {
        if (Objects.equals(topicCode, TopicCodeEnum.CUSTOMER_TRANSFER.getTopicCode())) {
            //PAM-ERP-072 & PAM-ERP-072-1
            customerTransferService.callback(applyNo, result, callBackMsg, serialNumber);
        }
    }


    public void handleGERP(Long applyNo, String topicCode, Boolean result, String message) {
        if (Objects.equals(topicCode, TopicCodeEnum.PURCHASE_ORDER.getTopicCode())) {
            //PAM-ERP-034
            PurchaseOrderDto item = new PurchaseOrderDto();
            item.setId(applyNo);
            if (result) {
                item.setSyncStatus(PurchaseOrderSyncStatus.SYNCED.code());
            } else {
                item.setSyncStatus(PurchaseOrderSyncStatus.FAILED_SYNC.code());
                item.setErpMessage(message);
            }
            purchaseOrderService.save(item, null);
        }
    }

    private void dataInCallBack(SdpDataImportReplyToDto replyDto) {
        if (Objects.equals(TopicCodeEnum.PURCHASE_CONTRACT.getTopicCode(), replyDto.getTopicCode())) {
            logger.info("SDP采购合同推送回调信息，replyDto:{}", replyDto);
            handlePurchaseContract(replyDto);
        }
    }

    /**
     * 处理FAP订单创建接口的SDP回调
     *
     * @param tradeImportReplyToDto SDP回调数据传输对象
     * @param esbResultReturn ESB结果返回记录
     */
    private void processFapOrderCreateCallback(SdpDataTradeImportReplyToDto tradeImportReplyToDto, EsbResultReturn esbResultReturn) {
        String businessId = tradeImportReplyToDto.getBusinessId();
        logger.info("开始处理FAP订单创建接口回调，流水号:{}", businessId);

        try {
            List<PurchaseOrderFapSdpCallbackExtendJsonDto> extraList = JSON.parseArray(
                tradeImportReplyToDto.getExtendJson(), PurchaseOrderFapSdpCallbackExtendJsonDto.class);

            if (ListUtils.isEmpty(extraList)) {
                logger.warn("FAP订单创建回调数据为空，流水号:{}", businessId);
                return;
            }

            logger.info("FAP订单创建回调处理开始，共{}条记录，流水号:{}", extraList.size(), businessId);

            for (PurchaseOrderFapSdpCallbackExtendJsonDto callbackDto : extraList) {
                processSingleFapOrderCallback(callbackDto, esbResultReturn, businessId);
            }

            logger.info("FAP订单创建回调处理完成，流水号:{}", businessId);

        } catch (Exception e) {
            logger.error("处理FAP订单创建接口回调异常，流水号:{}，错误信息:{}", businessId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 处理单个FAP订单回调记录
     *
     * @param callbackDto 回调数据传输对象
     * @param esbResultReturn ESB结果返回记录
     * @param businessId 业务流水号
     */
    private void processSingleFapOrderCallback(PurchaseOrderFapSdpCallbackExtendJsonDto callbackDto,
                                             EsbResultReturn esbResultReturn, String businessId) {
        // 提取回调信息
        String orderId = callbackDto.getSourceId2();
        String callBackCode = callbackDto.getCallBackCode();
        String callBackMsg = callbackDto.getCallBackMsg();

        logger.info("处理单个FAP订单回调，订单ID:{}，状态码:{}，消息:{}，流水号:{}",
                   orderId, callBackCode, callBackMsg, businessId);

        // 创建并保存ESB结果返回记录
        EsbResultReturnRecord esbResultReturnRecord = createEsbResultReturnRecord(callbackDto, esbResultReturn);
        esbResultReturnRecordService.insert(esbResultReturnRecord);
        logger.info("ESB结果返回记录已保存，记录ID:{}，流水号:{}", esbResultReturnRecord.getId(), businessId);

        // 更新采购订单状态
        boolean updateSuccess = updatePurchaseOrderStatus(orderId, callBackCode, callBackMsg, businessId);

        // 更新处理标志
        String handleFlag = updateSuccess ? "Y" : "N";
        esbResultReturnRecord.setHandleFlag(handleFlag);
        esbResultReturnRecordService.updateByPrimaryKeySelective(esbResultReturnRecord);

        logger.info("单个FAP订单回调处理完成，订单ID:{}，处理结果:{}，流水号:{}",
                   orderId, updateSuccess ? "成功" : "失败", businessId);
    }

    /**
     * 创建ESB结果返回记录
     *
     * @param callbackDto 回调数据传输对象
     * @param esbResultReturn ESB结果返回记录
     * @return ESB结果返回记录详情
     */
    private EsbResultReturnRecord createEsbResultReturnRecord(PurchaseOrderFapSdpCallbackExtendJsonDto callbackDto,
                                                            EsbResultReturn esbResultReturn) {
        EsbResultReturnRecord record = new EsbResultReturnRecord();
        record.setSourceId1(callbackDto.getSourceId1());
        record.setSourceId2(callbackDto.getSourceId2());
        record.setSourceId3(callbackDto.getSourceId3());
        record.setStatus(callbackDto.getCallBackCode());
        record.setTargetId1(callbackDto.getTargetId1());
        record.setTargetId2(callbackDto.getTargetId2());
        record.setTargetId3(callbackDto.getTargetId3());

        // 如果不是完成状态，设置错误信息
        if (!"COMPLETE".equals(callbackDto.getCallBackCode())) {
            record.setErrorCode(callbackDto.getCallBackCode());
            record.setErrorMessage(callbackDto.getCallBackMsg());
        }

        record.setApplyNo(callbackDto.getSourceId2());
        record.setReturnId(esbResultReturn.getId());

        return record;
    }

    /**
     * 更新采购订单状态
     *
     * @param orderId 订单ID
     * @param callBackCode 回调状态码
     * @param callBackMsg 回调消息
     * @param businessId 业务流水号
     * @return 更新是否成功
     */
    private boolean updatePurchaseOrderStatus(String orderId, String callBackCode, String callBackMsg, String businessId) {
        try {
            if (Objects.isNull(orderId)) {
                logger.warn("订单ID为空，跳过状态更新，流水号:{}", businessId);
                return true;
            }

            PurchaseOrderDto dto = new PurchaseOrderDto();
            dto.setId(Long.valueOf(orderId));

            if ("COMPLETED".equals(callBackCode)) {
                dto.setSyncStatus(PurchaseOrderSyncStatus.SYNCED.code());
                dto.setErpMessage("");
                logger.info("订单同步成功，订单ID:{}，流水号:{}", orderId, businessId);
            } else {
                dto.setSyncStatus(PurchaseOrderSyncStatus.FAILED_SYNC.code());
                dto.setErpMessage(callBackMsg);
                logger.warn("订单同步失败，订单ID:{}，错误信息:{}，流水号:{}", orderId, callBackMsg, businessId);
            }

            purchaseOrderService.save(dto, null);
            logger.info("采购订单状态更新成功，订单ID:{}，同步状态:{}，流水号:{}",
                       orderId, dto.getSyncStatus(), businessId);
            return true;

        } catch (Exception e) {
            logger.error("更新采购订单状态异常，订单ID:{}，流水号:{}，错误信息:{}",
                        orderId, businessId, e.getMessage(), e);
            return false;
        }
    }

    private void handlePurchaseContract(SdpDataImportReplyToDto replyDto) {
        SdpLog sdpLog = new SdpLog();
        String businessId = replyDto.getBusinessId();
        Boolean result = replyDto.getResult();
        String code = replyDto.getCode();
        List<SdpDataImportReplyToDto.KeyMapResponseDTO> params = replyDto.getParams();
        sdpLog.setSdpBusinessId(businessId);
        sdpLog.setResponseCode(code);
        if(result){
            sdpLog.setStatus(SdpLogStatusEnum.SEND_SUCCESS.getCode());
            sdpLog.setResponseMessage(params.get(0).getMessage());
        }else{
            sdpLog.setStatus(SdpLogStatusEnum.SEND_FAIL.getCode());
            sdpLog.setResponseMessage(params.get(0).getMessage());
        }
        sdpLogService.update(sdpLog);
    }

}
