package com.midea.pam.ctc.ext.service.impl;

import com.midea.pam.common.base.EsbResponse;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.entity.ProjectSynchro;
import com.midea.pam.common.ctc.entity.ResendExecute;
import com.midea.pam.common.enums.BusinessTypeEnums;
import com.midea.pam.common.enums.CommonStatus;
import com.midea.pam.common.enums.ProjectStatus;
import com.midea.pam.common.enums.ResponseCodeEnums;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.api.server.PassGcebToPamService;
import com.midea.pam.ctc.mapper.ProjectSynchroMapper;
import com.midea.pam.ctc.service.EsbService;
import com.midea.pam.ctc.service.GEMSCarrierServicel;
import com.midea.pam.ctc.service.ProjectService;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 写入项目号到外围(GFP,GEMS,GERP)系统
 * PAM-EMS-004,PAM-GFP-007,PAM-ERP-007
 */
public class ProjectCodeInputServiceImpl extends AbstractCommonBusinessService<ProjectDto> {
    @Resource
    private ProjectService projectService;
    @Resource
    private EsbService esbService;
    @Resource
    private ProjectSynchroMapper projectSynchroMapper;
    @Resource
    private PassGcebToPamService passGcebToPamService;
    @Resource
    private GEMSCarrierServicel gemsCarrierServicel;


    @Override
    public void init(ResendExecute resendExecute) {
        setInfo(projectService.findDetail(Long.parseLong(resendExecute.getApplyNo())));
    }

    /**
     * 基础校验.
     *
     * @param resendExecute
     */
    @Override
    public void validate(ResendExecute resendExecute) {
        Assert.notNull(info.getCode(), "项目号不能为空");
        Assert.notNull(info.getManagerMip(), "项目经理不能为空");
        Assert.notNull(info.getFinancialMip(), "项目财务不能为空");
        Assert.notNull(info.getOuId(), "ou不能为空");
        Assert.notNull(info.getOuCode(), "ou不能为空");

    }

    /**
     * 执行发送报文.
     *
     * @param resendExecute
     */
    @Override
    public EsbResponse execute(ResendExecute resendExecute) {
        EsbResponse response = null;
        if (Objects.equals(BusinessTypeEnums.CREATE_PROJECT_CODE_EMS.getCode(), resendExecute.getBusinessType())) {
            response = pushGemsProjectNumber(info, resendExecute);
        }
        if (Objects.equals(BusinessTypeEnums.CREATE_PROJECT_CODE_GFP.getCode(), resendExecute.getBusinessType())) {
            response = passGcebToPamService.pushGcebItemNumber(info);
        }
        if (Objects.equals(BusinessTypeEnums.CREATE_PROJECT_CODE_ERP.getCode(), resendExecute.getBusinessType())) {
            response = esbService.callCUXESBCOMMONINAPIPKGPortType(info);
        }
        return response;
    }

    private EsbResponse pushGemsProjectNumber(ProjectDto project, ResendExecute resendExecute) {
        // 有子单据则是ProjectSynchroJob推送失败记录，根据ProjectSynchro查询推送的项目状态
        ProjectSynchro projectSynchro = null;
        Integer status = ProjectStatus.APPROVALED.getCode();
        String subApplyNo = resendExecute.getSubApplyNo();
        if (StringUtils.isNotEmpty(subApplyNo)) {
            projectSynchro = projectSynchroMapper.selectByPrimaryKey(Long.valueOf(subApplyNo));
            if (projectSynchro != null) {
                status = projectSynchro.getType();
            }
        }
//        EsbResponse response = esbService.callGemsProjectNumberService(info, status);
        EsbResponse response = gemsCarrierServicel.gemsProjectNumber(info, status, null);
        // 回写状态同步记录表
        if (projectSynchro != null) {
            projectSynchro.setErrMsg(response.getResponsemessage());
            projectSynchro.setEsbSerialNo(null != response.getData() ? String.valueOf(response.getData()) : null);
            projectSynchro.setStatus(Objects.equals(response.getResponsecode(), ResponseCodeEnums.SUCESS.getCode()) ?
                    CommonStatus.DONE.getCode() : CommonStatus.ERROR.getCode());
            projectSynchroMapper.updateByPrimaryKey(projectSynchro);
        }
        return response;
    }


}
