package com.midea.pam.ctc.ext.service.impl;

import com.midea.pam.common.base.EsbResponse;
import com.midea.pam.common.ctc.dto.ReceiptClaimContractRelDto;
import com.midea.pam.common.ctc.dto.ReceiptClaimDto;
import com.midea.pam.common.ctc.entity.ResendExecute;
import com.midea.pam.common.enums.ResponseCodeEnums;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.common.enums.CommonErpStatus;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.ListUtil;
import com.midea.pam.ctc.mapper.ReceiptClaimExtMapper;
import com.midea.pam.ctc.service.EsbService;
import com.midea.pam.ctc.service.ReceiptClaimService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * @program: pam
 * @description: PAM-ERP-070 PAM收款合同分配变更同步ERP接口
 * @author: xiedl
 * @create: 2021-11-29
 **/
public class ReceiptClaimChangeEsbServiceImpl extends AbstractCommonBusinessService {

    @Resource
    private EsbService esbService;

    @Resource
    private ReceiptClaimService receiptClaimService;

    @Resource
    ReceiptClaimExtMapper receiptClaimExtMapper;

    private static final Logger logger = LoggerFactory.getLogger(ReceiptClaimChangeEsbServiceImpl.class);

    @Override
    public EsbResponse execute(ResendExecute resendExecute) {
        return null;
    }

    /**
     * 执行批量发送报文.
     * @param resendExecutes
     */
    @Override
    public EsbResponse execute(List resendExecutes) {
        List<ResendExecute> list = BeanConverter.copy(resendExecutes, ResendExecute.class);
        List<Long> ids = ListUtil.map(list, "applyNo");
        ReceiptClaimDto paramDto = new ReceiptClaimDto();
        paramDto.setIdList(ids);
        List<ReceiptClaimDto> receiptClaimDtoList = receiptClaimExtMapper.queryDetailList(paramDto);
        // 查询子合同编号
        List<ReceiptClaimContractRelDto> claimContractRelDtoList = receiptClaimExtMapper.queryReceiptClaimContractRel(paramDto);
        Map<Long, String> claimContractRelMap = new HashMap<>();
        if (ListUtils.isNotEmpty(claimContractRelDtoList)) {
            claimContractRelMap = claimContractRelDtoList
                    .stream()
                    .collect(Collectors.toMap(ReceiptClaimContractRelDto::getReceiptClaimDetailId, ReceiptClaimContractRelDto::getContractCode));
        }
        for(ReceiptClaimDto receiptClaimDto : receiptClaimDtoList) {
            receiptClaimDto.setAttribute3(claimContractRelMap.get(receiptClaimDto.getId()));
        }
        if(CollectionUtils.isNotEmpty(receiptClaimDtoList)) {
            return esbService.callCUXARRECEIPTAPIPKGReceiptChangeService(receiptClaimDtoList);
        }
        return new EsbResponse();
    }


    /**
     * 回调.
     * @param resendExecutes
     */
    @Override
    public void callback(List resendExecutes) {
        logger.info("PAM收款合同分配变更同步ERP接口同步回调："+ resendExecutes);
        List<ResendExecute> list = BeanConverter.copy(resendExecutes, ResendExecute.class);
        ReceiptClaimDto dto = new ReceiptClaimDto();
        for (ResendExecute execute : list) {
            if (StringUtils.isNotEmpty(execute.getApplyNo())) {
                if(Objects.equals(execute.getResponCode(), ResponseCodeEnums.SUCESS.getCode())){
                    dto.setContractSyncStatus(CommonErpStatus.PUSHED.code());
                    dto.setContractSyncMessage("");
                }else {
                    dto.setContractSyncStatus(CommonErpStatus.PUSH_FAILED.code());
                    dto.setContractSyncMessage(execute.getResponMsg());
                }
                dto.setId(Long.valueOf(execute.getApplyNo()));
                receiptClaimService.saveClaimDetailContractSyncStatus(dto);
            }
        }
    }

}
