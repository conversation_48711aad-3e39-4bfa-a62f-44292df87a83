package com.midea.pam.ctc.job;

import com.midea.pam.common.util.DateUtils;
import com.midea.pam.ctc.service.LaborCostDetailService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @description: 人工成本明细hard_working字段同步任务
 * @author: system
 * @create: 2025-07-03
 **/
@Component
public class LaborCostHardWorkingSyncJob {

    private Logger logger = LoggerFactory.getLogger(LaborCostHardWorkingSyncJob.class);

    @Resource
    private LaborCostDetailService laborCostDetailService;


    @XxlJob("laborCostHardWorkingSyncJob")
    public ReturnT<String> execute(String lastUpdateDate) {
        logger.info("LaborCostHardWorkingSyncJob task invoke start............");
        logger.info("最后更新时间的输入参数：" + lastUpdateDate);
        
        // 参数处理：为空的话最后更新时间取3天前
        if (StringUtils.isEmpty(lastUpdateDate)) {
            Date date = DateUtils.subtractDay(new Date(), 3);
            lastUpdateDate = DateUtils.format(date, "yyyy-MM-dd 00:00:00");
        }

        try {
            laborCostDetailService.syncLaborCostHardWorking(lastUpdateDate);
        } catch (Exception e) {
            logger.error("人工成本明细hard_working字段同步任务失败: " + e.getMessage(), e);
            return ReturnT.FAIL;
        }

        logger.info("LaborCostHardWorkingSyncJob task invoke end............");
        return ReturnT.SUCCESS;
    }
}













