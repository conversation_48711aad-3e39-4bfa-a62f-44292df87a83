package com.midea.pam.ctc.ext.service.impl;

import com.midea.mcomponent.core.util.Assert;
import com.midea.pam.common.base.EsbResponse;
import com.midea.pam.common.crm.dto.CommonByErpDto;
import com.midea.pam.common.ctc.entity.InvoiceReceivable;
import com.midea.pam.common.ctc.entity.ResendExecute;
import com.midea.pam.common.enums.BusinessTypeEnums;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.ctc.mapper.InvoiceReceivableMapper;
import com.midea.pam.ctc.service.EsbService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;

/**
 * PAM-ERP-074 应收发票到期日变更推送
 * <AUTHOR>
 */
public class InvoiceReceivableDueDateSyncService extends AbstractCommonBusinessService {
    private Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    InvoiceReceivableMapper invoiceReceivableMapper;
    @Resource
    private EsbService esbService;

    @Override
    public EsbResponse execute(ResendExecute resendExecute) {
        logger.info("应收发票到期日同步开始：" + resendExecute);
        String applyNo = resendExecute.getApplyNo();
        Assert.notNull(applyNo, "applyNo不能为空");
        InvoiceReceivable receivable = invoiceReceivableMapper.selectByPrimaryKey(Long.parseLong(applyNo));
        Assert.notNull(receivable, String.format("id为【%s】的应收发票不存在",applyNo));

        CommonByErpDto erpDto = new CommonByErpDto();
        erpDto.setEsbDataSource(BusinessTypeEnums.INVOICE_RECEIVABLE_DUE_DATE_SYNC.getCode());
        erpDto.setId(receivable.getId());
        erpDto.setCol1("PAM导入");
        erpDto.setCol2(receivable.getInvoiceCode());
        erpDto.setCol3(String.valueOf(receivable.getOuId()));
        erpDto.setCol4(DateUtils.formatDate(receivable.getDueDate()));

        return esbService.callCUXESBCOMMONINAPIPKGDDPortType(erpDto);
    }
}
