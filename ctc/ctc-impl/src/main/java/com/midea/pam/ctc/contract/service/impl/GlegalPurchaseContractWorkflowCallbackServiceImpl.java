package com.midea.pam.ctc.contract.service.impl;

import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.ctc.dto.GlegalContractFileInfoDto;
import com.midea.pam.common.ctc.entity.Contract;
import com.midea.pam.common.ctc.entity.ContractChangeway;
import com.midea.pam.common.ctc.entity.ContractChangewayExample;
import com.midea.pam.common.ctc.entity.ContractExample;
import com.midea.pam.common.ctc.entity.PaymentApply;
import com.midea.pam.common.ctc.entity.PaymentApplyExample;
import com.midea.pam.common.ctc.entity.PaymentInvoiceDetail;
import com.midea.pam.common.ctc.entity.PaymentInvoiceDetailExample;
import com.midea.pam.common.ctc.entity.PaymentPlan;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.PurchaseContract;
import com.midea.pam.common.ctc.entity.PurchaseContractChangeHeader;
import com.midea.pam.common.ctc.entity.PurchaseContractChangeHeaderExample;
import com.midea.pam.common.ctc.entity.PurchaseContractChangeHistory;
import com.midea.pam.common.ctc.entity.PurchaseContractChangeHistoryExample;
import com.midea.pam.common.ctc.entity.PurchaseContractExample;
import com.midea.pam.common.ctc.vo.ContractVO;
import com.midea.pam.common.enums.ContractChangewayStatus;
import com.midea.pam.common.enums.ContractStatus;
import com.midea.pam.common.enums.HistoryType;
import com.midea.pam.common.enums.PaymentPlanStatus;
import com.midea.pam.common.enums.PurchaseContractStatus;
import com.midea.pam.common.gateway.entity.GlegalFileInfo;
import com.midea.pam.common.gateway.entity.GlegalFileInfoExample;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.PublicUtil;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.enums.PurchaseContractChangeHeaderStatus;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.contract.service.ContractService;
import com.midea.pam.ctc.contract.service.GlegalPurchaseContractWorkflowCallbackService;
import com.midea.pam.ctc.contract.service.PaymentPlanService;
import com.midea.pam.ctc.contract.service.PurchaseContractService;
import com.midea.pam.ctc.contract.service.helper.PurchaseContractHelper;
import com.midea.pam.ctc.mapper.ContractChangewayMapper;
import com.midea.pam.ctc.mapper.ContractMapper;
import com.midea.pam.ctc.mapper.GlegalFileInfoExtMapper;
import com.midea.pam.ctc.mapper.PaymentApplyMapper;
import com.midea.pam.ctc.mapper.PaymentInvoiceDetailMapper;
import com.midea.pam.ctc.mapper.PaymentPlanChangeHistoryMapper;
import com.midea.pam.ctc.mapper.PaymentPlanMapper;
import com.midea.pam.ctc.mapper.ProjectMapper;
import com.midea.pam.ctc.mapper.PurchaseContractChangeHeaderMapper;
import com.midea.pam.ctc.mapper.PurchaseContractChangeHistoryMapper;
import com.midea.pam.ctc.mapper.PurchaseContractDetailChangeHistoryMapper;
import com.midea.pam.ctc.mapper.PurchaseContractDetailMapper;
import com.midea.pam.ctc.mapper.PurchaseContractMapper;
import com.midea.pam.ctc.mapper.PurchaseContractPenaltyChangeHistoryMapper;
import com.midea.pam.ctc.mapper.PurchaseContractPenaltyMapper;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.ProjectBusinessService;
import com.midea.pam.ctc.service.ProjectContractRsService;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-10-09
 * @description 法务法务采购合同流程回调
 */
public class GlegalPurchaseContractWorkflowCallbackServiceImpl extends GlegalPurchaseContractWorkflowCallbackService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    private BasedataExtService basedataExtService;

    @Resource
    private PurchaseContractService purchaseContractService;

    @Resource
    private PaymentPlanService paymentPlanService;

    @Resource
    private PurchaseContractMapper purchaseContractMapper;

    @Resource
    private PurchaseContractChangeHistoryMapper purchaseContractChangeHistoryMapper;

    @Resource
    private ProjectMapper projectMapper;

    @Resource
    private PurchaseContractChangeHeaderMapper purchaseContractChangeHeaderMapper;

    @Resource
    private PaymentInvoiceDetailMapper paymentInvoiceDetailMapper;

    @Resource
    private PaymentApplyMapper paymentApplyMapper;

    @Resource
    private ContractMapper contractMapper;

    @Resource
    private ContractChangewayMapper contractChangewayMapper;

    @Resource
    private ContractService contractService;

    @Resource
    private ProjectBusinessService projectBusinessService;

    @Resource
    private PurchaseContractDetailChangeHistoryMapper purchaseContractDetailChangeHistoryMapper;

    @Resource
    private PurchaseContractDetailMapper purchaseContractDetailMapper;

    @Resource
    private ProjectContractRsService projectContractRsService;

    @Resource
    private PaymentPlanChangeHistoryMapper paymentPlanChangeHistoryMapper;

    @Resource
    private PaymentPlanMapper paymentPlanMapper;

    @Resource
    private PurchaseContractPenaltyChangeHistoryMapper purchaseContractPenaltyChangeHistoryMapper;

    @Resource
    private PurchaseContractPenaltyMapper purchaseContractPenaltyMapper;

    @Resource
    private GlegalFileInfoExtMapper glegalFileInfoExtMapper;


    private final static Integer CHANGE_TYPE_TWO = 2;
    //变更状态 1->审批中 2->驳回 3->审批通过 4->撤回 5->作废 6->删除
    private final static Integer APPROVAL_TYPE_ONE = ContractChangewayStatus.PENDING.getCode();
    //合同审批状态
    private final static Integer FLOW_STATE_CHANGING = ContractStatus.CHANGING.getCode();
    private final static Integer APPROVAL_TYPE_TWO = ContractChangewayStatus.REFUSE.getCode();
    private final static Integer APPROVAL_TYPE_FOURE = ContractChangewayStatus.WITHDRAW.getCode();


    @Override
    public void draftSubmit(GlegalContractFileInfoDto glegalContractFileInfoDto) {
        String formInstanceId = glegalContractFileInfoDto.getBusinessId();
        String contractStatus = glegalContractFileInfoDto.getContractStatus();
        List<GlegalContractFileInfoDto.FileInfos> fileInfos = glegalContractFileInfoDto.getFileInfos();
        logger.info("法务采购合同审批提交审批回调 formInstanceId:{}, contractStatus:{}", formInstanceId, contractStatus);
        if (StringUtils.isEmpty(formInstanceId)) {
            logger.error("法务采购合同审批提交审批回调 formInstanceId为空，不处理");
            return;
        }

        final PurchaseContract purchaseContract = purchaseContractService.findById(Long.valueOf(formInstanceId));
        if (purchaseContract == null) {
            logger.error("法务采购合同审批提交审批回调 formInstanceId对应合同不存在，不处理");
            return;
        }
        purchaseContract.setContractStatus(Integer.valueOf(contractStatus));
        purchaseContract.setStatus(PurchaseContractStatus.PENDING.getCode());
        //purchaseContractService.update(purchaseContract);
        logger.info("法务采购合同审批提交审批回调成功，状态更新为待审批");
        insertFileInfo(fileInfos, purchaseContract);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void pass(GlegalContractFileInfoDto glegalContractFileInfoDto) {
        String formInstanceId = glegalContractFileInfoDto.getBusinessId();
        Long companyId = glegalContractFileInfoDto.getCompanyId();
        List<GlegalContractFileInfoDto.FileInfos> fileInfos = glegalContractFileInfoDto.getFileInfos();
        String contractStatus = glegalContractFileInfoDto.getContractStatus();
        Date filingDate = glegalContractFileInfoDto.getFilingDate();
        logger.info("法务采购合同审批通过回调 formInstanceId:{}, contractStatus:{}, filingDate:{}", formInstanceId, contractStatus, filingDate);
        String seqPerfix = basedataExtService.getUnitSeqPerfix(companyId);

        if (StringUtils.isEmpty(formInstanceId)) {
            logger.error("法务采购合同审批通过回调 formInstanceId为空，不处理");
            return;
        }

        final PurchaseContract purchaseContract = purchaseContractService.findById(Long.valueOf(formInstanceId));
        if (purchaseContract == null) {
            logger.error("法务采购合同审批通过回调 formInstanceId对应合同不存在，不处理");
            return;
        }
        //防止法务重复且延迟回调
        if (Objects.equals(purchaseContract.getStatus(), PurchaseContractStatus.CHANGEING.getCode())) {
            return;
        }

        Date now = new Date();
        int status = PurchaseContractStatus.EFFECTIVE.getCode();
        purchaseContract.setStatus(status);
        purchaseContract.setFilingDate(now);
        purchaseContract.setContractStatus(Integer.valueOf(contractStatus));
        purchaseContract.setFilingDate(filingDate);
        final List<PaymentPlan> paymentPlans = paymentPlanService.findByContractId(purchaseContract.getId());
        if (ListUtils.isNotEmpty(paymentPlans)) {
            paymentPlans.forEach(paymentPlan -> {
                final String paymentPlanCode = PurchaseContractHelper.generatePaymentPlanCode(seqPerfix);
                paymentPlan.setCode(paymentPlanCode);
                paymentPlan.setStatus(PaymentPlanStatus.UNPAYED.getCode());
                paymentPlanService.update(paymentPlan);
            });
        }
        insertFileInfo(fileInfos, purchaseContract);
    }

    @Override
    public void refuse(GlegalContractFileInfoDto glegalContractFileInfoDto) {
        String formInstanceId = glegalContractFileInfoDto.getBusinessId();
        String contractStatus = glegalContractFileInfoDto.getContractStatus();
        logger.info("法务采购合同审批驳回回调 formInstanceId:{}, contractStatus:{}", formInstanceId, contractStatus);
        if (StringUtils.isEmpty(formInstanceId)) {
            logger.error("法务采购合同审批驳回回调 formInstanceId为空，不处理");
            return;
        }

        final PurchaseContract purchaseContract = purchaseContractService.findById(Long.valueOf(formInstanceId));
        if (purchaseContract == null) {
            logger.error("法务采购合同审批驳回回调 formInstanceId对应合同不存在，不处理");
            return;
        }
        purchaseContract.setContractStatus(Integer.valueOf(contractStatus));
        purchaseContract.setStatus(PurchaseContractStatus.REFUSE.getCode());
        purchaseContractService.update(purchaseContract);
    }

    @Override
    public void abandon(String formInstanceId, String handlerId, PurchaseContractStatus purchaseContractStatus, String contractStatus) {
        changeContractFlow(formInstanceId, purchaseContractStatus, contractStatus);
    }


    @Override
    public void draftReturn(String formInstanceId, String contractStatus) {
        logger.info("法务采购合同审批撤回回调 formInstanceId:{}, contractStatus:{}", formInstanceId, contractStatus);
        if (StringUtils.isEmpty(formInstanceId)) {
            logger.error("法务采购合同审批撤回回调 formInstanceId为空，不处理");
            return;
        }

        final PurchaseContract purchaseContract = purchaseContractService.findById(Long.valueOf(formInstanceId));
        if (purchaseContract == null) {
            logger.error("法务采购合同审批撤回回调 formInstanceId对应合同不存在，不处理");
            return;
        }
        purchaseContract.setContractStatus(Integer.valueOf(contractStatus));
        purchaseContract.setStatus(PurchaseContractStatus.DRAFT.getCode());
        purchaseContractService.update(purchaseContract);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void baseInfoChangePass(GlegalContractFileInfoDto glegalContractFileInfoDto) {
        String formInstanceId = glegalContractFileInfoDto.getBusinessId();
        List<GlegalContractFileInfoDto.FileInfos> fileInfos = glegalContractFileInfoDto.getFileInfos();
        String contractStatus = glegalContractFileInfoDto.getContractStatus();
        logger.info("采购合同基本信息变更审批通过审批回调 formInstanceId:{}", formInstanceId);
        if (StringUtils.isEmpty(formInstanceId)) {
            logger.error("采购合同基本信息变更审批通过审批回调 formInstanceId为空，不处理");
            return;
        }
        final PurchaseContractChangeHeader header = purchaseContractChangeHeaderMapper.selectByPrimaryKey(Long.valueOf(formInstanceId));
        if (header == null) {
            logger.error("采购合同基本信息变更审批通过审批回调 formInstanceId对应合同不存在，不处理");
            return;
        }
        //防止重复回调
        if (Objects.equals(header.getStatus(), PurchaseContractChangeHeaderStatus.PASS.code())) {
            return;
        }
        //归集冲销
        purchaseContractService.collectionReverse(header.getId());

        PurchaseContractChangeHistoryExample example = new PurchaseContractChangeHistoryExample();
        example.createCriteria().andHistoryTypeEqualTo(HistoryType.CHANGE.getCode()).andHeaderIdEqualTo(Long.valueOf(formInstanceId));
        List<PurchaseContractChangeHistory> contractChangeHistoryList = purchaseContractChangeHistoryMapper.selectByExample(example);
        if (PublicUtil.isNotEmpty(contractChangeHistoryList)) {
            PurchaseContractChangeHistory contractChangeHistory = contractChangeHistoryList.get(0);
            PurchaseContract purchaseContract = BeanConverter.copy(contractChangeHistory, PurchaseContract.class);
            purchaseContract.setId(contractChangeHistory.getOriginId());
            //List<String> ids = fileInfos.stream().map(GlegalContractFileInfoDto.FileInfos::getId).collect(Collectors.toList());
            List<String> ids = fileInfos.stream().filter(i -> !i.getDeleteFlag() && ("StampArchive".equals(i.getFileSign()) || "ChangeProtocol".equals(i.getFileSign()))).map(GlegalContractFileInfoDto.FileInfos::getId).collect(Collectors.toList());
            String ifUploadChangeFile = String.join(",", ids);
            purchaseContract.setIfUploadChangeFile(ifUploadChangeFile);
            purchaseContractMapper.updateByPrimaryKeySelective(purchaseContract);
            final Project project = projectMapper.selectByPrimaryKey(purchaseContract.getProjectId());
            //供应商变更需同步更新采购发票对应的供应商信息（供应商ID，供应商名称、供应商编号、供应商地点）
            PaymentInvoiceDetailExample invoiceDetailExample = new PaymentInvoiceDetailExample();
            invoiceDetailExample.createCriteria().andPurchaseContractIdEqualTo(header.getPurchaseContractId());
            //采购发票列表
            List<PaymentInvoiceDetail> invoiceDetailList = paymentInvoiceDetailMapper.selectByExample(invoiceDetailExample);
            if (!org.springframework.util.CollectionUtils.isEmpty(invoiceDetailList)) {
                for (PaymentInvoiceDetail paymentInvoiceDetail : invoiceDetailList) {
                    paymentInvoiceDetail.setVendorId(contractChangeHistory.getVendorId());
                    paymentInvoiceDetail.setVendorCode(contractChangeHistory.getVendorCode());
                    paymentInvoiceDetail.setVendorName(contractChangeHistory.getVendorName());
                    paymentInvoiceDetail.setVendorSiteCode(contractChangeHistory.getVendorSiteCode());
                    paymentInvoiceDetail.setPurchaseContractName(purchaseContract.getName());
                    paymentInvoiceDetail.setProjectId(purchaseContract.getProjectId());
                    paymentInvoiceDetail.setProjectCode(project.getCode());
                    paymentInvoiceDetail.setProjectName(project.getName());
                    paymentInvoiceDetailMapper.updateByPrimaryKeySelective(paymentInvoiceDetail);
                }
            }
            //更新付款申请的冗余字段信息
            PaymentApplyExample applyExample = new PaymentApplyExample();
            applyExample.createCriteria().andPurchaseContractIdEqualTo(header.getPurchaseContractId());
            List<PaymentApply> paymentApplyList = paymentApplyMapper.selectByExample(applyExample);
            if (!org.springframework.util.CollectionUtils.isEmpty(paymentApplyList)) {
                for (PaymentApply paymentApply : paymentApplyList) {
                    paymentApply.setPurchaseContractName(purchaseContract.getName());
                    paymentApply.setProjectId(purchaseContract.getProjectId());
                    paymentApply.setProjectCode(project.getCode());
                    paymentApply.setProjectName(project.getName());
                    paymentApplyMapper.updateByPrimaryKey(paymentApply);
                }
            }
            changeHeadStatus(PurchaseContractChangeHeaderStatus.PASS.code(), Long.valueOf(formInstanceId), fileInfos, contractStatus);

        }
    }

    @Override
    public Long changeHeadStatus(Integer status, Long headerId, List<GlegalContractFileInfoDto.FileInfos> fileInfos, String contractStatus) {
        PurchaseContractChangeHeader header = setFormatHeader(status, headerId, contractStatus);
        Long purchaseContractId = header.getPurchaseContractId();
        PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(purchaseContractId);
        if (null != purchaseContract) {
            insertFileInfo(fileInfos, purchaseContract);
        }

        return headerId;
    }

    @Override
    public Long draftServiceChangeSubmit(Integer status, Long headerId, String contractStatus) {
        setFormatHeader(status, headerId, contractStatus);
        return headerId;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long passServiceChange(Long headerId, List<GlegalContractFileInfoDto.FileInfos> fileInfos, String contractStatus) {
        logger.info("采购合同内容变更审批通过审批回调 formInstanceId:{}", headerId);
        if (null == headerId) {
            logger.error("采购合同内容变更审批通过审批回调 formInstanceId为空，不处理");
            return null;
        }
        final PurchaseContractChangeHeader header = purchaseContractChangeHeaderMapper.selectByPrimaryKey(headerId);
        if (header == null) {
            logger.error("采购合同内容变更审批通过审批回调 formInstanceId对应合同不存在，不处理");
            return null;
        }
        //防止重复回调
        if (Objects.equals(header.getStatus(), PurchaseContractChangeHeaderStatus.PASS.code())) {
            return null;
        }
        //业务处理
        purchaseContractService.handleContractDetailChangePass(header, fileInfos, contractStatus);
        return headerId;
    }

    @Override
    public Long refuseServiceChange(Integer status, Long headerId, String contractStatus) {
        setFormatHeader(status, headerId, contractStatus);
        return headerId;
    }

    @Override
    public Long draftServiceChangeReturn(Integer status, Long headerId, String contractStatus) {
        setFormatHeader(status, headerId, contractStatus);
        return headerId;
    }

    @Override
    public Long serviceChangeAbandon(Integer status, Long headerId, String contractStatus) {
        setFormatHeader(status, headerId, contractStatus);
        return headerId;
    }

    /**
     * 销售合同新增流程作废和删除
     *
     * @param formInstanceId
     * @param purchaseContractStatus
     */
    private void changeContractFlow(String formInstanceId, PurchaseContractStatus purchaseContractStatus, String contractStatus) {
        PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(Long.valueOf(formInstanceId));
        if (purchaseContract != null) {
            if (purchaseContractStatus.equals(PurchaseContractStatus.CANCEL)) {  //作废
                purchaseContract.setStatus(PurchaseContractStatus.CANCEL.getCode());
            } else { //流程删除
                purchaseContract.setDeletedFlag(DeletedFlag.INVALID.code());
            }
            purchaseContract.setContractStatus(Integer.valueOf(contractStatus));
            purchaseContractMapper.updateByPrimaryKey(purchaseContract);
            PurchaseContractExample contractExample = new PurchaseContractExample();
            contractExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andIdEqualTo(purchaseContract.getId());
            List<PurchaseContract> childrenContractList = purchaseContractMapper.selectByExample(contractExample);
            if (CollectionUtils.isNotEmpty(childrenContractList)) {
                for (PurchaseContract children : childrenContractList) {
                    if (purchaseContractStatus.equals(PurchaseContractStatus.CANCEL)) {
                        children.setStatus(PurchaseContractStatus.CANCEL.getCode());
                    } else {
                        children.setDeletedFlag(DeletedFlag.INVALID.code());
                    }
                    purchaseContractMapper.updateByPrimaryKey(children);
                }
            }
        }
    }

    private Boolean changeStatus(PurchaseContractStatus status, Long purchaseContractId, Integer contractStatus) {
        PurchaseContract purchaseContract = new PurchaseContract();
        purchaseContract.setId(purchaseContractId);
        purchaseContract.setStatus(status.getCode());
        purchaseContract.setContractStatus(contractStatus);
        return purchaseContractMapper.updateByPrimaryKeySelective(purchaseContract) > 0;
    }

    /**
     * 提交审批,驳回，撤回公用方法
     *
     * @param formInstanceId
     * @param handlerId
     * @param changeType
     */
    private void flow(String formInstanceId, String handlerId, Integer changeType, Integer changesApprovalType, Integer flowState) {
        logger.info("合同变更提交审批回调 formInstanceId:{}, handlerId:{}", formInstanceId, handlerId);
        if (StringUtils.isEmpty(formInstanceId)) {
            logger.error("合同变更审批提交审批回调 formInstanceId为空，不处理");
            return;
        }
        ContractExample contractExample = new ContractExample();
        ContractExample.Criteria criteria = contractExample.createCriteria();
        criteria.andChangewayIdEqualTo(Long.valueOf(formInstanceId));
        final List<Contract> contracts = contractMapper.selectByExample(contractExample);
        Contract contract = new Contract();
        // 更新变更记录状态
        ContractChangewayExample contractChangewayExample = new ContractChangewayExample();
        ContractChangewayExample.Criteria criteria1 = contractChangewayExample.createCriteria();
        criteria1.andIdEqualTo(Long.valueOf(formInstanceId)).andChangeTypeEqualTo(changeType);
        List<ContractChangeway> contractChangeways = contractChangewayMapper.selectByExample(contractChangewayExample);
        if (CollectionUtils.isNotEmpty(contractChangeways)) {
            ContractChangeway contractChangeway = contractChangeways.get(0);
            contractChangeway.setApprovalStatus(changesApprovalType);

            contractChangewayMapper.updateByPrimaryKey(contractChangeway);
        }

        if (CollectionUtils.isEmpty(contracts)) {
            logger.error("合同变更审批驳回回调 formInstanceId对应合同变更不存在，不处理");
            return;
        } else {
            contract = contracts.get(0);
            contract.setStatus(flowState);
            contractService.update(contract);
            final List<ContractVO> childContracts = contractService.getListByParentId(contract.getId());

            if (CollectionUtils.isNotEmpty(childContracts)) {
                childContracts.forEach(contractVO -> {
                    Contract child = new Contract();
                    BeanUtils.copyProperties(contractVO, child);
                    child.setStatus(flowState);
                    contractService.update(child);
                });
            }
        }
    }

    private void saveFileInfo(List<GlegalContractFileInfoDto.FileInfos> fileInfos, PurchaseContract purchaseContract) {
        if (CollectionUtils.isNotEmpty(fileInfos) && Objects.nonNull(purchaseContract)) {
            List<String> originalContractAnnexList = new ArrayList<>();
            List<String> changeProtocolFileList = new ArrayList<>();
            List<String> annexList = new ArrayList<>();
            for (GlegalContractFileInfoDto.FileInfos fileInfo : fileInfos) {
                //收集法务系统回调删除标识为false的附件信息
                if (fileInfo.getDeleteFlag()) {
                    continue;
                }
                String fileSign = fileInfo.getFileSign();
                String fileId = fileInfo.getId();
                //ContractStampArchive-ContractNeedStamp采购合同原件,StampArchive-ChangeProtocol 变更协议，ContractNotNeedStamp 采购合同附件
                if ("ContractStampArchive".equals(fileSign) || "ContractNeedStamp".equals(fileSign)) {
                    originalContractAnnexList.add(fileId);
                } else if ("StampArchive".equals(fileSign) || "ChangeProtocol".equals(fileSign)) {
                    changeProtocolFileList.add(fileId);
                } else if ("ContractNotNeedStamp".equals(fileSign)) {
                    annexList.add(fileId);
                }
            }
            //1.更新合同原件，附件信息
            String originalContractAnnexStr = String.join(",", originalContractAnnexList);
            String annexStr = String.join(",", annexList);
            purchaseContract.setOriginalContractAnnex(originalContractAnnexStr);
            purchaseContract.setAnnex(annexStr);
            purchaseContractService.update(purchaseContract);

            //2.变更协议不为空，则更新变更协议
            String changeProtocolFileStr = String.join(",", changeProtocolFileList);
            if (StringUtils.isNotEmpty(changeProtocolFileStr)) {
                PurchaseContractChangeHeaderExample headerExample = new PurchaseContractChangeHeaderExample();
                headerExample.createCriteria().andPurchaseContractIdEqualTo(purchaseContract.getId())
                        .andDeletedFlagEqualTo(DeletedFlag.VALID.code());
                List<PurchaseContractChangeHeader> buildHeaders = purchaseContractChangeHeaderMapper.selectByExample(headerExample);
                if (CollectionUtils.isNotEmpty(buildHeaders)) {
                    PurchaseContractChangeHeader header = buildHeaders.get(0);
                    header.setChangeProtocolFile(changeProtocolFileStr.toString());
                    purchaseContractChangeHeaderMapper.updateByPrimaryKeySelective(header);
                }
            }
        }
    }

    private void insertFileInfo(List<GlegalContractFileInfoDto.FileInfos> fileInfos, PurchaseContract purchaseContract) {
        logger.info("更新附件信息，fileInfos参数为：{}，purchaseContract参数为：{}", fileInfos, purchaseContract);
        //1.处理合同原件，附件，变更协议，法务合同状态修改
        saveFileInfo(fileInfos, purchaseContract);
        logger.info("法务采购合同审批通过回调成功，状态更新为:{}", purchaseContract.getStatus());
        //2.更新附件表信息
        final UserInfo userInfo = CacheDataUtils.findUserById(SystemContext.getUserId());
        fileInfos.forEach(f -> {
            GlegalFileInfoExample glegalFileInfoExample = new GlegalFileInfoExample();
            glegalFileInfoExample.createCriteria().andDocIdEqualTo(f.getId());
            List<GlegalFileInfo> glegalFileInfos = glegalFileInfoExtMapper.selectByExample(glegalFileInfoExample);
            if (ListUtils.isNotEmpty(glegalFileInfos)) {
                //更新
                glegalFileInfos.get(0).setFileName(URLDecoder.decode(f.getFileName()));//文件名
                glegalFileInfos.get(0).setFileSize(Long.valueOf(f.getFileSize()));//文件大小
                glegalFileInfos.get(0).setFileType(f.getFileSurfix());//文件类型
                if (userInfo != null) {
                    glegalFileInfos.get(0).setMipAccount(userInfo.getUsername()); //mip账户
                }
                glegalFileInfos.get(0).setFileAttachmentId(f.getAttachmentId()); //模板库返回的附件attachmentId
                glegalFileInfos.get(0).setDeleteFlag(f.getDeleteFlag());
                glegalFileInfos.get(0).setDocId(f.getId());
                glegalFileInfoExtMapper.updateByPrimaryKeySelective(glegalFileInfos.get(0));
            } else {
                //插入
                GlegalFileInfo fileInfo = new GlegalFileInfo();
                fileInfo.setFileName(URLDecoder.decode(f.getFileName()));//文件名
                fileInfo.setFileSize(Long.valueOf(f.getFileSize()));//文件大小
                //String suffixName = f.getFileSurfix().substring(f.getFileSurfix().lastIndexOf("."));
                fileInfo.setFileType(f.getFileSurfix());//文件类型
                if (userInfo != null) {
                    fileInfo.setMipAccount(userInfo.getUsername()); //mip账户
                }
                fileInfo.setFileAttachmentId(f.getAttachmentId()); //模板库返回的附件attachmentId
                fileInfo.setDeleteFlag(f.getDeleteFlag());
                fileInfo.setDocId(f.getId());
                glegalFileInfoExtMapper.insert(fileInfo);
            }
        });
    }

    private PurchaseContractChangeHeader setFormatHeader(Integer status, Long headerId, String contractStatusStr) {
        Integer contractStatus = null;
        if (StringUtils.hasText(contractStatusStr)) {
            contractStatus = Integer.valueOf(contractStatusStr);
        }
        PurchaseContractChangeHeader header = new PurchaseContractChangeHeader();
        header.setId(headerId);
        header.setStatus(status);
        purchaseContractChangeHeaderMapper.updateByPrimaryKeySelective(header);
        header = purchaseContractChangeHeaderMapper.selectByPrimaryKey(headerId);
        if (status.equals(PurchaseContractChangeHeaderStatus.CHECKING.code())) {
            changeStatus(PurchaseContractStatus.CHANGEING, header.getPurchaseContractId(), contractStatus);
        } else if (status.equals(PurchaseContractChangeHeaderStatus.RETURN.code())
                || status.equals(PurchaseContractChangeHeaderStatus.REJECT.code())
                || status.equals(PurchaseContractChangeHeaderStatus.PASS.code())
                || status.equals(PurchaseContractChangeHeaderStatus.ABANDON.code())) {
            changeStatus(PurchaseContractStatus.EFFECTIVE, header.getPurchaseContractId(), contractStatus);
        } else if (status.equals(PurchaseContractChangeHeaderStatus.DELETE.code())) {
            changeStatus(PurchaseContractStatus.EFFECTIVE, header.getPurchaseContractId(), contractStatus);
            header.setDeletedFlag(Boolean.TRUE);
        }
        return header;
    }

}
