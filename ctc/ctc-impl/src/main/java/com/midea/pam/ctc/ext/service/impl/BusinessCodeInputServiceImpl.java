package com.midea.pam.ctc.ext.service.impl;

import com.midea.pam.common.base.EsbResponse;
import com.midea.pam.common.crm.dto.BusinessDto;
import com.midea.pam.common.crm.entity.Business;
import com.midea.pam.common.ctc.entity.ResendExecute;
import com.midea.pam.common.enums.BusinessTypeEnums;
import com.midea.pam.common.enums.CommonStatus;
import com.midea.pam.common.enums.ResponseCodeEnums;
import com.midea.pam.ctc.service.CrmExtService;
import com.midea.pam.ctc.service.EsbService;
import com.midea.pam.ctc.service.GEMSCarrierServicel;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @description: 写入商机号导GEMS系统
 * @author: ex_xuwj4
 * @create: 2021-07-19
 **/
public class BusinessCodeInputServiceImpl extends AbstractCommonBusinessService<BusinessDto> {

    @Resource
    private CrmExtService crmExtService;
    @Resource
    private EsbService esbService;
    @Resource
    private GEMSCarrierServicel gemsCarrierServicel;


    @Override
    public void init(ResendExecute resendExecute) {
        setInfo(crmExtService.getBusinessDetailById(Long.parseLong(resendExecute.getApplyNo())));
    }

    /**
     * 基础校验.
     *
     * @param resendExecute
     */
    @Override
    public void validate(ResendExecute resendExecute) {
        Assert.notNull(info.getBusinessCode(), "商机编号不能为空");
        Assert.notNull(info.getName(), "商机名称不能为空");
        Assert.notNull(info.getOperatingUnitId(), "ouId不能为空");
        Assert.notNull(info.getOuCode(), "ouCode不能为空");
        Assert.notNull(info.getOperatingUnitName(), "ou名称不能为空");
    }

    /**
     * 执行发送报文.
     *
     * @param resendExecute
     */
    @Override
    public EsbResponse execute(ResendExecute resendExecute) {
        EsbResponse response = null;
        if (Objects.equals(BusinessTypeEnums.CREATE_BUSINESS_CODE_EMS.getCode(), resendExecute.getBusinessType())) {
//            response = esbService.callGemsBusinessNumberService(info);
            response = gemsCarrierServicel.gemsProjectNumber(null, null, info);
            if (Objects.equals("winningBusiness", resendExecute.getSubApplyNo())) { //回写商机赢单传gems的状态
                Business business = new Business();
                business.setId(Long.parseLong(resendExecute.getApplyNo()));
                business.setGemsStatus(Objects.equals(response.getResponsecode(), ResponseCodeEnums.SUCESS.getCode()) ?
                        CommonStatus.DONE.getCode() : CommonStatus.ERROR.getCode());

                crmExtService.updateBusinessGemsStatus(business);
            }
        }
        return response;
    }
}
