package com.midea.pam.ctc.api.service.impl;

import cn.hutool.http.ContentType;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.midea.jr.commons.security.utils.SHAUtil;
import com.midea.pam.common.base.EsbResponse;
import com.midea.pam.common.basedata.dto.VendorSiteBankDto;
import com.midea.pam.common.basedata.entity.Dict;
import com.midea.pam.common.basedata.entity.OperatingUnit;
import com.midea.pam.common.crm.dto.CustomerDto;
import com.midea.pam.common.crm.entity.CustomerBankAccount;
import com.midea.pam.common.ctc.dto.PaymentApplyDto;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.dto.ReceiptClaimDto;
import com.midea.pam.common.ctc.dto.RefundApplyDTO;
import com.midea.pam.common.ctc.entity.EsbResultReturn;
import com.midea.pam.common.ctc.entity.ReceiptClaim;
import com.midea.pam.common.enums.ProjectStatus;
import com.midea.pam.common.enums.ResponseCodeEnums;
import com.midea.pam.common.esb.constant.EsbConstant;
import com.midea.pam.common.esb.dto.GcebReceiptClaim;
import com.midea.pam.common.esb.dto.PassRequest;
import com.midea.pam.common.esb.dto.PassResponse;
import com.midea.pam.common.esb.dto.SourceRecordItem;
import com.midea.pam.common.esb.util.EsbUtil;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.PamStringUtil;
import com.midea.pam.ctc.api.server.PassGcebToPamService;
import com.midea.pam.ctc.api.service.helper.GcebToPamHelper;
import com.midea.pam.ctc.esb.service.EsbResultReturnService;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.CrmExtService;
import com.midea.pam.framework.core.exception.Guard;
import deps.com.alibaba.fastjson.JSON;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.ObjectUtils;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;

public class PassGcebToPamServiceImpl implements PassGcebToPamService {
    private static final Logger logger = LoggerFactory.getLogger(PassGcebToPamServiceImpl.class);

    // 服务提供方：金融网关-插件管理-身份认证-验签配置-appId
    public static final String appId = "PAM";

    @Value("${gfp.apiUrl:}")
    private String apiUrl;

    @Value("${gfp.appKey:}")
    public String appKey;

    public static final String signType = "SHA256";

    @Resource
    private EsbResultReturnService esbResultReturnService;

    @Resource
    private GcebToPamHelper gcebToPamHelper;

    @Resource
    private BasedataExtService basedataExtService;
    @Resource
    private CrmExtService crmExtService;

    @Override
    public EsbResponse<String> advancePaymentApply(PaymentApplyDto paymentApplyDto) {
        Guard.notNull(paymentApplyDto, "付款申请单不能为空!");
        //交易流水号
        String serialNo = EsbConstant.PAM_REQUEST_ID + new Date().getTime() + "";
        try {
            //构建入参
            JSONObject params = this.buildAdvancePaymentApplyParams(paymentApplyDto, serialNo);
            //发起请求
            JSONObject jsonResult = callGceb("all-auz/pay/advance-payment", params);
            if (Objects.nonNull(jsonResult) && jsonResult.getString("responseCode").equals("000000")) {
                EsbResponse<String> esbResponse = new EsbResponse<>();
                esbResponse.setResponsecode(ResponseCodeEnums.SUCESS.getCode());
                esbResponse.setResponsemessage(jsonResult.getString("responseMessage"));
                esbResponse.setData(serialNo);
                return esbResponse;
            } else {
                String result = com.alibaba.fastjson.JSON.toJSONString(jsonResult);
                return new EsbResponse<>("", ResponseCodeEnums.FAULT.getCode(), result.length() > 3000 ? result.substring(0, 3000) : result, serialNo);
            }
        } catch (Exception e) {
            logger.error("预付款申请导入报错", e);
            return new EsbResponse<>("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), serialNo);
        }

    }

    @Override
    public EsbResponse<String> refundPaymentApply(RefundApplyDTO refundApplyDTO) {
        Guard.notNull(refundApplyDTO, "退款申请单不能为空!");
        //交易流水号
        String serialNo = EsbConstant.PAM_REQUEST_ID + new Date().getTime() + "";
        try {
            //构建入参
            JSONObject params = this.buildRefundPaymentApplyParams(refundApplyDTO, serialNo);
            //发起请求
            JSONObject jsonResult = callGceb("all-auz/refund-payment/apply", params);
            if (Objects.nonNull(jsonResult) && jsonResult.getString("responseCode").equals("000000")) {
                EsbResponse<String> esbResponse = new EsbResponse<>();
                esbResponse.setResponsecode(ResponseCodeEnums.SUCESS.getCode());
                esbResponse.setResponsemessage(jsonResult.getString("responseMessage"));
                esbResponse.setData(serialNo);
                return esbResponse;
            } else {
                String result = com.alibaba.fastjson.JSON.toJSONString(jsonResult);
                return new EsbResponse<>("", ResponseCodeEnums.FAULT.getCode(), result.length() > 3000 ? result.substring(0, 3000) : result, serialNo);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new EsbResponse<>("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), serialNo);
        }
    }

    @Override
    public EsbResponse<String> receiveCancel(ReceiptClaim receiptClaim) {
        Guard.notNull(receiptClaim, "收款信息不能为空!");
        //交易流水号
        String serialNo = EsbConstant.PAM_REQUEST_ID + new Date().getTime() + "";
        try {
            //构建入参
            JSONObject params = this.buildReceiveCancelParams(receiptClaim, serialNo);
            //发起请求
            JSONObject jsonResult = callGceb("all-txn/receive/cancel", params);
            if (Objects.nonNull(jsonResult) && jsonResult.getString("responseCode").equals("000000")) {
                EsbResponse<String> esbResponse = new EsbResponse<>();
                esbResponse.setResponsecode(ResponseCodeEnums.SUCESS.getCode());
                esbResponse.setResponsemessage(jsonResult.getString("responseMessage"));
                esbResponse.setData(serialNo);
                return esbResponse;
            } else {
                String result = com.alibaba.fastjson.JSON.toJSONString(jsonResult);
                return new EsbResponse<>("", ResponseCodeEnums.FAULT.getCode(), result.length() > 3000 ? result.substring(0, 3000) : result, serialNo);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new EsbResponse<>("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), serialNo);
        }
    }

    @Override
    public EsbResponse<String> pushGcebItemNumber(ProjectDto project) {
        Guard.notNull(project, "项目信息不存在");
        //交易流水号
        String serialNo = String.valueOf(project.getOuId());
        try {
            //构建入参
            JSONObject params = this.buildItemNumberParams(project, serialNo);
            //发起请求
            JSONObject jsonResult = callGceb("all-com/receive-pam-item-number", params);
            if (Objects.nonNull(jsonResult) && jsonResult.getString("responseCode").equals("000000")) {
                EsbResponse<String> esbResponse = new EsbResponse<>();
                esbResponse.setResponsecode(ResponseCodeEnums.SUCESS.getCode());
                esbResponse.setResponsemessage(jsonResult.getString("responseMessage"));
                esbResponse.setData(serialNo);
                return esbResponse;
            } else {
                String result = com.alibaba.fastjson.JSON.toJSONString(jsonResult);
                return new EsbResponse<>("", ResponseCodeEnums.FAULT.getCode(), result.length() > 3000 ? result.substring(0, 3000) : result, serialNo);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new EsbResponse<>("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), serialNo);
        }
    }

    private JSONObject buildAdvancePaymentApplyParams(PaymentApplyDto paymentApplyDto, String serialNo) {
        JSONObject params = new JSONObject();
        params.put("businesstype", "05"); // 05，预付款申请导入报文
        params.put("itemrecnum", 1); // 明细个数
        params.put("importdate", EsbUtil.formatXMLGreDate(new Date())); // 导入日期
        params.put("sourcesystemcode", EsbConstant.PAM_REQUEST_ID); // 系统来源编码
        params.put("transcode", serialNo); // 交易流水号
        params.put("recivecode", EsbConstant.PAM_REQUEST_ID); // 业务系统编码
        params.put("applyno", paymentApplyDto.getPaymentApplyCode()); // 预付款单号
        params.put("contractno", paymentApplyDto.getContractCode()); // 合同号
        params.put("supplierid", paymentApplyDto.getVendorErpId() + ""); // 供应商ID
        params.put("suppliername", paymentApplyDto.getVendorName()); // 供应商名称
        params.put("ouid", paymentApplyDto.getOuId() + ""); // 付款方信息-组织ID
        params.put("paytype", "ADVANCE"); // 默认"ADVANCE"(预付款)
        //付款金额不能为0,不能为负数,小数点后不能多于2位数 TODO
        params.put("payamount", paymentApplyDto.getTaxPayIncludedPrice()); // 付款金额
        params.put("currency", StringUtils.isEmpty(paymentApplyDto.getCurrency()) ? "CNY" : paymentApplyDto.getCurrency()); // 付款币种，默认CNY

        Long paymentMethodId = paymentApplyDto.getPaymentMethodId();//付款方式ID
        if (paymentMethodId != null && paymentMethodId > 0) {
            Dict dict = basedataExtService.findDictById(paymentMethodId);
            if (dict != null) {
                params.put("paymethod", dict.getCode());//付款方式 MD_XH：现汇, MD_CD：承兑,ADVANCE 预付款 todo
            }
        }
        params.put("recaccountname", paymentApplyDto.getAccountNum()); // 开户名
        params.put("recaccountno", paymentApplyDto.getVendorBankNum()); // 开户账号
        params.put("recbankname", paymentApplyDto.getAccountName()); // 开户行名称
        //联行号
        Long ouId = paymentApplyDto.getOuId();
        String vendorBankNum = paymentApplyDto.getVendorBankNum();
        String vendorCode = paymentApplyDto.getVendorCode();
        String accountName = paymentApplyDto.getAccountName();
        VendorSiteBankDto vendorSiteBankInfo = basedataExtService.getVendorSiteBankInfo(ouId, vendorBankNum, vendorCode, accountName);
        if (Objects.nonNull(vendorSiteBankInfo)) {
            params.put("province", vendorSiteBankInfo.getProvince()); //省
            params.put("city", vendorSiteBankInfo.getCity()); //市
            params.put("bankcode", vendorSiteBankInfo.getCombineBankNummber()); //币种为人民币时,联行号必填 TODO
        }


        params.put("supplieraddress", paymentApplyDto.getErpVendorSiteId() + ""); // 供应商地点ID
        params.put("budgetcode", paymentApplyDto.getItemNumber()); // 预算项目号
        //备注总长度不能大于80个汉字或240个字符
        params.put("remark", paymentApplyDto.getRemark()); // 备注

        params.put("country", paymentApplyDto.getTerritoryCode()); // 国家代码
        params.put("address", paymentApplyDto.getFullAddress()); // 详细地址
        params.put("swiftcode", paymentApplyDto.getSwiftCode()); // 银行swiftcode

        //外汇付款类型:A预付货款 O其他 P货到付款 R退款 TODO
        params.put("paymenttype", paymentApplyDto.getPayType()); // 付款类型
        //付汇性质:A其他特殊经济区 D砖石交易所 E出口加工区 M深加工转结 O其他 X保税区 TODO
        params.put("paynature", paymentApplyDto.getPayNature()); // 付汇性质
        params.put("transactioncode", paymentApplyDto.getTranCode()); // 交易编码
        params.put("transaction", paymentApplyDto.getTranMessage()); // 交易附言
        // 费用承担方式:O汇款人OUR B收款人BEN S共同SHA TODO
        params.put("costmethod", paymentApplyDto.getExpenseWay()); // 费用承担方式
        params.put("contractnum", paymentApplyDto.getContractCode()); // 合同号
        params.put("invoiceno", paymentApplyDto.getInvoiceCode()); // 发票号
        params.put("documentno", paymentApplyDto.getBusinessRecordCode()); // 外汇局批件/备案表号/业务编号
        // 是否保税货物项下付款 1是2否
        params.put("isgoodsunder", paymentApplyDto.getFreeTaxGoods()); // 是否保税货物项下付款

        logger.info("预付款导入请求params:{}", params.toJSONString());
        return params;
    }

    private JSONObject buildRefundPaymentApplyParams(RefundApplyDTO refundApplyDTO, String serialNo) {
        JSONObject params = new JSONObject();

        params.put("sendSerialNo", serialNo); // 交易流水号
        params.put("sourceSystemCode", EsbConstant.PAM_REQUEST_ID); // 系统来源编码
        // 封装 details
        JSONArray details = new JSONArray();
        //入参是单个，无需循环
        JSONObject detail = new JSONObject();
        detail.put("applyCode", refundApplyDTO.getRefundApplyCode()); // 退款申请单编号
        detail.put("customerCode", refundApplyDTO.getCustomerCode()); // 客户编号
        detail.put("customerName", refundApplyDTO.getCustomerName()); // 客户名称
        detail.put("ouId", refundApplyDTO.getOuId() + ""); // OU组织ID
        detail.put("refundApplyDate", DateUtils.getShortDate(refundApplyDTO.getRefundEntryDate())); // 退款日期
        detail.put("amount", refundApplyDTO.getAmount()); // 退款金额
        detail.put("currency", refundApplyDTO.getCurrency()); // 币种
        detail.put("receiveAccount", refundApplyDTO.getPayeeBankAccount()); // 收款方账户
        CustomerBankAccount customerBankInfo = crmExtService.getCustomerBankInfo(refundApplyDTO.getCustomerCode(), refundApplyDTO.getOuId());
        // 收款方账户名称
        if (Objects.nonNull(customerBankInfo) && StringUtils.isNotBlank(customerBankInfo.getInvoiceName())) {
            detail.put("payeeAccName", customerBankInfo.getInvoiceName());
        } else {
            detail.put("payeeAccName", refundApplyDTO.getInvoiceCustomeName());
        }
        detail.put("receiveBank", refundApplyDTO.getPayeeBankName()); // 收款方银行
        detail.put("receiveOpenBankNo", refundApplyDTO.getPayeeCnaps()); // 收款方开户银行号(联行号)
        detail.put("payType", refundApplyDTO.getPaymentType()); // 付款方式
        detail.put("budgetCode", refundApplyDTO.getBudgetProjectCode()); // 预算项目编号
        detail.put("remark", refundApplyDTO.getRefundReasonDes()); // 备注是空，这里要求必填，取退款原因描述
        detail.put("swiftCode", refundApplyDTO.getSwiftCode()); // 收款行SWIFT CODE
        details.add(detail);
        params.put("details", details);

        logger.info("退货款申请请求params:{}", params.toJSONString());
        return params;
    }

    private JSONObject buildReceiveCancelParams(ReceiptClaim receiptClaim, String serialNo) {
        JSONObject params = new JSONObject();
        params.put("businessType", "CANCEL-RECEIVE-CLAIM");//业务类型
        params.put("sourceSystemCode", EsbConstant.PAM_REQUEST_ID); // 系统来源编码
        params.put("recDocCode", receiptClaim.getCashReceiptCode());//收款单据号
        params.put("remark", receiptClaim.getRemark());//撤销原因，对方不落到数据库表
        params.put("ouId", String.valueOf(receiptClaim.getOuId()));//出口组织编号
        logger.info("收款撤销请求params:{}", params.toJSONString());
        return params;
    }

    private JSONObject buildItemNumberParams(ProjectDto project, String serialNo) {
        JSONObject params = new JSONObject();
        params.put("transCode", serialNo); // 交易流水号 - 项目id
        params.put("sourceSystemCode", EsbConstant.PAM_REQUEST_ID); // 系统来源编码
        // 封装 details
        JSONArray details = new JSONArray();
        JSONObject detail = new JSONObject();
        detail.put("ouId", String.valueOf(project.getOuId()));
        detail.put("ouCode", project.getOuCode());
        detail.put("ouName", project.getOuName());
        detail.put("itemNumber", project.getCode()); //项目编号
        detail.put("itemName", project.getName()); //项目名称
        detail.put("managerMip", project.getManagerMip()); // 项目经理MIP账号
        detail.put("managerMipName", project.getManagerName()); // 项目经理MIP名称
        detail.put("financeMip", project.getFinancialMip()); // 项目财务MIP账号
        detail.put("financeMipName", project.getFinancialName()); // 项目财务MIP名称
        detail.put("itemStatus", String.valueOf(ProjectStatus.APPROVALED.getCode())); //项目状态
        details.add(detail);
        params.put("itemDetail", details);

        logger.info("项目号同步请求params:{}", params.toJSONString());
        return params;
    }


    public JSONObject callGceb(String suffixUrl, JSONObject json) {
        //请求参数
        String bodyStr = json.toJSONString();
        // 当前时间戳
        String timestamp = "" + System.currentTimeMillis() / 1000;
        // 参数加盐拼接
        String paramsString = bodyStr + appKey + timestamp;
        // 参数sha256摘要  SHAUtil 引入pom
        String sha256 = SHAUtil.getSHA256(paramsString).toLowerCase();

        String result = null;
        // 服务方paas提供API地址
        String url = null;
        try {
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(apiUrl + suffixUrl);
            url = builder.build(true).toUri().toString();
            logger.info(String.format("请求地址：%s", url));
            result = HttpRequest.post(url)
                    .header(Header.CONTENT_TYPE, String.valueOf(ContentType.JSON))
                    .header("Timestamp", timestamp)
                    .header("Sign-Type", signType)
                    .header("App-Id", appId)
                    .header("Sign", sha256)
                    .body(bodyStr)
                    .timeout(20000)
                    .execute().body();
            logger.info(String.format("返回结果：%s", result));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            result = e.getMessage();
        }
        //插入接口调用日志表
        return com.alibaba.fastjson.JSON.parseObject(result);
    }


    @Override
    public PassResponse callback(PassRequest request) {
        logger.info("input PassGcebToPamService.callback method");
        PassResponse response = new PassResponse();
        //数据非空校验
        List<String> errorMsgList = callBackParamValidate(request);
        if (CollectionUtils.isNotEmpty(errorMsgList)) {
            response.setResponseCode("000001");
            response.setResponseMessage(String.join("\n", errorMsgList));
            return response;
        }
        try {
            if (request != null) {
                logger.info("PassGcebToPamService回调入参: {}", JSON.toJSONString(request));
                //存入日志表
                EsbResultReturn esbResultReturn = new EsbResultReturn();
                esbResultReturn.setDataSource(request.getDataSource());
                esbResultReturn.setRequestId(request.getRequestId());//来源系统
                esbResultReturn.setInput(JSON.toJSONString(request));
                esbResultReturn.setHandleFlag("N");
                esbResultReturnService.insert(esbResultReturn);

                List<SourceRecordItem> sourceRecordItemList = request.getDetails();
                if (CollectionUtils.isNotEmpty(sourceRecordItemList)) {
                    for (SourceRecordItem sourceRecordItem : sourceRecordItemList) {
                        gcebToPamHelper.callbackHandle(sourceRecordItem, request.getDataSource(), esbResultReturn, logger);
                    }
                }
                response.setResponseCode("000000");
                response.setResponseMessage("SUCCESS");
            } else {
                response.setResponseCode("000001");
                response.setResponseMessage("PassGcebToPamService回调报文无法解析");
            }
        } catch (Exception ex) {
            logger.error("PassGcebToPamService回调异常：", ex);
            response.setResponseCode("000001");
            response.setResponseMessage(ex.getStackTrace()[0].getClassName() + "/" + ex.getStackTrace()[0].getMethodName() + "/" + ex.getStackTrace()[0].getLineNumber()
                    + "/" + ex.getMessage());
        }
        return response;
    }

    private List<String> callBackParamValidate(PassRequest request) {
        List<String> errorMsgs = new ArrayList<>();
        if (StringUtils.isBlank(request.getRequestId())) {
            errorMsgs.add("来源系统名称不能为空");
        }
        if (StringUtils.isBlank(request.getDataSource())) {
            errorMsgs.add("回调业务编码不能为空");
        }
        if (ListUtils.isEmpty(request.getDetails())) {
            errorMsgs.add("交易明细数据不能为空");
        }
        for (SourceRecordItem item : request.getDetails()) {
            if (StringUtils.isBlank(item.getSourceId1()) || !Arrays.asList("1", "2").contains(item.getSourceId1())) {
                errorMsgs.add("sourceId1的值只能为1或者2");
            }
            if (StringUtils.isBlank(item.getSourceId3())) {
                errorMsgs.add("sourceId3不能为空");
            }
            if (StringUtils.isBlank(item.getStatus())) {
                errorMsgs.add("status不能为空");
            }
        }
        return errorMsgs;
    }


    @Override
    public PassResponse receiveGcebReceiptClaim(GcebReceiptClaim gcebReceiptClaim) {
        logger.info("收款认领 start..");
        PassResponse response = new PassResponse();
        //数据非空校验
        List<String> errorMsgList = validate(gcebReceiptClaim);
        if (CollectionUtils.isNotEmpty(errorMsgList)) {
            response.setResponseCode("000001");
            response.setResponseMessage(String.join("\n", errorMsgList));
            return response;
        }
        String responseCode = "000000";
        String responseMessage = "收款认领 Success";
        try {
            List<GcebReceiptClaim.ItemDetail> details = gcebReceiptClaim.getItemDetail();
            if (CollectionUtils.isNotEmpty(details)) {
                List<ReceiptClaimDto> receiptClaimList = new ArrayList<>();
                //解析并校验
                for (GcebReceiptClaim.ItemDetail detail : details) {
                    ReceiptClaimDto receiptClaim = new ReceiptClaimDto();
                    receiptClaim.setSourceSystemCode(gcebReceiptClaim.getSourceSystemCode());
                    receiptClaim.setCashReceiptCode(detail.getReceiptCode());
                    receiptClaim.setPayName(detail.getPayName());
                    receiptClaim.setPayBankCode(detail.getPayBankCode());
                    receiptClaim.setPayBankName(detail.getPayBankName());
                    receiptClaim.setBillCode(detail.getBillCode());
                    receiptClaim.setPayAmount(detail.getPayAmount());
                    receiptClaim.setCurrencyCode(detail.getCurrencyCode());
                    receiptClaim.setBillType(detail.getBillType());
                    receiptClaim.setSettleWay(detail.getSettlteWay());
                    receiptClaim.setRecMethod(detail.getRecMethod());
                    receiptClaim.setRecBankId(PamStringUtil.toLongValue(detail.getRecBankId()));
                    receiptClaim.setRecBankCode(detail.getRecBankCode());
                    receiptClaim.setRecAccountNo(detail.getRecAccountNo());
                    receiptClaim.setRecOrgName(detail.getRecOrgName());
                    receiptClaim.setRecOrgCode(detail.getRecOrgCode());
                    receiptClaim.setBudgetItemCode(detail.getBudget());
                    receiptClaim.setCashStatus(1);// todo 资金反馈有1和3，但他们不传了
                    receiptClaim.setOuId(Long.valueOf(detail.getOuId()));
                    receiptClaim.setPayDate(DateUtils.parse(detail.getPayDate(), DateUtils.FORMAT_SHORT));
                    receiptClaim.setRemark(detail.getRemark());
                    receiptClaim.setConntransCode(detail.getConntransCode());
                    receiptClaim.setSerialNumber(detail.getSerialNumber());

                    if (!ObjectUtils.isEmpty(gcebToPamHelper.findByUniqueKey(receiptClaim))) {
                        //此处可能会覆盖
                        responseMessage = receiptClaim.getCashReceiptCode() + "资金号在PAM已存在!";
                        continue;
                    }
                    if (StringUtils.isNotBlank(detail.getCrmCustomerCode())) {
                        CustomerDto customer = gcebToPamHelper.getCustomerIdByCrmCode(detail.getCrmCustomerCode());
                        if (null == customer) {
                            responseCode = "000001";
                            responseMessage = detail.getReceiptCode() + ":" + detail.getCrmCustomerCode() + ",客户在PAM不存在!";
                            logger.info(responseMessage);
                            break;
                        } else {
                            receiptClaim.setCustomerId(customer.getId());
                            receiptClaim.setCrmCustomerCode(detail.getCrmCustomerCode());
                            receiptClaim.setCrmCustomerName(detail.getCrmCustomerName());
                        }
                    }
                    //启用ou过滤
                    OperatingUnit ou = CacheDataUtils.findOuById(receiptClaim.getOuId());
                    if (ObjectUtils.isEmpty(ou) || ObjectUtils.isEmpty(ou.getPamEnabled()) || !"0".equals(String.valueOf(ou.getPamEnabled()))) {
                        responseCode = "000001";
                        responseMessage = detail.getReceiptCode() + ":" + receiptClaim.getOuId() + ",ou在PAM未启用!";
                        logger.info(responseMessage);
                        break;
                    }

                    receiptClaimList.add(receiptClaim);
                }
                //保存数据
                gcebToPamHelper.saveFromGceb(receiptClaimList);
            }

        } catch (Exception ex) {
            logger.error("收款认领异常：", ex);
            responseCode = "000001";
            responseMessage = ex.getStackTrace()[0].getClassName() + "/" + ex.getStackTrace()[0].getMethodName() + "/" + ex.getStackTrace()[0].getLineNumber()
                    + "/" + ex.getMessage();
        }
        response.setResponseCode(responseCode);
        response.setResponseMessage(responseMessage);
        return response;
    }


    /**
     * 必填校验
     */
    private List<String> validate(GcebReceiptClaim gcebReceiptClaim) {
        List<String> errorMsgs = new ArrayList<>();

        if (StringUtils.isBlank(gcebReceiptClaim.getSourceSystemCode())) {
            errorMsgs.add("来源系统名称不能为空");
        }
        if (ListUtils.isEmpty(gcebReceiptClaim.getItemDetail())) {
            errorMsgs.add("交易明细数据不能为空");
        }
        for (GcebReceiptClaim.ItemDetail item : gcebReceiptClaim.getItemDetail()) {
            if (ObjectUtils.isEmpty(item.getReceiptCode())) {
                errorMsgs.add("资金流水号不能为空");
            }
            if (ObjectUtils.isEmpty(item.getPayAmount())) {
                errorMsgs.add("payAmount不能为空");
            }
            if (ObjectUtils.isEmpty(item.getCurrencyCode())) {
                errorMsgs.add("币种编码不能为空");
            }
            if (ObjectUtils.isEmpty(item.getSettlteWay())) {
                errorMsgs.add("结算方式不能为空");
            }
            if (ObjectUtils.isEmpty(item.getRecMethod())) {
                errorMsgs.add("收款方式编码不能为空");
            }
            if (ObjectUtils.isEmpty(item.getRecBankId())) {
                errorMsgs.add("收款方银行账号ID不能为空");
            }
            if (ObjectUtils.isEmpty(item.getRecBankCode())) {
                errorMsgs.add("收款方银行帐号不能为空");
            }
            if (ObjectUtils.isEmpty(item.getRecAccountNo())) {
                errorMsgs.add("收款账户号码不能为空");
            }
            if (ObjectUtils.isEmpty(item.getBudget())) {
                errorMsgs.add("预算科目编码不能为空");
            }
            if (ObjectUtils.isEmpty(item.getOuId())) {
                errorMsgs.add("ouId不能为空");
            }
            if (ObjectUtils.isEmpty(item.getPayDate())) {
                errorMsgs.add("付款日期不能为空");
            }
        }
        return errorMsgs;
    }

}
