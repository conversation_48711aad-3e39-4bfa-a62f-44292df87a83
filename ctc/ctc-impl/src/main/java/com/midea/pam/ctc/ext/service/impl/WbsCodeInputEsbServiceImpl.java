package com.midea.pam.ctc.ext.service.impl;

import com.midea.pam.common.base.EsbResponse;
import com.midea.pam.common.basedata.dto.OrganizationRelDto;
import com.midea.pam.common.basedata.query.OrganizationRelQuery;
import com.midea.pam.common.crm.dto.BusinessDto;
import com.midea.pam.common.crm.dto.CommonByErpDto;
import com.midea.pam.common.crm.dto.LeadDto;
import com.midea.pam.common.ctc.entity.*;
import com.midea.pam.common.enums.ResponseCodeEnums;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.ctc.mapper.ProjectMapper;
import com.midea.pam.ctc.mapper.ProjectWbsBudgetMapper;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.CrmExtService;
import com.midea.pam.ctc.service.EsbService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

public class WbsCodeInputEsbServiceImpl extends AbstractCommonBusinessService {

    @Resource
    private CrmExtService crmExtService;
    @Resource
    private EsbService esbService;
    @Resource
    private BasedataExtService basedataExtService;
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private ProjectWbsBudgetMapper projectWbsBudgetMapper;

    @Override
    public EsbResponse execute(ResendExecute resendExecute) {
        List<CommonByErpDto> dtoList = new ArrayList<>();
        CommonByErpDto erpDto = new CommonByErpDto();
        if ("lead".equals(resendExecute.getSubApplyNo())) {
            LeadDto dto = crmExtService.getLeadById(Long.valueOf(resendExecute.getApplyNo()));
            erpDto.setId(dto.getId());
            erpDto.setWbsCode(dto.getNum());//线索号
            erpDto.setInstructions("PAM线索-"+dto.getNum());//WBS说明
            erpDto.setWbsType("SO-SERVICE");//类别
            erpDto.setProfitCenter(dto.getProfitCenter());//利润中心
            erpDto.setProductGroup(dto.getProductCenter());//产品组
            erpDto.setPercent(String.valueOf(100));//百分比
            if (dto.getOperatingUnitId() != null) {
                OrganizationRelQuery query = new OrganizationRelQuery();
                query.setOperatingUnitId(dto.getOperatingUnitId());
                List<OrganizationRelDto> organizationRelDtos = basedataExtService.getOrganizationRel(query);
                if (ListUtils.isNotEmpty(organizationRelDtos)) {
                    erpDto.setLedgerId(organizationRelDtos.get(0).getLedgerId());
                }
                erpDto.setOuId(dto.getOperatingUnitId());
            }
            erpDto.setStartDate(DateUtils.formatDate(dto.getCreateAt()));//有效日期从
            GregorianCalendar calendar = new GregorianCalendar();
            calendar.setTime(new Date());
            if (dto.getStatus() == 0) {
                calendar.add(Calendar.DAY_OF_MONTH, -1);
            } else {
                calendar.add(Calendar.YEAR, 10);//有效期10年后
            }
            erpDto.setEndDate(DateUtils.formatDate(calendar.getTime()));//有效日期至
            dtoList.add(erpDto);
        } else if ("business".equals(resendExecute.getSubApplyNo())) {
            BusinessDto dto = crmExtService.getBusinessById(Long.valueOf(resendExecute.getApplyNo()));
            erpDto.setId(dto.getId());
            erpDto.setWbsCode(dto.getBusinessCode());//商机编号
            erpDto.setInstructions(dto.getName());//商机名称
            erpDto.setWbsType("SO-SERVICE");//类别
            erpDto.setProfitCenter(dto.getProfitCenter());//利润中心
            erpDto.setProductGroup(dto.getProductCenter());//产品组
            erpDto.setPercent(String.valueOf(100));//百分比
            if (dto.getOperatingUnitId() != null) {
                OrganizationRelQuery query = new OrganizationRelQuery();
                query.setOperatingUnitId(dto.getOperatingUnitId());
                List<OrganizationRelDto> organizationRelDtos = basedataExtService.getOrganizationRel(query);
                if (ListUtils.isNotEmpty(organizationRelDtos)) {
                    erpDto.setLedgerId(organizationRelDtos.get(0).getLedgerId());
                }
                erpDto.setOuId(dto.getOperatingUnitId());
            }
            erpDto.setStartDate(DateUtils.formatDate(dto.getCreateAt()));//有效日期从
            GregorianCalendar calendar = new GregorianCalendar();
            calendar.setTime(new Date());
            if (dto.getStatus() == 0 || dto.getStatus() == 3) {
                calendar.add(Calendar.DAY_OF_MONTH, -1);
            } else {
                calendar.add(Calendar.YEAR, 10);//有效期10年后
            }
            erpDto.setEndDate(DateUtils.formatDate(calendar.getTime()));//有效日期至
            dtoList.add(erpDto);
        }else if ("project".equals(resendExecute.getSubApplyNo())) {
            Project project = projectMapper.selectByPrimaryKey(Long.valueOf(resendExecute.getApplyNo()));
            // 如果是wbs项目，遍历project_wbs_budget传全量wbs记录
            if(Boolean.TRUE.equals(project.getWbsEnabled())){
                ProjectWbsBudgetExample example = new ProjectWbsBudgetExample();
                example.createCriteria().andDeletedFlagEqualTo(false).andProjectIdEqualTo(project.getId());
                List<ProjectWbsBudget> wbsBudgetList = projectWbsBudgetMapper.selectByExample(example);
                if(CollectionUtils.isNotEmpty(wbsBudgetList)){
                    GregorianCalendar calendar = new GregorianCalendar();
                    calendar.setTime(new Date());
                    if (project.getStatus() == 0 || project.getStatus() == 3) {
                        calendar.add(Calendar.DAY_OF_MONTH, -1);
                    } else {
                        calendar.add(Calendar.YEAR, 10);//有效期10年后
                    }
                    Long ledgerId = null;
                    Long ouId = null;
                    if (project.getOuId() != null) {
                        OrganizationRelQuery query = new OrganizationRelQuery();
                        query.setOperatingUnitId(project.getOuId());
                        List<OrganizationRelDto> organizationRelDtos = basedataExtService.getOrganizationRel(query);
                        if (ListUtils.isNotEmpty(organizationRelDtos)) {
                            ledgerId = organizationRelDtos.get(0).getLedgerId();
                        }
                        ouId = project.getOuId();
                    }
                    // 将wbsFullCode分组推送
                    Map<String, List<ProjectWbsBudget>> wbsMap = wbsBudgetList.stream().collect(Collectors.groupingBy(a->a.getWbsFullCode()));
                    for(String wbsFullCode : wbsMap.keySet()){
                        List<ProjectWbsBudget> wbsList = wbsMap.get(wbsFullCode);
                        erpDto = new CommonByErpDto();
                        erpDto.setId(wbsList.get(0).getId());
                        erpDto.setWbsCode(project.getCode() + "-" + wbsFullCode);//项目号(projectCode拼wbs号)
                        erpDto.setInstructions(wbsList.get(0).getDescription()); //项目名称（wbs描述）
                        erpDto.setWbsType("SO-SERVICE");                         //类别
                        if(StringUtils.isNotBlank(project.getProfitCenter())){
                            erpDto.setProfitCenter(project.getProfitCenter());   //利润中心
                        }
                        if(StringUtils.isNotBlank(project.getProductCenter())){
                            erpDto.setProductGroup(project.getProductCenter());  //产品组
                        }
                        erpDto.setPercent(String.valueOf(100));                  //百分比
                        if(null != ledgerId){
                            erpDto.setLedgerId(ledgerId);
                        }
                        if(null != ouId){
                            erpDto.setOuId(ouId);
                        }
                        erpDto.setStartDate(DateUtils.formatDate(project.getCreateAt()));//有效日期从
                        erpDto.setEndDate(DateUtils.formatDate(calendar.getTime()));//有效日期至
                        dtoList.add(erpDto);
                    }
                }
            }else{
                erpDto.setId(project.getId());
                erpDto.setWbsCode(project.getCode());//项目号
                erpDto.setInstructions(project.getName());//项目名称
                erpDto.setWbsType("SO-SERVICE");//类别
                erpDto.setProfitCenter(project.getProfitCenter());//利润中心
                erpDto.setProductGroup(project.getProductCenter());//产品组
                erpDto.setPercent(String.valueOf(100));//百分比
                if (project.getOuId() != null) {
                    OrganizationRelQuery query = new OrganizationRelQuery();
                    query.setOperatingUnitId(project.getOuId());
                    List<OrganizationRelDto> organizationRelDtos = basedataExtService.getOrganizationRel(query);
                    if (ListUtils.isNotEmpty(organizationRelDtos)) {
                        erpDto.setLedgerId(organizationRelDtos.get(0).getLedgerId());
                    }
                    erpDto.setOuId(project.getOuId());
                }
                erpDto.setStartDate(DateUtils.formatDate(project.getCreateAt()));//有效日期从
                GregorianCalendar calendar = new GregorianCalendar();
                calendar.setTime(new Date());
                if (project.getStatus() == 0 || project.getStatus() == 3) {
                    calendar.add(Calendar.DAY_OF_MONTH, -1);
                } else {
                    calendar.add(Calendar.YEAR, 10);//有效期10年后
                }
                erpDto.setEndDate(DateUtils.formatDate(calendar.getTime()));//有效日期至
                dtoList.add(erpDto);
            }
        }
        if (CollectionUtils.isEmpty(dtoList)) {
            // 匹配不到记录
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), "关联记录为空，subApplyNo=" + resendExecute.getSubApplyNo() + ",applyNo=" + resendExecute.getApplyNo(), "99");
        }
        return esbService.callCUXESBCOMMONINAPIPKGPortType(dtoList);
    }
}
