package com.midea.pam.ctc.ext.service.impl;

import com.midea.mcomponent.core.util.Assert;
import com.midea.pam.common.base.EsbResponse;
import com.midea.pam.common.ctc.dto.ReceiptClaimContractRelDto;
import com.midea.pam.common.ctc.dto.ReceiptClaimDto;
import com.midea.pam.common.ctc.entity.ReceiptClaimDetail;
import com.midea.pam.common.ctc.entity.ResendExecute;
import com.midea.pam.common.enums.CommonStatus;
import com.midea.pam.common.enums.ResponseCodeEnums;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.ctc.common.enums.CommonErpStatus;
import com.midea.pam.ctc.common.enums.ReceiptClaimEnum;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.ListUtil;
import com.midea.pam.ctc.mapper.ReceiptClaimDetailMapper;
import com.midea.pam.ctc.mapper.ReceiptClaimExtMapper;
import com.midea.pam.ctc.service.EsbService;
import com.midea.pam.ctc.service.SdpService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * @program: pam
 * @description: ReceiptClaimEsbServiceImpl
 * @author: gaojh1
 * @create: 2019-3-12 10:51
 **/
public class ReceiptClaimEsbServiceImpl extends AbstractCommonBusinessService {

    private static final Logger logger = LoggerFactory.getLogger(ReceiptClaimEsbServiceImpl.class);

    @Resource
    private SdpService sdpService;
    @Resource
    ReceiptClaimExtMapper receiptClaimExtMapper;
    @Resource
    ReceiptClaimDetailMapper receiptClaimDetailMapper;


    @Override
    public EsbResponse execute(ResendExecute resendExecute) {
        return null;
    }

    /**
     * 执行批量发送报文.
     *
     * @param resendExecutes
     */
    @Override
    public EsbResponse execute(List resendExecutes) {
        List<ResendExecute> list = BeanConverter.copy(resendExecutes, ResendExecute.class);
        List<Long> ids = ListUtil.map(list, "applyNo");
        ReceiptClaimDto paramDto = new ReceiptClaimDto();
        paramDto.setIdList(ids);
        List<ReceiptClaimDto> receiptClaimDtoList = receiptClaimExtMapper.queryDetailList(paramDto);
        // 查询子合同编号
        List<ReceiptClaimContractRelDto> claimContractRelDtoList = receiptClaimExtMapper.queryReceiptClaimContractRel(paramDto);
        Map<Long, String> claimContractRelMap = new HashMap<>();
        if (ListUtils.isNotEmpty(claimContractRelDtoList)) {
            claimContractRelMap = claimContractRelDtoList
                    .stream()
                    .collect(Collectors.toMap(ReceiptClaimContractRelDto::getReceiptClaimDetailId, ReceiptClaimContractRelDto::getContractCode));
        }
        for (ReceiptClaimDto receiptClaimDto : receiptClaimDtoList) {
            String callingApi = null;
            if (ReceiptClaimEnum.SALE.getCode() == receiptClaimDto.getBusinessType()) {
                callingApi = "CREATE_CASH";//标准收款
            } else {
                callingApi = "CREATE_MISC";//杂项收款
            }
            receiptClaimDto.setCallingApi(callingApi);
            receiptClaimDto.setAttribute3(claimContractRelMap.get(receiptClaimDto.getId()));
            // 中转关联号，客户间转款场景下取“转账单据号”
            if (Objects.equals(receiptClaimDto.getSource(), ReceiptClaimEnum.SOURCE_TRANSFER.getCode())) {
                receiptClaimDto.setTransferCode(receiptClaimDto.getCashReceiptCode());
            }
        }
        if (CollectionUtils.isNotEmpty(receiptClaimDtoList)) {
            return sdpService.callErpReceipt(receiptClaimDtoList);
//            return esbService.callCUXARRECEIPTAPIPKGReceiptService(receiptClaimDtoList);
        }
        return new EsbResponse();
    }


    /**
     * 回调.
     *
     * @param resendExecute
     */
    @Override
    public void callback(ResendExecute resendExecute) {
        logger.info("收款写入同步回调开始：{}", resendExecute);
        String applyNo = resendExecute.getApplyNo();
        Assert.notNull(applyNo, "applyNo不能为空");
        //收款认领明细单
        Long receiptClaimDetailId = Long.parseLong(applyNo);
        ReceiptClaimDetail receiptClaimDetail = receiptClaimDetailMapper.selectByPrimaryKey(receiptClaimDetailId);
        if (Objects.nonNull(receiptClaimDetail) && Objects.equals(receiptClaimDetail.getErpStatus(), ReceiptClaimEnum.ERP_ACCOUNTING.getCode())) {
            //更新同步状态
            if (Objects.equals(CommonStatus.ERROR.getCode(), resendExecute.getStatus()) ||
                    Objects.equals(ResponseCodeEnums.FAULT.getCode(), resendExecute.getResponCode())) {
                ReceiptClaimDetail updateEntity = new ReceiptClaimDetail();
                updateEntity.setId(receiptClaimDetail.getId());
                updateEntity.setErpStatus(ReceiptClaimEnum.ERP_ACCOUNT_FAILED.getCode());//记账失败
                String responMsg = resendExecute.getResponMsg();
                if (StringUtils.length(responMsg) > 200) {
                    updateEntity.setErpMessage(responMsg.substring(0, 199));
                } else {
                    updateEntity.setErpMessage(responMsg);
                }
                updateEntity.setContractSyncStatus(CommonErpStatus.PUSH_FAILED.code());
                updateEntity.setContractSyncMessage("");
                receiptClaimDetailMapper.updateByPrimaryKeySelective(updateEntity);
            }
        }
    }

    /**
     * 批量回调.
     *
     * @param resendExecutes
     */
    @Override
    public void callback(List resendExecutes) {
        if (!CollectionUtils.isEmpty(resendExecutes)) {
            List<ResendExecute> list = BeanConverter.copy(resendExecutes, ResendExecute.class);
            for (ResendExecute execute : list) {
                callback(execute);
            }
        }
    }

}
