package com.midea.pam.ctc.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.basedata.dto.OrganizationRelDto;
import com.midea.pam.common.basedata.entity.OperatingUnit;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.basedata.entity.VendorSiteBankForDisplay;
import com.midea.pam.common.basedata.query.OrganizationRelQuery;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.dto.BusiSceneNonSaleDetailDto;
import com.midea.pam.common.ctc.dto.InvoiceReceivableDetailPrintDto;
import com.midea.pam.common.ctc.dto.InvoiceReceivableDto;
import com.midea.pam.common.ctc.dto.MaterialOrderPrintDto;
import com.midea.pam.common.ctc.dto.PrintPDFParam;
import com.midea.pam.common.ctc.dto.PurchaseContractInvoiceDetailsPrintDto;
import com.midea.pam.common.ctc.dto.PurchaseContractPaymentInvoiceDetailDto;
import com.midea.pam.common.ctc.dto.PurchaseContractPunishmentDetailDto;
import com.midea.pam.common.ctc.dto.PurchaseContractPunishmentDetailsPrintDto;
import com.midea.pam.common.ctc.dto.PurchaseContractPunishmentPrintDto;
import com.midea.pam.common.ctc.dto.PurchaseContractTemplateDto;
import com.midea.pam.common.ctc.dto.PurchaseContractTemplatePrintDto;
import com.midea.pam.common.ctc.dto.TicketTasksDTO;
import com.midea.pam.common.ctc.dto.WorkOrderPrintDto;
import com.midea.pam.common.ctc.entity.BusiSceneNonSale;
import com.midea.pam.common.ctc.entity.BusiSceneNonSaleExample;
import com.midea.pam.common.ctc.entity.CloudPrintStatistics;
import com.midea.pam.common.ctc.entity.CloudPrintStatisticsExample;
import com.midea.pam.common.ctc.entity.MaterialGetDetail;
import com.midea.pam.common.ctc.entity.MaterialGetDetailExample;
import com.midea.pam.common.ctc.entity.MaterialGetHeader;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetail;
import com.midea.pam.common.ctc.entity.PaymentInvoice;
import com.midea.pam.common.ctc.entity.PaymentInvoiceDetail;
import com.midea.pam.common.ctc.entity.PaymentInvoiceDetailExample;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.PurchaseContract;
import com.midea.pam.common.ctc.entity.PurchaseContractDetail;
import com.midea.pam.common.ctc.entity.PurchaseContractDetailExample;
import com.midea.pam.common.ctc.entity.PurchaseContractPunishmentDetail;
import com.midea.pam.common.ctc.entity.TicketTasks;
import com.midea.pam.common.ctc.entity.TicketTasksDetail;
import com.midea.pam.common.ctc.vo.PurchaseContractPunishmentVo;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.MaterialGetStatus;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.PurchaseContractStatus;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.ConvertUpMoney;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.enums.PurchaseContractTemplateCode;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.contract.service.PurchaseContractPunishmentService;
import com.midea.pam.ctc.mapper.BusiSceneNonSaleMapper;
import com.midea.pam.ctc.mapper.CloudPrintStatisticsMapper;
import com.midea.pam.ctc.mapper.MaterialGetDetailMapper;
import com.midea.pam.ctc.mapper.MaterialGetHeaderMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailMapper;
import com.midea.pam.ctc.mapper.PaymentInvoiceDetailMapper;
import com.midea.pam.ctc.mapper.PaymentInvoiceMapper;
import com.midea.pam.ctc.mapper.ProjectMapper;
import com.midea.pam.ctc.mapper.PurchaseContractDetailMapper;
import com.midea.pam.ctc.mapper.PurchaseContractMapper;
import com.midea.pam.ctc.mapper.TicketTasksMapper;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.BusiSceneNonSaleService;
import com.midea.pam.ctc.service.CloudPrintExtService;
import com.midea.pam.ctc.service.TicketTasksService;
import com.midea.pam.ctc.service.helper.OrganizationRelHelper;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.system.SystemContext;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.sql.Array;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

import static com.midea.pam.common.util.StringUtils.buildGetUrl;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/16 上午 11:16
 */
public class CloudPrintExtServiceImpl implements CloudPrintExtService {
    private static Logger logger = LoggerFactory.getLogger(CloudPrintExtServiceImpl.class);

    @Resource
    private TicketTasksMapper ticketTasksMapper;
    @Resource
    private MilepostDesignPlanDetailMapper milepostDesignPlanDetailMapper;
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private TicketTasksService ticketTasksService;
    @Resource
    private MaterialGetHeaderMapper headerMapper;
    @Resource
    private MaterialGetDetailMapper detailMapper;
    @Resource
    private CloudPrintStatisticsMapper cloudPrintStatisticsMapper;
    @Resource
    private PurchaseContractMapper purchaseContractMapper;
    @Resource
    private PurchaseContractDetailMapper purchaseContractDetailMapper;
    @Resource
    private OrganizationRelHelper organizationRelHelper;

    @Resource
    private PaymentInvoiceMapper paymentInvoiceMapper;

    @Resource
    private BasedataExtService basedataExtService;

    @Resource
    private PaymentInvoiceDetailMapper paymentInvoiceDetailMapper;

    @Resource
    private BusiSceneNonSaleMapper busiSceneNonSaleMapper;

    @Resource
    private BusiSceneNonSaleService busiSceneNonSaleService;

    @Resource
    private PurchaseContractPunishmentService purchaseContractPunishmentService;

    @Resource
    private RestTemplate restTemplate;

    @Value("${print.urlPath}")
    private String urlPath;

    @Value("${print.tenantCode}")
    private String tenantCode;

    @Value("${print.userCode}")
    private String userCode;

    @Value("${business.kukaRouxingUnitId}")
    private String kukaRouxingUnitId;


    @Override
    public PrintPDFParam exeWorkOrderPrint(Long ticketTasksId) throws IOException {
        //url
        //获取token
        String printCode = "WorkOrderPrint";
        JSONObject tokenApi = this.getTokenFromJson(printCode);
        Object timeStamp = tokenApi.get("timeStamp");
        Object token = tokenApi.get("token");
        Object pdfUrl = tokenApi.get("pdfUrl");
        Object remark = tokenApi.get("remark");

        String strTicketTasksId = ticketTasksId.toString();
        writePrintNum(strTicketTasksId);

        List<WorkOrderPrintDto> resultUnit = new ArrayList<>();
        TicketTasks ticketTasksInfo = ticketTasksMapper.selectByPrimaryKey(ticketTasksId);
        Long projectId = null;
        Project project = null;
        WorkOrderPrintDto workOrderPrintDto = new WorkOrderPrintDto();
        if (ticketTasksInfo != null) {
            projectId = ticketTasksInfo.getProjectId();
            if (projectId != null) {
                project = projectMapper.selectByPrimaryKey(projectId);
            }
            workOrderPrintDto.setORDER_TASK_CODE(ticketTasksInfo.getTicketTaskCode());//工单任务号
            workOrderPrintDto.setASSEMBLY_DES(ticketTasksInfo.getTheassemblyDes());//装配件描述
            workOrderPrintDto.setASSEMBLY_CODE(ticketTasksInfo.getTheassemblyCode());//装配件编号
            if (ticketTasksInfo.getNumber() != null) {
                workOrderPrintDto.setASSEMBLY_TOTAL(ticketTasksInfo.getNumber().toString());//装配需求总数
            }
            workOrderPrintDto.setMODULE_ORDER_TASK_CODE(ticketTasksInfo.getModuleCode());//模组工单任务号
            workOrderPrintDto.setMODULE_NAME(ticketTasksInfo.getModuleName());//模组名称
        }
        if (project != null) {
            workOrderPrintDto.setPROJECT_CODE(project.getCode());//项目编号
            workOrderPrintDto.setPROJECT_NAME(project.getName());//项目名称
        } else {
            workOrderPrintDto.setPROJECT_NAME("");
            workOrderPrintDto.setPROJECT_CODE("");
        }
        String userName = SystemContext.getUserName();
        workOrderPrintDto.setPRINT_BY(userName);//打印人
        //获取打印次数
        CloudPrintStatisticsExample example = new CloudPrintStatisticsExample();
        CloudPrintStatisticsExample.Criteria criteria = example.createCriteria();
        criteria.andPrintIdsEqualTo(ticketTasksId.toString());
        List<CloudPrintStatistics> cloudPrintStatisticsList = cloudPrintStatisticsMapper.selectByExample(example);
        Long printNum = cloudPrintStatisticsList.get(0).getPrintNum();
        workOrderPrintDto.setPRINT_NUM(printNum.toString());
        List<TicketTasksDetail> milepostDesignPlanDetailDto = ticketTasksService.getMilepostPlanDesignByTicketTasksId(ticketTasksId);
        Long index = 0L;
        if (ListUtils.isNotEmpty(milepostDesignPlanDetailDto)) {
            for (TicketTasksDetail dto : milepostDesignPlanDetailDto) {
                WorkOrderPrintDto workOrderPrintDto1 = BeanConverter.copy(workOrderPrintDto, WorkOrderPrintDto.class);
                index++;
                workOrderPrintDto1.setINDEX(index.toString());//序号
                workOrderPrintDto1.setPAM_CODE(dto.getPamCode());//PAM物料编码
                workOrderPrintDto1.setERP_CODE(dto.getErpCode());//ERP物料编码
                //查找上层PAM物料编码
                Long parentId = dto.getParentId();
                MilepostDesignPlanDetail InfoByParentId = milepostDesignPlanDetailMapper.selectByPrimaryKey(parentId);
                if (InfoByParentId != null) {
                    workOrderPrintDto1.setTOP_PAM_CODE(InfoByParentId.getPamCode());//上层PAM物料编码
                }
                workOrderPrintDto1.setMATERIAL_DES(dto.getMaterielDescr());//物料描述
                workOrderPrintDto1.setTYPE(dto.getMaterialCategory());//类型
                workOrderPrintDto1.setUNIT(dto.getUnit());//单位
                if (ticketTasksInfo.getNumber() != null && dto.getNumber() != null) {
                    workOrderPrintDto1.setPER_NUM(dto.getNumber().toString());//单套数量
                    Long l = ticketTasksInfo.getNumber().longValue() * dto.getNumber().longValue();
                    workOrderPrintDto1.setASSEMBLY_L_TOTAL(l.toString());//装配需求总数
                }
                resultUnit.add(workOrderPrintDto1);
            }
        } else {
            resultUnit.add(workOrderPrintDto);
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data", resultUnit);
        logger.info("resultUnit:{}", resultUnit);
        String s = listToJson(resultUnit);
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("{");
        stringBuffer.append("\"");
        stringBuffer.append("data");
        stringBuffer.append("\"");
        stringBuffer.append(" : ");
        stringBuffer.append(s);
        stringBuffer.append("}");
        logger.info("结果集为：{}", stringBuffer.toString());
        //String s1 = doPost(pdfUrl.toString(), stringBuffer.toString());
        PrintPDFParam printPDFParam = new PrintPDFParam();
        printPDFParam.setJson(stringBuffer.toString());
        printPDFParam.setUrl(pdfUrl.toString());
        return printPDFParam;
    }

    @Override
    public PrintPDFParam exeMaterialOrderPrint(Long materialGetHeaderId) throws IOException {
        MaterialGetHeader materialGetHeaderInfo = headerMapper.selectByPrimaryKey(materialGetHeaderId);
        Asserts.notEmpty(materialGetHeaderInfo.getProjectId(), ErrorCode.CTC_PROJECT_ID_NULL);
        Project project = projectMapper.selectByPrimaryKey(materialGetHeaderInfo.getProjectId());
        Asserts.notEmpty(project, ErrorCode.CTC_PROJECT_NOT_FIND);
        //url
        //获取token
        Long unitId = SystemContext.getUnitId();
        String printCode = null;
        if (unitId == Long.parseLong("814428597413478400")) { //库卡工业自动化（昆山）有限公司
            printCode = "MaterialOrderPrint-KIK";
        } else if (unitId == Long.parseLong("884455544528568320")) { //上海瑞仕格医疗科技有限公司
            printCode = "MaterialOrderPrint-health";
        } else {
            printCode = "MaterialOrderPrint"; //默认模板
        }
        if (project.getWbsEnabled() != null && project.getWbsEnabled()) {
            if (StringUtils.hasText(kukaRouxingUnitId) && Objects.equals(unitId, Long.valueOf(kukaRouxingUnitId))) { //库卡柔性系统（上海）有限公司
                printCode = "MaterialOrderPrintWbs-KIK";
            } else {
                printCode = "MaterialOrderPrintWbs"; //柔性模板
            }
        }
        JSONObject tokenApi = this.getTokenFromJson(printCode);
        Object timeStamp = tokenApi.get("timeStamp");
        Object token = tokenApi.get("token");
        Object pdfUrl = tokenApi.get("pdfUrl");
        Object remark = tokenApi.get("remark");
        List<MaterialOrderPrintDto> resultUnit = new ArrayList<>();
        String getCode = materialGetHeaderInfo.getGetCode();//领料单编号
        String ticketTaskCode = materialGetHeaderInfo.getTicketTaskCode();//工单任务号
        Integer materialGetType = materialGetHeaderInfo.getMaterialGetType();//领料类型
        Date applyTime = materialGetHeaderInfo.getApplyTime();//申请日期
        String projectCode = materialGetHeaderInfo.getProjectCode();//项目编号
        String organizationName = materialGetHeaderInfo.getOrganizationName();//库存组织
        String inventoryCode = materialGetHeaderInfo.getInventoryCode();//仓库编号
        String moduleCode = materialGetHeaderInfo.getModuleCode();//模组号
        String moduleName = materialGetHeaderInfo.getModuleName();//模组名称
        String theassemblyDes = materialGetHeaderInfo.getTheassemblyDes();//装配件描述
        String remark1 = materialGetHeaderInfo.getRemark();//备注

        MaterialOrderPrintDto materialOrderPrintDto = new MaterialOrderPrintDto();
        materialOrderPrintDto.setPICKING_REQUISITION_NO(getCode);//领料单号
        materialOrderPrintDto.setORDER_TASK_CODE(ticketTaskCode);//工单任务号
        //领料类型
        if (materialGetType != null) {
            if (materialGetType.equals(0)) {
                materialOrderPrintDto.setPICKING_TYPE("工单领料");
            } else if (materialGetType.equals(1)) {
                materialOrderPrintDto.setPICKING_TYPE("项目领料");
            } else if (materialGetType.equals(2)) {
                materialOrderPrintDto.setPICKING_TYPE("项目领料（WBS）");
            }
        } else {
            materialOrderPrintDto.setPICKING_TYPE(null);
        }
        materialOrderPrintDto.setAPPLY_DATE(applyTime.toString());//申请日期
        materialOrderPrintDto.setPROJECT_CODE(projectCode);//项目编号
        materialOrderPrintDto.setINVENTORY_ORG(organizationName);//库存组织
        materialOrderPrintDto.setSTORE_CODE(inventoryCode);//仓库编号
        materialOrderPrintDto.setMODULE_CODE(moduleCode);//模组号
        materialOrderPrintDto.setMODULE_NAME(moduleName);//模组名称
        materialOrderPrintDto.setASSEMBLY_DES(theassemblyDes);//装配件描述
        materialOrderPrintDto.setMARK(remark1);//备注
        materialOrderPrintDto.setPICKING_BY(materialGetHeaderInfo.getGetUserName());//领料人
        materialOrderPrintDto.setPRODUCER(materialGetHeaderInfo.getFillUserName());//制单人
        materialOrderPrintDto.setAPPROVED_BY(materialGetHeaderInfo.getAuditingUserName());//审批人
        //获取明细信息
        MaterialGetDetailExample detailsExample = new MaterialGetDetailExample();
        detailsExample.setOrderByClause("material_code asc");
        detailsExample.createCriteria().andHeaderIdEqualTo(materialGetHeaderId).andDeletedFlagEqualTo(0);
        List<MaterialGetDetail> materialGetDetails = detailMapper.selectByExample(detailsExample);
        Long index = 0L;
        if (ListUtils.isNotEmpty(materialGetDetails)) {
            Long orgId = materialGetDetails.get(0).getOrgId();
            if(orgId == null){
                orgId = materialGetHeaderInfo.getOrganizationId();
            }
            Map<String, String> shelvesMap = basedataExtService.getMaterialShelvesByOrgIdAndItemCode(orgId
                    , materialGetDetails.stream().map(MaterialGetDetail::getMaterialCode).collect(Collectors.toList()));
            for (MaterialGetDetail dto : materialGetDetails) {
                MaterialOrderPrintDto materialOrderPrintDto1 = BeanConverter.copy(materialOrderPrintDto, MaterialOrderPrintDto.class);
                index++;
                materialOrderPrintDto1.setINDEX(index.toString());//序号
                materialOrderPrintDto1.setMATERIAL_CODE(dto.getMaterialCode());//物料编号
                materialOrderPrintDto1.setMATERIAL_DES(dto.getMaterialName());//物料描述
                materialOrderPrintDto1.setUNIT(dto.getUnit());//单位

                if(MapUtil.isNotEmpty(shelvesMap) && shelvesMap.containsKey(dto.getMaterialCode())){
                    materialOrderPrintDto1.setSHELVES(shelvesMap.get(dto.getMaterialCode()));
                }

                if (dto.getApplyAmount() != null) {
                    materialOrderPrintDto1.setAPPLY_NUM(dto.getApplyAmount().stripTrailingZeros().toPlainString());//申请数量
                }
                if (dto.getActualAmount() != null && !Objects.equals(materialGetHeaderInfo.getStatus(), MaterialGetStatus.PENDING.code())) {
                    materialOrderPrintDto1.setNUM(dto.getActualAmount().stripTrailingZeros().toPlainString());//实发数量
                }
                materialOrderPrintDto1.setMARK2(dto.getRemark());//备注

                materialOrderPrintDto1.setPICKING_SHELVES(dto.getLocationCode());//领料货位

                materialOrderPrintDto1.setWBS_SUMMARY_CODE(dto.getWbsSummaryCode());//WBS
                materialOrderPrintDto1.setWBS_DESCRIPTION(dto.getWbsDescription());//WBS描述

                resultUnit.add(materialOrderPrintDto1);
            }
        } else {
            resultUnit.add(materialOrderPrintDto);
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data", resultUnit);
        logger.info("resultUnit:{}", resultUnit);
        String s = listToJson(resultUnit);
        s = s.replaceAll("\\s+", " ");
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("{");
        stringBuffer.append("\"");
        stringBuffer.append("data");
        stringBuffer.append("\"");
        stringBuffer.append(" : ");
        stringBuffer.append(s);
        stringBuffer.append("}");
        logger.info("结果集为：{}", stringBuffer.toString());
        //String s1 = doPost(pdfUrl.toString(), stringBuffer.toString());
        PrintPDFParam printPDFParam = new PrintPDFParam();
        printPDFParam.setJson(stringBuffer.toString());
        printPDFParam.setUrl(pdfUrl.toString());
        return printPDFParam;
    }

    public List<TicketTasksDTO> setList(List<TicketTasksDTO> list, List<TicketTasksDTO> resultUnit) {
        for (TicketTasksDTO l : list) {
            resultUnit.add(l);
            List<TicketTasksDTO> dtos = l.getChildrenTicketTasksDTOS();
            if (ListUtils.isNotEmpty(dtos)) {
                setList(dtos, resultUnit);
            }
        }
        return resultUnit;
    }

    @Override
    public PrintPDFParam exeManyWorkOrderPrint(String ticketTasksIds) throws IOException {
        //url
        //获取token
        String printCode = "mWorkOrderPrint";
        JSONObject tokenApi = this.getTokenFromJson(printCode);
        Object timeStamp = tokenApi.get("timeStamp");
        Object token = tokenApi.get("token");
        Object pdfUrl = tokenApi.get("pdfUrl");
        Object remark = tokenApi.get("remark");
        String[] ids = ticketTasksIds.split(",");

        writePrintNum(ticketTasksIds);

        List<WorkOrderPrintDto> resultUnit = new ArrayList<>();
        /*List<TicketTasksDTO> ticketTasksDTOS = ticketTasksService.selectTreeByProjectId(projectId);
        List<TicketTasksDTO> dtoss = new ArrayList<>();
        for (TicketTasksDTO dto : ticketTasksDTOS) {
            dtoss.add(dto);
            List<TicketTasksDTO> list = dto.getChildrenTicketTasksDTOS();
            if (ListUtils.isNotEmpty(list)) {
                setList(list, dtoss);
            }
        }*/
        for (int i = 0; i < ids.length; i++) {
            WorkOrderPrintDto workOrderPrintDto = new WorkOrderPrintDto();
            Long id = Long.parseLong(ids[i]);
            TicketTasks ticketTasksInfo = ticketTasksMapper.selectByPrimaryKey(id);
            if (ticketTasksInfo != null) {
                workOrderPrintDto.setORDER_TASK_CODE(ticketTasksInfo.getTicketTaskCode());//工单任务号
                workOrderPrintDto.setASSEMBLY_DES(ticketTasksInfo.getTheassemblyDes());//装配件描述
                workOrderPrintDto.setASSEMBLY_CODE(ticketTasksInfo.getTheassemblyCode());//装配件编号
                if (ticketTasksInfo.getNumber() != null) {
                    workOrderPrintDto.setASSEMBLY_TOTAL(ticketTasksInfo.getNumber().toString());//装配需求总数
                } else {
                    workOrderPrintDto.setASSEMBLY_TOTAL("");//装配需求总数
                }
                workOrderPrintDto.setMODULE_ORDER_TASK_CODE(ticketTasksInfo.getModuleCode());//模组工单任务号workOrderPrintDto.setMODULE_NAME(ticketTasksInfo.getModuleName());//模组名称
                workOrderPrintDto.setMODULE_NAME(ticketTasksInfo.getModuleName());//模组名称
                Long projectId = ticketTasksInfo.getProjectId();
                Project project = null;
                if (projectId != null) {
                    project = projectMapper.selectByPrimaryKey(projectId);
                    if (project != null) {
                        workOrderPrintDto.setPROJECT_NAME(project.getName());//项目名称
                        workOrderPrintDto.setPROJECT_CODE(project.getCode());//项目编号
                    } else {
                        workOrderPrintDto.setPROJECT_NAME("");
                        workOrderPrintDto.setPROJECT_CODE("");
                    }
                } else {
                    workOrderPrintDto.setPROJECT_NAME("");
                    workOrderPrintDto.setPROJECT_CODE("");
                }
            }
            String userName = SystemContext.getUserName();
            workOrderPrintDto.setPRINT_BY(userName);//打印人
            //获取打印次数
            CloudPrintStatisticsExample example = new CloudPrintStatisticsExample();
            CloudPrintStatisticsExample.Criteria criteria = example.createCriteria();
            criteria.andPrintIdsEqualTo(ticketTasksIds);
            List<CloudPrintStatistics> cloudPrintStatisticsList = cloudPrintStatisticsMapper.selectByExample(example);
            Long printNum = cloudPrintStatisticsList.get(0).getPrintNum();
            workOrderPrintDto.setPRINT_NUM(printNum.toString());

            List<TicketTasksDetail> milepostDesignPlanDetailDto = ticketTasksService.getMilepostPlanDesignByTicketTasksId(id);
            Long index = 0L;
            List<WorkOrderPrintDto> resultDetail = new ArrayList<>();
            if (ListUtils.isNotEmpty(milepostDesignPlanDetailDto)) {
                for (TicketTasksDetail dtos : milepostDesignPlanDetailDto) {
                    WorkOrderPrintDto workOrderPrintDto1 = new WorkOrderPrintDto();
                    index++;
                    workOrderPrintDto1.setINDEX(index.toString());//序号
                    workOrderPrintDto1.setPAM_CODE(dtos.getPamCode());//PAM物料编码
                    workOrderPrintDto1.setERP_CODE(dtos.getErpCode());//ERP物料编码
                    //查找上层PAM物料编码
                    Long parentId = dtos.getParentId();
                    MilepostDesignPlanDetail InfoByParentId = milepostDesignPlanDetailMapper.selectByPrimaryKey(parentId);
                    if (InfoByParentId != null) {
                        workOrderPrintDto1.setTOP_PAM_CODE(InfoByParentId.getPamCode());
                    }
                    workOrderPrintDto1.setMATERIAL_DES(dtos.getMaterielDescr());//物料描述
                    workOrderPrintDto1.setTYPE(dtos.getMaterialCategory());//类型
                    workOrderPrintDto1.setUNIT(dtos.getUnit());//单位
                    if (dtos.getNumber() != null && ticketTasksInfo.getNumber() != null) {
                        workOrderPrintDto1.setPER_NUM(dtos.getNumber().toString());//单套数量
                        Long l = dtos.getNumber().longValue() * ticketTasksInfo.getNumber().longValue();
                        workOrderPrintDto1.setASSEMBLY_L_TOTAL(l.toString());//装配需求总数
                    }
                    resultDetail.add(workOrderPrintDto1);
                }
            }
            if (ListUtils.isNotEmpty(resultDetail)) {
                workOrderPrintDto.setLines(resultDetail);
            } else {
                workOrderPrintDto.setLines(null);
            }
            resultUnit.add(workOrderPrintDto);
        }
        /*if (ListUtils.isNotEmpty(dtoss)) {
            for (TicketTasksDTO dto : dtoss) {


                Long id = dto.getId();//工单任务Id

            }
        }*/
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data", resultUnit);
        logger.info("resultUnit:{}", resultUnit);
        String s = JSON.toJSONString(resultUnit);
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("{");
        stringBuffer.append("\"");
        stringBuffer.append("data");
        stringBuffer.append("\"");
        stringBuffer.append(" : ");
        stringBuffer.append(s);
        stringBuffer.append("}");
        logger.info("结果集为：{}", stringBuffer.toString());
        //String s1 = doPost(pdfUrl.toString(), stringBuffer.toString());
        PrintPDFParam printPDFParam = new PrintPDFParam();
        printPDFParam.setJson(stringBuffer.toString());
        printPDFParam.setUrl(pdfUrl.toString());
        return printPDFParam;
    }

    @Override
    public PrintPDFParam exePurchaseContractTemplatePrint(PurchaseContractTemplateDto template) {
        Asserts.notEmpty(template.getPurchaseContractId(), ErrorCode.CTC_PURCHASE_CONTRACT_ID_NOT_NULL);
        Asserts.notEmpty(template.getTemplateCode(), ErrorCode.CTC_PURCHASE_CONTRACT_TEMPLATE_CODE_NOT_NULL);
        PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(template.getPurchaseContractId());
        Asserts.notEmpty(purchaseContract, ErrorCode.CTC_PURCHASE_CONTRACT_NOT_EXISTS);
        Project project = projectMapper.selectByPrimaryKey(purchaseContract.getProjectId());
        Asserts.notEmpty(project, ErrorCode.CTC_PROJECT_NOT_FIND);

        //状态校验
        if (!Objects.equals(purchaseContract.getStatus(), PurchaseContractStatus.DRAFT.getCode()) && !Objects.equals(purchaseContract.getStatus(), PurchaseContractStatus.REFUSE.getCode())) {
            throw new ApplicationBizException("只有草稿和驳回状态的合同允许生成合同原件");
        }

        //暂不支持外币
        OrganizationRelQuery organizationRelQuery = new OrganizationRelQuery();
        organizationRelQuery.setOperatingUnitId(purchaseContract.getOuId());
        final List<OrganizationRelDto> organizationRelDtoList = organizationRelHelper.list(organizationRelQuery);
        if (CollectionUtils.isNotEmpty(organizationRelDtoList)) {
            if (!Objects.equals(organizationRelDtoList.get(0).getCurrency(), purchaseContract.getCurrency())) {
                throw new ApplicationBizException("该模板不适用于外币合同");
            }
        }

        //获取token
        String printCode = "PurchaseContractPrint-" + template.getTemplateCode();
        JSONObject tokenApi = this.getTokenFromJson(printCode);
        if (tokenApi == null || StringUtils.isEmpty(tokenApi.getString("pdfUrl"))) {
            throw new ApplicationBizException("调用云打印系统出现异常");
        }
        List<PurchaseContractTemplatePrintDto> resultUnit = new ArrayList<>();
        List<PurchaseContractTemplatePrintDto> lines = new ArrayList<>();
        PurchaseContractTemplatePrintDto printDto = new PurchaseContractTemplatePrintDto();

        /** 合同模板字段 **/
        printDto.setDELIVER_CONTENT(template.getDeliverContent());
        printDto.setPROJECT_CONTENT(template.getProjectContent());
        printDto.setPROJECT_SCHEDULE(template.getProjectSchedule());
        printDto.setREMARK(template.getRemark());
        printDto.setINVOICE_INFO(template.getInvoiceInfo());
        printDto.setSUPPLY_SCOPE(template.getSupplyScope());
        printDto.setPAYMENT_METHOD(template.getPaymentMethod());
        printDto.setACCEPTANCE_HANDOVER(template.getAcceptanceHandover());
        printDto.setWARRANTY(template.getWarranty());
        printDto.setOTHER(template.getOther());
        printDto.setGENERAL_TERMS(template.getGeneralTerms());
        printDto.setDELIVERY_TIME_LOCATION(template.getDeliveryTimeLocation());
        printDto.setACCEPTANCE_WARRANTY(template.getAcceptanceWarranty());
        printDto.setCONTRACT_PERIOD_ATTACHMENT(template.getContractPeriodAttachment());
        printDto.setFRAMEWORK_TERMS(template.getFrameworkTerms());

        //获取合同内容
        PurchaseContractDetailExample detailExample = new PurchaseContractDetailExample();
        detailExample.createCriteria().andContractIdEqualTo(template.getPurchaseContractId()).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        List<PurchaseContractDetail> purchaseContractDetailList = purchaseContractDetailMapper.selectByExample(detailExample);
        Asserts.notEmpty(purchaseContractDetailList, ErrorCode.CTC_PURCHASE_CONTRACT_DETAIL_LIST_NOT_EXISTS);
        Long index = 0L;
        for (int i = 0; i < purchaseContractDetailList.size(); i++) {
            PurchaseContractDetail purchaseContractDetail = purchaseContractDetailList.get(i);
            PurchaseContractTemplatePrintDto printDetailDto = new PurchaseContractTemplatePrintDto();
            index++;
            /** 合同内容字段 **/
            printDetailDto.setINDEX(index.toString());
            printDetailDto.setMODEL(purchaseContractDetail.getModel());
            printDetailDto.setNAME(purchaseContractDetail.getName());
            printDetailDto.setNUMBER(BigDecimalUtils.stripTrailingZerosString(purchaseContractDetail.getNumber()));
            printDetailDto.setUNIT_NAME(purchaseContractDetail.getUnitName());
            BigDecimal noTaxUnitPrice = BigDecimalUtils.divide(purchaseContractDetail.getNoTaxTotalPrice(), purchaseContractDetail.getNumber());
            if (noTaxUnitPrice != null) {
                printDetailDto.setNO_TAX_UNIT_PRICE(ConvertUpMoney.fmtMicrometer(noTaxUnitPrice));
            }
            printDetailDto.setNO_TAX_TOTAL_PRICE(ConvertUpMoney.fmtMicrometer(purchaseContractDetail.getNoTaxTotalPrice()));
            printDetailDto.setTAX_RATE(purchaseContractDetail.getTaxRate());
            printDetailDto.setPROJECT_NAME(project.getCode());
            lines.add(printDetailDto);
            //合并行
            if (i == purchaseContractDetailList.size() - 1) {
                printDetailDto.setNO_TAX_TOTAL_PRICE_SUM(ConvertUpMoney.fmtMicrometer(BigDecimalUtils.scale(purchaseContract.getExcludingTaxAmount())));  //未税总价(汇总)
                String totalPriceSum = String.valueOf(BigDecimalUtils.scale(purchaseContract.getAmount()));
                printDetailDto.setTOTAL_PRICE_SUM(ConvertUpMoney.fmtMicrometer(totalPriceSum));  //含税总价(汇总)
                printDetailDto.setTOTAL_PRICE_SUM_IN_WORDS(ConvertUpMoney.toChinese(totalPriceSum));  //金额大写
                printDetailDto.setREMARK(template.getRemark());  //备注
                printDetailDto.setINVOICE_INFO(template.getInvoiceInfo());  //收票信息
            }
        }
        printDto.setLines(lines);

        /** 基本信息字段 **/
        printDto.setCODE(purchaseContract.getCode());
        printDto.setUNIT(CacheDataUtils.findUnitNameById(SystemContext.getUnitId()));
        printDto.setVENDOR_NAME(Optional.ofNullable(purchaseContract.getVendorName()).map(s -> s.split("-")[0]).orElse(null));
        printDto.setTITLE(buildTitle(template.getTemplateCode(), printDto));

        resultUnit.add(printDto);
        logger.info("resultUnit:{}", resultUnit);
        String s = JSON.toJSONString(resultUnit);
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("{");
        stringBuffer.append("\"");
        stringBuffer.append("header");
        stringBuffer.append("\"");
        stringBuffer.append(" : ");
        stringBuffer.append(s);
        stringBuffer.append("}");
        logger.info("结果集为：{}", stringBuffer.toString());
        PrintPDFParam printPDFParam = new PrintPDFParam();
        printPDFParam.setJson(stringBuffer.toString());
        printPDFParam.setUrl(tokenApi.getString("pdfUrl"));
        return printPDFParam;
    }

    @Override
    public PrintPDFParam exePurchaseContractInvoiceDetailsPrint(Long paymentInvoiceId) throws IOException {
        List<PurchaseContractInvoiceDetailsPrintDto> resultUnit = new ArrayList<>();
        //匹配的云打印编号
        String printCode = "PurchaseContractInvoiceDetails";
        PurchaseContractInvoiceDetailsPrintDto dto = new PurchaseContractInvoiceDetailsPrintDto();
        PaymentInvoice paymentInvoice = paymentInvoiceMapper.selectByPrimaryKey(paymentInvoiceId);
        logger.info("获取的采购合同发票信息为:{}",JSON.toJSONString(paymentInvoice));
        if (paymentInvoice != null && paymentInvoice.getPurchaseContractId() != null) {
            //拼装应付发票基本信息
            dto.setApInvoiceCode(Optional.ofNullable(paymentInvoice.getApInvoiceCode()).orElse(""));
            dto.setInvoiceVouNum(Optional.ofNullable(paymentInvoice.getInvoiceVouNum()).orElse(""));
            BigDecimal totalInvoiceIncludedPrice = Optional.ofNullable(paymentInvoice.getTotalInvoiceIncludedPrice()).orElse(BigDecimal.ZERO);
            dto.setTotalInvoiceIncludedPrice(totalInvoiceIncludedPrice.toPlainString());
            //不含税金额 = 含税金额-税额
            BigDecimal taxAmount = Optional.ofNullable(paymentInvoice.getTaxAmount()).orElse(BigDecimal.ZERO);
            BigDecimal noTaxTotalInvoiceIncludedPrice = totalInvoiceIncludedPrice.subtract(taxAmount).setScale(2);
            dto.setNoTaxTotalInvoiceIncludedPrice(noTaxTotalInvoiceIncludedPrice.toPlainString());
            dto.setGlDate(DateUtils.formatDate(paymentInvoice.getGlDate()));
            dto.setDueDate(DateUtils.formatDate(paymentInvoice.getDueDate()));
            //查询ou名称
            OperatingUnit operatingUnit = CacheDataUtils.findOuById(paymentInvoice.getOuId());
            if (operatingUnit != null) {
                dto.setOuName(operatingUnit.getOperatingUnitName());
            }else {
                dto.setOuName("");
            }
            //创建人
            UserInfo userInfo = CacheDataUtils.findUserById(paymentInvoice.getCreateBy());
            if (userInfo != null) {
                dto.setCreateByName(userInfo.getName());
            }else {
                dto.setCreateByName("");
            }
            dto.setCreateAt(DateUtils.format(paymentInvoice.getCreateAt()));
            Integer erpStatus = paymentInvoice.getErpStatus();
            //ERP同步状态(0验证中/1同步成功/2-已取消/3同步失败/4待同步 )
            String erpStatusStr ;
            if(Objects.isNull(erpStatus)){
                logger.info("ERP同步状态返回为空");
                erpStatusStr = "";
            }else {
                switch (erpStatus){
                    case 0 :
                        erpStatusStr = "验证中";
                        break;
                    case 1 :
                        erpStatusStr = "同步成功";
                        break;
                    case 2 :
                        erpStatusStr = "已取消";
                        break;
                    case 3 :
                        erpStatusStr = "同步失败";
                        break;
                    case 4 :
                        erpStatusStr = "待同步";
                        break;
                    default:
                        erpStatusStr = "";
                        break;
                }

            }

            dto.setErpStatus(erpStatusStr);
            //返回信息长度截取
            String erpMsg = paymentInvoice.getErpMsg();
            if(StringUtils.isNotEmpty(erpMsg) && erpMsg.length() > 50){
                erpMsg = erpMsg.substring(0,50)+" ...";
            }
            dto.setErpMsg(Optional.ofNullable(erpMsg).orElse(""));
            String invoiceEntryExplain = paymentInvoice.getInvoiceEntryExplain();
            if(StringUtils.isNotEmpty(invoiceEntryExplain) && invoiceEntryExplain.length() > 120){
                invoiceEntryExplain = invoiceEntryExplain.substring(0,120)+" ...";
            }
            dto.setInvoiceEntryExplain(Optional.ofNullable(invoiceEntryExplain).orElse(""));
            //ERP取消状态(0未取消/1取消中/2取消失败/3已取消)
            Integer erpCancelStatus = paymentInvoice.getErpCancelStatus();
            String erpCancelStatusStr ;
            if(Objects.isNull(erpCancelStatus)){
                logger.info("ERP同步取消状态为空");
                erpCancelStatusStr = "";
            }else {
                switch (erpCancelStatus){
                    case 0 :
                        erpCancelStatusStr = "未取消";
                        break;
                    case 1 :
                        erpCancelStatusStr = "取消中";
                        break;
                    case 2 :
                        erpCancelStatusStr = "取消失败";
                        break;
                    case 3 :
                        erpCancelStatusStr = "已取消";
                        break;
                    default:
                        erpCancelStatusStr = "";
                        break;
                }

            }
            //6、ERP取消状态为：取消中、取消失败、已取消时，才显示发票取消信息
            if(Objects.equals(1,erpCancelStatus) || Objects.equals(2,erpCancelStatus) || Objects.equals(3,erpCancelStatus) ){
                printCode = "PurchaseContractInvoice_cancel";
            }
            dto.setErpCancelStatus(erpCancelStatusStr);
            String erpCancelMsg = paymentInvoice.getErpCancelMsg();
            if(StringUtils.isNotEmpty(erpCancelMsg) && erpCancelMsg.length() > 50){
                erpCancelMsg = erpCancelMsg.substring(0,50)+" ...";
            }
            dto.setErpCancelMsg(Optional.ofNullable(erpCancelMsg).orElse(""));
            UserInfo cancelUserInfo = CacheDataUtils.findUserById(paymentInvoice.getErpCancelBy());
            //取消人
            if (cancelUserInfo != null) {
                dto.setErpCancelBy(cancelUserInfo.getName());
            }else {
                dto.setErpCancelBy("");
            }
            dto.setErpCancelDate(DateUtils.formatDate(paymentInvoice.getErpCancelDate()));
            String erpCancelCause = paymentInvoice.getErpCancelCause();
            if(StringUtils.isNotEmpty(erpCancelCause) && erpCancelCause.length() > 120){
                erpCancelCause = erpCancelCause.substring(0,120)+" ...";
            }
            dto.setErpCancelCause(Optional.ofNullable(erpCancelCause).orElse(""));
            //查询采购合同信息
            PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(paymentInvoice.getPurchaseContractId());
            logger.info("获取的采购合同信息为:{}",JSON.toJSONString(purchaseContract));
            //查询项目信息
            if (purchaseContract!= null && purchaseContract.getProjectId() != null) {
                dto.setPurchaseContractCode(purchaseContract.getCode());
                dto.setPurchaseContractName(purchaseContract.getName());
                dto.setConversionDate(DateUtils.formatDate(purchaseContract.getConversionDate()));

                if(StringUtils.isNotEmpty(purchaseContract.getConversionType())){
                    dto.setConversionType(purchaseContract.getConversionType());
                }else {
                    dto.setConversionType("");
                }
                BigDecimal conversionRate = purchaseContract.getConversionRate();
                if(Objects.nonNull(conversionRate)){
                    dto.setConversionRate(conversionRate.toPlainString());
                }else {
                    dto.setConversionRate("");
                }
                Project project = projectMapper.selectByPrimaryKey(purchaseContract.getProjectId());
                logger.info("获取的项目信息为:{}",JSON.toJSONString(project));
                if (project != null) {
                    dto.setProjectCode(project.getCode());
                    dto.setProjectName(project.getName());
                }

                //供应商信息
                Long ouId = purchaseContract.getOuId();
                String erpVendorSiteId = purchaseContract.getErpVendorSiteId();
                String vendorCode = purchaseContract.getVendorCode();
                if (StringUtils.isNotEmpty(erpVendorSiteId) && StringUtils.isNotEmpty(vendorCode)) {
                    VendorSiteBankForDisplay vendorSiteBankInfo = basedataExtService.getVendorSiteBankStatus(ouId,
                            vendorCode,
                            erpVendorSiteId);
                    logger.info("获取的供应商信息为:{}",JSON.toJSONString(vendorSiteBankInfo));
                    if (vendorSiteBankInfo != null) {
                        dto.setCurrencyCode(vendorSiteBankInfo.getCurrencyCode());
                        dto.setVendorCode(vendorSiteBankInfo.getVendorCode());
                        dto.setVendorName(vendorSiteBankInfo.getVendorName());
                        dto.setVendorSiteCode(vendorSiteBankInfo.getVendorSiteCode());
                    }
                }
            }

            //采购发票列表
            PaymentInvoiceDetailExample invoiceDetailExample = new PaymentInvoiceDetailExample();
            invoiceDetailExample.createCriteria()
                    .andDeletedFlagEqualTo(Boolean.FALSE)
                    .andPurchaseContractIdEqualTo(paymentInvoice.getPurchaseContractId())
                    .andPaymentInvoiceIdEqualTo(paymentInvoice.getId());
            List<PaymentInvoiceDetail> paymentInvoiceDetailList = paymentInvoiceDetailMapper.selectByExample(invoiceDetailExample);
            logger.info("获取采购发票的列表数据为:{}",paymentInvoiceDetailList.size());
            List<PurchaseContractPaymentInvoiceDetailDto> lines = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(paymentInvoiceDetailList)){

                //非销售业务场景-GL成本结转
//                List<BusiSceneNonSaleDetailDto> detailDtoList = new ArrayList<>();
//                BusiSceneNonSaleExample example = new BusiSceneNonSaleExample();
//                example.createCriteria().andBusiSceneNameEqualTo("采购合同发票入账")
//                        .andOuIdEqualTo(paymentInvoice.getOuId())
//                        .andDeletedFlagEqualTo(Boolean.FALSE);
//                List<BusiSceneNonSale> busiSceneNonSales = busiSceneNonSaleMapper.selectByExample(example);
//                if (ListUtils.isNotEmpty(busiSceneNonSales)) {
//                    Map<String, Object> param = new HashMap();
//                    param.put(Constants.Page.PAGE_NUM.toString(), 1);
//                    param.put(Constants.Page.PAGE_SIZE.toString(), 100);
//                    param.put("busiSceneNonSaleId", String.valueOf(busiSceneNonSales.get(0).getId()));
//                    PageInfo<BusiSceneNonSaleDetailDto> pageInfo = busiSceneNonSaleService.detailPage(param);
//                    detailDtoList.addAll(pageInfo.getList());
//                }

                for (int i = 0; i < paymentInvoiceDetailList.size(); i++) {
                    PaymentInvoiceDetail invoiceDetail = paymentInvoiceDetailList.get(i);
                    PurchaseContractPaymentInvoiceDetailDto detail = new PurchaseContractPaymentInvoiceDetailDto();
                    int index = i+1;
                    detail.setIndex(index+ "");
                    String invoiceType = invoiceDetail.getInvoiceType();
                    if(StringUtils.isNotEmpty(invoiceType)){
                        detail.setInvoiceTypeName(Objects.equals("0",invoiceType) ? "增值税专票" : "增值税普票");
                    }else {
                        //不传值打印文件会显示null
                        detail.setInvoiceTypeName("");
                    }
                    BigDecimal taxIncludedPrice = Optional.ofNullable(invoiceDetail.getTaxIncludedPrice()).orElse(BigDecimal.ZERO);
                    detail.setTaxIncludedPrice(taxIncludedPrice.toPlainString());
                    BigDecimal taxRate = Optional.ofNullable(invoiceDetail.getTaxRate()).orElse(BigDecimal.ZERO);
                    detail.setTaxRate(taxRate.toPlainString());
                    BigDecimal invoiceTaxAmount = Optional.ofNullable(invoiceDetail.getTaxAmount()).orElse(BigDecimal.ZERO);
                    detail.setTaxAmount(invoiceTaxAmount.toString());
                    BigDecimal taxExcludedPrice = Optional.ofNullable(invoiceDetail.getTaxExcludedPrice()).orElse(BigDecimal.ZERO);
                    detail.setTaxExcludedPrice(taxExcludedPrice.toPlainString());
                    detail.setInvoiceDate(DateUtils.formatDate(invoiceDetail.getInvoiceDate()));
                    detail.setDueDateDetail(DateUtils.formatDate(invoiceDetail.getDueDate()));
                    detail.setCurrency(StringUtils.isNotEmpty(invoiceDetail.getCurrency()) ? invoiceDetail.getCurrency() : "");
                    detail.setInvoiceDetailCode(StringUtils.isNotEmpty(invoiceDetail.getInvoiceDetailCode()) ? invoiceDetail.getInvoiceDetailCode() : "");
                    //取落表字段信息，不取配置的
                    if (StringUtils.isNotEmpty(invoiceDetail.getAccountingSubjectProject())){
                        detail.setAccountGroupDebit(invoiceDetail.getAccountingSubjectProject());
                    }else {
                        //不传值打印文件会显示null
                        detail.setAccountGroupDebit("");
                    }

                    lines.add(detail);
                }
                //logger.info("获取的行信息为:{}",JSON.toJSONString(lines));

                dto.setLines(lines);
                //logger.info("获取dto信息为:{}",JSON.toJSONString(dto));
                //添加打印日期
                dto.setPrintDate(DateUtils.format(new Date()));
                resultUnit.add(dto);

            }else {
                logger.info("数据不包含关联发票信息");
                resultUnit.add(dto);
            }
        }





        JSONObject tokenApi = this.getTokenFromJson(printCode);
        if (tokenApi == null || StringUtils.isEmpty(tokenApi.getString("pdfUrl"))) {
            throw new ApplicationBizException("调用云打印系统出现异常");
        }
        Object pdfUrl = tokenApi.get("pdfUrl");
        logger.info("获取pdfUrl为:{}",pdfUrl);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("mainDataSource", resultUnit);
        logger.info("resultUnit:{}", JSON.toJSONString(resultUnit));
        String s = JSON.toJSONString(resultUnit);
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("{");
        stringBuffer.append("\"");
        stringBuffer.append("mainDataSource");
        stringBuffer.append("\"");
        stringBuffer.append(" : ");
        stringBuffer.append(s);
        stringBuffer.append("}");
        logger.info("结果集为：{}", stringBuffer.toString());
        PrintPDFParam printPDFParam = new PrintPDFParam();
        printPDFParam.setJson(stringBuffer.toString());
        printPDFParam.setUrl(pdfUrl.toString());

        return printPDFParam;
    }

    @Override
    public PrintPDFParam exePurchaseContractPunishmentDetailsPrint(Long punishmentId) throws IOException {
        //打印模板编号
        String printCode = "PurchaseContractPunishmentDetail";

        String strTicketTasksId = punishmentId.toString();
        writePrintNum(strTicketTasksId);

        PurchaseContractPunishmentVo punishmentVo = purchaseContractPunishmentService.detail(punishmentId);

        if(Objects.isNull(punishmentVo)){
            throw new BizException(Code.ERROR,"罚扣信息不存在");
        }

        List<PurchaseContractPunishmentDetailDto> punishmentDetails = punishmentVo.getPunishmentDetails();

        PurchaseContractPunishmentPrintDto punishmentPrintDto = new PurchaseContractPunishmentPrintDto();

        //罚扣编号
        punishmentPrintDto.setCode(punishmentVo.getCode());

        //甲方
        if(StringUtils.isNotEmpty(punishmentVo.getOuName())){
            String ouName = punishmentVo.getOuName();
            ouName =  ouName.substring(ouName.lastIndexOf("_") + 1);
            punishmentPrintDto.setUnitName(ouName);
        }


        //乙方
        punishmentPrintDto.setVendorName(punishmentVo.getVendorName());

        //采购合同编号
        punishmentPrintDto.setContractCode(punishmentVo.getContractCode());

        //标题
        punishmentPrintDto.setTitle(punishmentVo.getTitle());

        //罚扣类型
        punishmentPrintDto.setPunishmentType(punishmentVo.getPunishmentType());

        //罚扣金额
        punishmentPrintDto.setAmount(BigDecimalUtils.stripTrailingZerosString(punishmentVo.getAmount()));

        //币种
        punishmentPrintDto.setCurrency(punishmentVo.getCurrency());

        //税率
        punishmentPrintDto.setTaxRate(punishmentVo.getTaxRate());

        //罚扣日期
        punishmentPrintDto.setPunishmentDate(DateUtils.format(punishmentVo.getPunishmentDate(),DateUtils.FORMAT_SHORT));

        //备注
        punishmentPrintDto.setRemark(punishmentVo.getRemark());

        //采购跟进人
        punishmentPrintDto.setContractPurchasingFollowerName(punishmentVo.getContractPurchasingFollowerName());

        //项目编号
        punishmentPrintDto.setProjectCode(punishmentVo.getProjectCode());

        //项目名称
        punishmentPrintDto.setProjectName(punishmentVo.getProjectName());

        //项目经理
        punishmentPrintDto.setContractManagerName(punishmentVo.getContractManagerName());

        if(ListUtils.isNotEmpty(punishmentDetails)) {
            ArrayList<PurchaseContractPunishmentDetailsPrintDto> detailsPrintDtos = new ArrayList<>();
            for (PurchaseContractPunishmentDetail detail : punishmentDetails) {
                PurchaseContractPunishmentDetailsPrintDto detailsPrintDto = new PurchaseContractPunishmentDetailsPrintDto();
                detailsPrintDto.setItem(detail.getItem());
                detailsPrintDto.setPunishmentDate(punishmentPrintDto.getPunishmentDate());
                detailsPrintDto.setAmount(BigDecimalUtils.stripTrailingZerosString(detail.getAmount()));
                detailsPrintDto.setRemark(detail.getRemark());
                detailsPrintDtos.add(detailsPrintDto);
            }
            punishmentPrintDto.setPunishmentDetails(detailsPrintDtos);
            punishmentPrintDto.setDetailTotalCount(detailsPrintDtos.size());
        }

        JSONObject tokenApi = this.getTokenFromJson(printCode);

        if (tokenApi == null || StringUtils.isEmpty(tokenApi.getString("pdfUrl"))) {
            throw new ApplicationBizException("调用云打印系统出现异常");
        }
        Object pdfUrl = tokenApi.get("pdfUrl");
        logger.info("罚扣内容打印，获取pdfUrl为:{}",pdfUrl);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("mainDataSource", punishmentPrintDto);
        logger.info("punishmentInfo:{}", JSON.toJSONString(punishmentPrintDto));
        String s = JSON.toJSONString(punishmentPrintDto);
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("{");
        stringBuffer.append("\"");
        stringBuffer.append("mainDataSource");
        stringBuffer.append("\"");
        stringBuffer.append(" : ");
        stringBuffer.append(s);
        stringBuffer.append("}");
        logger.info("罚扣打印内容：{}", stringBuffer);
        PrintPDFParam printPDFParam = new PrintPDFParam();
        printPDFParam.setJson(stringBuffer.toString());
        printPDFParam.setUrl(pdfUrl.toString());

        return printPDFParam;
    }

    /**
     * 编者注：由于云打印实现字段填充后字段长度自适应遇到难点，现改为程序拼接好整一段内容返回
     *
     * @param templateCode
     * @param printDto
     * @return
     */
    private String buildTitle(String templateCode, PurchaseContractTemplatePrintDto printDto) {
        String unit = printDto.getUNIT();
        String vendorName = printDto.getVENDOR_NAME();
        String deliverContent = printDto.getDELIVER_CONTENT();
        PurchaseContractTemplateCode enumByCode = PurchaseContractTemplateCode.getEnumByCode(templateCode);
        switch (enumByCode) {
            case SUPPLY :
                return String.format("    【%s】（以下简称“甲方”）和【%s】（以下简称“乙方”），在平等互利的基础上经过友好协商和充分讨论后达成一致协议并签订此合同。", unit, vendorName);
            case TURNKEY:
                return String.format("    【%s】（以下简称甲方）和【%s】（以下简称乙方），针对甲方从乙方采购、乙方向甲方交付【%s】工程事宜，在平等互利的基础上经过充分讨论和友好协商后达成一致意见并签订此合同。", unit, vendorName, deliverContent);
            case INSTALL:
                return String.format("    【%s】（以下简称甲方）和【%s】（以下简称乙方），针对甲方从乙方采购、乙方向甲方交付【%s】工程事宜，在平等互利的基础上经过充分讨论和友好协商后达成一致意见并签订此合同。", unit, vendorName, deliverContent);
            case OUTSOURCE:
                return String.format("    【%s】（以下简称“甲方”）和【%s】（以下简称“乙方”），在平等互利的基础上经过友好协商和充分讨论后达成一致协议并签订此合同。", unit, vendorName);
            default:
                return null;
        }
    }

    private JSONObject getTokenFromJson(String printCode) {
        // token获取API /pdf/token/{printCode}/{tenantCode}/{userCode}
        try {
            URL url = new URL(urlPath + "token" + "/" + printCode + "/" + tenantCode + "/" + userCode);
            logger.info("TOKENurl:" + url);
            URLConnection urlConnection = url.openConnection();
            HttpURLConnection httpURLConnection = (HttpURLConnection) urlConnection;
            httpURLConnection.setDoOutput(true);
            httpURLConnection.setDoInput(true);
            httpURLConnection.setUseCaches(false);
            httpURLConnection.setRequestProperty("Content-type", "application/octet-stream;charset=UTF-8");
            httpURLConnection.setRequestMethod("GET");
            httpURLConnection.setRequestProperty("connection", "Keep-Alive");
            httpURLConnection.setRequestProperty("Charsert", "UTF-8");
            httpURLConnection.setRequestProperty("content-disposition", "attachment");
            httpURLConnection.setRequestProperty("Content-Length", "1361");
            httpURLConnection.setConnectTimeout(60000);
            httpURLConnection.setReadTimeout(60000);
            httpURLConnection.connect();
            httpURLConnection.getResponseCode();
            InputStream inputStream = httpURLConnection.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));
            StringBuilder sbf = new StringBuilder();
            String strRead = null;
            while ((strRead = reader.readLine()) != null) {
                sbf.append(strRead);
                sbf.append("\r\n");
            }
            String result = sbf.toString();
            logger.info(result);
            JSONObject json = JSONObject.parseObject(result);
            logger.info("json:" + json);
            JSONObject object = JSONObject.parseObject(json.getString("data"));
            logger.info("pdfUrl:" + object.getString("pdfUrl"));
            //获取token
            logger.info("token:" + object.getString("token"));
            return object;
        } catch (Exception e) {
            logger.error("token获取失败:", e);
            return null;
        }
    }

    private static String doPost(String url, String param) throws IOException {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        httpPost.addHeader("Content-Type", "application/json");
        httpPost.setEntity(new StringEntity(param));
        CloseableHttpResponse response = httpClient.execute(httpPost);
        logger.info("状态为：{}", response.getStatusLine().getStatusCode());
        System.out.println(response.getStatusLine().getStatusCode() + "\n");
        HttpEntity entity = response.getEntity();
        String responseContent = EntityUtils.toString(entity, "UTF-8");
        System.out.println(responseContent);
        response.close();
        httpClient.close();
        return responseContent;
    }

    /**
     * 通过传入一个列表对象,调用指定方法将列表中的数据生成一个JSON规格指定字符串
     *
     * @param list 列表对象
     * @return String "[{},{}]"
     */
    public static String listToJson(List<?> list) {
        StringBuilder json = new StringBuilder();
        json.append("[");
        if (list != null && list.size() > 0) {
            for (Object obj : list) {
                json.append(objectToJson(obj));
                json.append(",");
            }
            json.setCharAt(json.length() - 1, ']');
        } else {
            json.append("]");
        }
        return json.toString();
    }

    /**
     * 传入任意一个 object对象生成一个指定规格的字符串
     *
     * @param object 任意对象
     * @return String
     */
    public static String objectToJson(Object object) {
        StringBuilder json = new StringBuilder();
        if (object == null) {
            json.append("\"\"");
        } else if (object instanceof String || object instanceof Integer || object instanceof Double) {
            String str = object.toString().replaceAll("\\\\", "\\\\\\\\");
            str = str.replaceAll("\"", "\\\\\"");
            json.append("\"").append(str).append("\"");
        } else {
            json.append(beanToJson(object));
        }
        return json.toString();
    }

    /**
     * 传入任意一个 Javabean对象生成一个指定规格的字符串
     *
     * @param bean bean对象
     * @return String "{}"
     */
    public static String beanToJson(Object bean) {
        StringBuilder json = new StringBuilder();
        json.append("{");
        PropertyDescriptor[] props = null;
        try {
            props = Introspector.getBeanInfo(bean.getClass(), Object.class).getPropertyDescriptors();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        if (props != null) {
            for (int i = 0; i < props.length; i++) {
                try {
                    String name = objectToJson(props[i].getName());
                    String value = objectToJson(props[i].getReadMethod().invoke(bean));
                    json.append(name);
                    json.append(":");
                    json.append(value);
                    json.append(",");
                } catch (Exception e) {
                }
            }
            json.setCharAt(json.length() - 1, '}');
        } else {
            json.append("}");
        }
        return json.toString();
    }

    private void writePrintNum(String printId) {
        String userName = SystemContext.getUserName();
        Long userId = SystemContext.getUserId();
        //统计打印次数
        CloudPrintStatisticsExample example = new CloudPrintStatisticsExample();
        CloudPrintStatisticsExample.Criteria criteria = example.createCriteria();
        criteria.andPrintIdsEqualTo(printId);
        List<CloudPrintStatistics> cloudPrintStatisticsList = cloudPrintStatisticsMapper.selectByExample(example);
        CloudPrintStatistics cloudPrintStatistics = new CloudPrintStatistics();
        if (ListUtils.isEmpty(cloudPrintStatisticsList)) {
            cloudPrintStatistics.setPrintIds(printId);
            cloudPrintStatistics.setUserName(userName);
            cloudPrintStatistics.setPrintNum(1L);
            cloudPrintStatistics.setDeletedFlag(DeletedFlag.VALID.code());
            cloudPrintStatistics.setCreateAt(new Date());
            cloudPrintStatistics.setCreateBy(userId);
            cloudPrintStatisticsMapper.insert(cloudPrintStatistics);
        } else {
            Long printNum = cloudPrintStatisticsList.get(0).getPrintNum();
            cloudPrintStatistics.setId(cloudPrintStatisticsList.get(0).getId());
            cloudPrintStatistics.setPrintNum(++printNum);
            cloudPrintStatistics.setUpdateAt(new Date());
            cloudPrintStatistics.setUpdateBy(userId);
            cloudPrintStatisticsMapper.updateByPrimaryKeySelective(cloudPrintStatistics);
        }
    }

    @Override
    public PrintPDFParam exeInvoiceReceivableDetailsPrint(Long invoiceReceivableId,String bankAccount,String accountName,String combineBankNumber) {

        final Map<String, Object> param = new HashMap<>();
        param.put("id",invoiceReceivableId);
        param.put("unitId",SystemContext.getUnitId());
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/invoiceReceivable/queryExtDetail", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<InvoiceReceivableDto> response = JSON.parseObject(res, new TypeReference< DataResponse<InvoiceReceivableDto>>() {
        });

        if(Objects.isNull(response)){
             throw new BizException(Code.ERROR, "获取数据失败");
        }

        InvoiceReceivableDto invoiceReceivableDto = response.getData();

        if(Objects.isNull(invoiceReceivableDto)){
            throw new BizException(Code.ERROR, "获取应收发票数据失败");
        }

        InvoiceReceivableDetailPrintDto printDto = new InvoiceReceivableDetailPrintDto();

        Optional.ofNullable(invoiceReceivableDto.getInvoiceCode()).ifPresent(printDto::setInvoiceNo);
        Optional.ofNullable(invoiceReceivableDto.getOuId()).map(CacheDataUtils::findOuById).ifPresent(s->printDto.setBusinessUnit(s.getOperatingUnitName()));
        Optional.ofNullable(invoiceReceivableDto.getCurrencyCode()).ifPresent(printDto::setCurrency);
        Optional.ofNullable(invoiceReceivableDto.getConversionRate())
                .map(rate -> rate.stripTrailingZeros().toPlainString())
                .ifPresent(printDto::setExchangeRate);
        Optional.ofNullable(invoiceReceivableDto.getCustomerCode()).ifPresent(printDto::setCustomerCrmCode);
        Optional.ofNullable(invoiceReceivableDto.getCustomerName()).ifPresent(printDto::setCustomerName);
        Optional.ofNullable(invoiceReceivableDto.getContractCode()).ifPresent(printDto::setSubcontractNo);
        Optional.ofNullable(invoiceReceivableDto.getContractName()).ifPresent(printDto::setSubcontractName);
        Optional.ofNullable(invoiceReceivableDto.getDueDate())
                .map(date -> DateUtils.format(date, DatePattern.NORM_DATE_PATTERN))
                .ifPresent(printDto::setInvoiceDueDate);
        Optional.ofNullable(invoiceReceivableDto.getRemarks()).ifPresent(printDto::setRemarks);
        Optional.ofNullable(invoiceReceivableDto.getTaxIncludedPrice())
                .map(BigDecimal::toPlainString)
                .ifPresent(printDto::setTotalAmountInclTax);
        Optional.ofNullable(invoiceReceivableDto.getExclusiveOfTax())
                .map(BigDecimal::toPlainString)
                .ifPresent(printDto::setTotalAmountExclTax);
        Optional.ofNullable(invoiceReceivableDto.getTaxIncludedPrice())
                .map(s-> BigDecimalUtils.scaleAndToString( s.multiply(invoiceReceivableDto.getConversionRate()).setScale(2, RoundingMode.HALF_UP)))
                .ifPresent(printDto::setLocalCurrencyAmountInclTax);
        Optional.ofNullable(invoiceReceivableDto.getExclusiveOfTax())
                .map(s-> BigDecimalUtils.scaleAndToString( s.multiply(invoiceReceivableDto.getConversionRate()).setScale(2, RoundingMode.HALF_UP)))
                .ifPresent(printDto::setLocalCurrencyAmountExclTax);

        ArrayList<InvoiceReceivableDetailPrintDto.DetailItem> detailItems = new ArrayList<>();

        Optional.ofNullable(invoiceReceivableDto.getApplyDetailSplitList())
                .orElse(Collections.emptyList())
                .forEach(item -> {
                    InvoiceReceivableDetailPrintDto.DetailItem detailItem = new InvoiceReceivableDetailPrintDto.DetailItem();
                    Optional.ofNullable(item.getProduct()).ifPresent(detailItem::setServiceTypeProductName);
                    Optional.ofNullable(item.getProductTaxName()).ifPresent(detailItem::setTaxableProductName);
                    Optional.ofNullable(item.getUnit()).ifPresent(detailItem::setUnit);
                    Optional.ofNullable(item.getQuantity())
                            .map(BigDecimalUtils::stripTrailingZerosString)
                            .ifPresent(detailItem::setQuantity);
                    Optional.ofNullable(item.getTaxRace())
                            .map(BigDecimalUtils::stripTrailingZerosString)
                            .ifPresent(detailItem::setTaxRate);
                    Optional.ofNullable(item.getPrice())
                            .map(s->s.setScale(6, RoundingMode.HALF_UP).toPlainString())
                            .ifPresent(detailItem::setUnitPrice);
                    Optional.ofNullable(item.getTaxIncludedPrice())
                            .map(s-> s.setScale(2, RoundingMode.HALF_UP).toPlainString())
                            .ifPresent(detailItem::setTotalPriceInclTax);
                    Optional.ofNullable(item.getExclusiveOfTax())
                            .map(s-> s.setScale(2, RoundingMode.HALF_UP).toPlainString())
                            .ifPresent(detailItem::setTotalPriceExclTax);
                    Optional.ofNullable(item.getRemark()).ifPresent(detailItem::setItemRemarks);
                    detailItem.setTaxAmount(
                            Optional.ofNullable(invoiceReceivableDto.getExternalInvoiceTax())
                                    .map(BigDecimal::toPlainString)
                                    .orElseGet(() -> new BigDecimal("0.00").toPlainString())
                    );
                    detailItems.add(detailItem);
                });

        printDto.setDetails(detailItems);

        Optional.ofNullable(bankAccount).ifPresent(printDto::setAccountNumber);
        Optional.ofNullable(accountName).ifPresent(printDto::setBankName);
        Optional.ofNullable(combineBankNumber).ifPresent(printDto::setSwiftCode);

        Long unitId = SystemContext.getUnitId();

        HashMap<Long, String> templateMap = new HashMap<>();
        templateMap.put(789091426037137408L,"InvoiceReceivable");
        templateMap.put(844572813460242432L,"InvoiceReceivable");

        templateMap.put(581144287652085760L,"InvoiceReceivable-KUKA");
        templateMap.put(814428597413478400L,"InvoiceReceivable-KUKA");
        templateMap.put(846396138532634624L,"InvoiceReceivable-KUKA");
        templateMap.put(1008730763140530176L,"InvoiceReceivable-KUKA");
        templateMap.put(1105533683340673024L,"InvoiceReceivable-KUKA");
        templateMap.put(1296044147280900096L,"InvoiceReceivable-KUKA");

        templateMap.put(884455544528568320L,"InvoiceReceivable-RSGYL");

        String printTemplateName = templateMap.getOrDefault(unitId, "InvoiceReceivable-RSGWL");

        JSONObject tokenApi = this.getTokenFromJson(printTemplateName);

        Object pdfUrl = tokenApi.get("pdfUrl");
        logger.info("罚扣内容打印，获取pdfUrl为:{}",pdfUrl);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data", printDto);
        logger.info("resultUnit:{}", JSON.toJSONString(printDto));
        String s = JSON.toJSONString(printDto);
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("{");
        stringBuffer.append("\"");
        stringBuffer.append("data");
        stringBuffer.append("\"");
        stringBuffer.append(" : ");
        stringBuffer.append(s);
        stringBuffer.append("}");
        logger.info("结果集为：{}", stringBuffer);
        PrintPDFParam printPDFParam = new PrintPDFParam();
        printPDFParam.setJson(stringBuffer.toString());
        printPDFParam.setUrl(pdfUrl.toString());
        return printPDFParam;
    }
}
