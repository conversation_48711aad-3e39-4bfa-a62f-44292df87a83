package com.midea.pam.ctc.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.midea.pam.cache.ValueCache;
import com.midea.pam.common.basedata.dto.GlPeriodDto;
import com.midea.pam.common.basedata.dto.OrganizationRelDto;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.basedata.query.GlPeriodQuery;
import com.midea.pam.common.basedata.query.OrganizationRelQuery;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.dto.CarryoverBillDto;
import com.midea.pam.common.ctc.dto.CarryoverBillProcessScheduleDto;
import com.midea.pam.common.ctc.dto.ProjectMilepostDto;
import com.midea.pam.common.ctc.dto.SelectionRangeDto;
import com.midea.pam.common.ctc.entity.CarryoverBill;
import com.midea.pam.common.ctc.entity.CarryoverBillProcessSchedule;
import com.midea.pam.common.ctc.entity.CarryoverBillProcessScheduleExample;
import com.midea.pam.common.ctc.entity.CtcAttachment;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectIncomeCostPlan;
import com.midea.pam.common.ctc.entity.ProjectIncomeCostPlanExample;
import com.midea.pam.common.ctc.entity.ProjectMilepost;
import com.midea.pam.common.ctc.entity.ProjectType;
import com.midea.pam.common.enums.CarryoverBatchType;
import com.midea.pam.common.enums.CarryoverBillResourceType;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.CostMethod;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.GlPeriodType;
import com.midea.pam.common.enums.IncomePoint;
import com.midea.pam.common.enums.OrgCustomDictOrgFrom;
import com.midea.pam.common.enums.ProjectStatus;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.CacheKey;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.ListUtil;
import com.midea.pam.ctc.mapper.CarryoverBillExtMapper;
import com.midea.pam.ctc.mapper.CarryoverBillMapper;
import com.midea.pam.ctc.mapper.CarryoverBillProcessScheduleMapper;
import com.midea.pam.ctc.mapper.CtcAttachmentMapper;
import com.midea.pam.ctc.mapper.ProjectIncomeCostPlanMapper;
import com.midea.pam.ctc.mapper.SelectionRangeMapper;
import com.midea.pam.ctc.service.CarryoverBillBatchManageService;
import com.midea.pam.ctc.service.CarryoverBillProcessScheduleService;
import com.midea.pam.ctc.service.CarryoverBillService;
import com.midea.pam.ctc.service.OrganizationCustomDictService;
import com.midea.pam.ctc.service.ProjectIncomeCostPlanService;
import com.midea.pam.ctc.service.ProjectMilepostService;
import com.midea.pam.ctc.service.ProjectService;
import com.midea.pam.ctc.service.ProjectTypeService;
import com.midea.pam.ctc.service.helper.GlPeriodHelper;
import com.midea.pam.ctc.service.helper.OrganizationRelHelper;
import com.midea.pam.ctc.service.helper.PageUtils;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.midea.pam.common.enums.ProjectStatus.APPROVALED;
import static com.midea.pam.common.enums.ProjectStatus.CLOSE;
import static com.midea.pam.common.enums.ProjectStatus.TERMINATION;

public class CarryoverBillProcessScheduleServiceImpl implements CarryoverBillProcessScheduleService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private CarryoverBillProcessScheduleMapper carryoverBillProcessScheduleMapper;
    @Resource
    private SelectionRangeMapper selectionRangeMapper;
    @Resource
    private ProjectMilepostService projectMilepostService;
    @Resource
    private CarryoverBillService carryoverBillService;
    @Resource
    private GlPeriodHelper glPeriodHelper;
    @Resource
    private OrganizationCustomDictService organizationCustomDictService;
    @Resource
    private OrganizationRelHelper organizationRelHelper;
    @Resource
    private ProjectIncomeCostPlanMapper projectIncomeCostPlanMapper;
    @Resource
    private ProjectIncomeCostPlanService projectIncomeCostPlanService;
    @Resource
    private CarryoverBillBatchManageService carryoverBillBatchManageService;
    @Resource
    private ProjectService projectService;
    @Resource
    private ProjectTypeService projectTypeService;
    @Resource
    private CtcAttachmentMapper ctcAttachmentMapper;
    @Resource
    private CarryoverBillMapper carryoverBillMapper;
    @Resource
    private CarryoverBillExtMapper carryoverBillExtMapper;
    @Resource
    private ValueCache valueCache;


    @Override
    public CarryoverBillProcessScheduleDto add(CarryoverBillProcessScheduleDto dto) {
        dto.setDeletedFlag(DeletedFlag.VALID.code());
        CarryoverBillProcessSchedule entity = BeanConverter.copy(dto, CarryoverBillProcessSchedule.class);
        carryoverBillProcessScheduleMapper.insert(entity);
        BeanConverter.copy(entity, dto);
        return dto;
    }

    @Override
    public CarryoverBillProcessScheduleDto update(CarryoverBillProcessScheduleDto dto) {
        CarryoverBillProcessSchedule entity = BeanConverter.copy(dto, CarryoverBillProcessSchedule.class);
        carryoverBillProcessScheduleMapper.updateByPrimaryKeySelective(entity);
        BeanConverter.copy(entity, dto);
        return dto;
    }

    @Override
    public CarryoverBillProcessScheduleDto save(CarryoverBillProcessScheduleDto dto, Long userBy) {
        if (dto.getId() == null) {
            dto.setCreateBy(userBy);
            return this.add(dto);
        } else {
            dto.setUpdateBy(userBy);
            return this.update(dto);
        }
    }

    @Override
    public CarryoverBillProcessScheduleDto getById(Long id) {
        return null;
    }

    @Override
    public List<CarryoverBillProcessScheduleDto> selectList(CarryoverBillProcessScheduleDto query) {
        List<CarryoverBillProcessSchedule> list = carryoverBillProcessScheduleMapper.selectByExampleWithBLOBs(buildCondition(query));
        List<CarryoverBillProcessScheduleDto> dtos = BeanConverter.copy(list, CarryoverBillProcessScheduleDto.class);
        return dtos;
    }

    @Override
    public PageInfo<CarryoverBillProcessScheduleDto> selectPage(CarryoverBillProcessScheduleDto query) {
        return null;
    }

    @Override
    public PageInfo<SelectionRangeDto> selectionRangePage(SelectionRangeDto query) {
        query.setProjectStatuses(Arrays.asList(APPROVALED.getCode(), CLOSE.getCode(), TERMINATION.getCode()));

        query = build(query);

        // 可结转的里程碑列表
        final List<SelectionRangeDto> milepostSelectionRange = selectionRangeMapper.selectionRangeOfMilepost(query);
        List<SelectionRangeDto> list = new ArrayList<>(milepostSelectionRange);

        // 可结转的收入成本计划列表
        final List<SelectionRangeDto> selectionRangeOfIncomeCostPlan = getSelectionRangeOfIncomeCostPlan(query);
        list.addAll(selectionRangeOfIncomeCostPlan);

        // 过滤锁定的批次号对应的数据
        filterLockBatchNum(list, SystemContext.getUserId());

        if (Objects.equals(CostMethod.FAT_INCOME.getCode(), query.getCostMethod())
                || Objects.equals(CostMethod.PLAN_INCOME.getCode(), query.getCostMethod())) {
            list.forEach(s -> {
                CarryoverBill carryoverBill = carryoverBillExtMapper.getCarryoverBillTotalIncome(s.getProjectId());
                boolean isLast = checkLast(s.getProjectId()); //是否最后一个节点
                if (null != carryoverBill) {
                    s.setAutoloandailyTotal(carryoverBill.getCurrentIncomeTotalAmount()); //累计收入总额-原币（不含本期）
                    s.setStandardAutoloandailyTotalHistory(carryoverBill.getStandardCumulativeIncomeTotal()); //累计收入总额-本位币（不含本期）
                    s.setIncomeRatioTotal(carryoverBill.getCumulativeIncomePercent()); //累计收入比例
                    s.setLastMilePost(isLast);
                } else {
                    s.setAutoloandailyTotal(new BigDecimal(0));
                    s.setStandardAutoloandailyTotalHistory(new BigDecimal(0));
                    s.setIncomeRatioTotal(new BigDecimal(0));
                    s.setLastMilePost(false);
                }
            });
        }
        // 封装分页数据
        final PageInfo<SelectionRangeDto> page = PageUtils.buildPageInfo(list, query.getPageSize(), query.getPageNum());

        for (SelectionRangeDto selectionRangeDto : page.getList()) {
            if (selectionRangeDto.getCostMethod() != null) {
                selectionRangeDto.setCostMethodName(CostMethod.getValue(selectionRangeDto.getCostMethod()));
            }
        }
        return page;
    }

    /**
     * 查询收入成本计划待结转数据
     * <p>
     * 1、根据项目的业务实体（OU）和项目类型，查询业务实体层的组织参数配置【收入总额不固定类型】的值，如果不存在跳过此条件，如果存在，增加以下判断：
     * 根据前面2个条件找到的收入确认节点，需判断是否是项目收入计划最后一个节点，当是最后一个节点时，项目主里程碑的最后一个里程碑节点必须是“通过”状态，
     * 项目的最后一个收入节点才满足结转条件，显示在选择列表中，否则不能显示选择列表中
     * 在“已选条目”字段加解释：一个项目同时存在多个待结转的数据时，需按节点的顺序分批结转，单次结转只能做一个收入节点确认；
     * 2、在创建结转单第一步时，如果一个项目有多个待结转数据，按节点顺序只显示第一个收入结转节点；
     *
     * @param query 查询条件
     * @return 可参与结转的收入成本计划列表
     */
    private List<SelectionRangeDto> getSelectionRangeOfIncomeCostPlan(SelectionRangeDto query) {
        // 根据新建结转单对应的业务实体，先从【基础数据-ERP组织查询】中获取【分类账】，再根据【分类账】从【基础数据-会计期间】中查询【期间类型】等于“应收期间”，【状态】等于“打开”的期间
        final Long projectOuId = query.getProjectOuId();
        Asserts.notEmpty(projectOuId, ErrorCode.CTC_CARRYOVER_BILL_OU_ID_NULL);

        OrganizationRelQuery organizationRelQuery = new OrganizationRelQuery();
        organizationRelQuery.setOperatingUnitId(projectOuId);
        final List<OrganizationRelDto> organizationRelDtoList = organizationRelHelper.list(organizationRelQuery);
        if (CollectionUtils.isEmpty(organizationRelDtoList)) {
            // 是否可定义需要收集且需要反馈到应用管理员的错误日志，例如参数未配置的类型的，提示应用管理员配置跟进
            logger.warn("{}对应的ERP组织不存在，请核对下ERP组织配置信息", projectOuId);
            throw new BizException(Code.ERROR, "【" + projectOuId + "】对应的ERP组织不存在");
        }

        // ERP分类账ID
        final Long ledgerId = organizationRelDtoList.get(0).getLedgerId();
        GlPeriodQuery glPeriodQuery = new GlPeriodQuery();
        glPeriodQuery.setLedgerId(ledgerId);
        glPeriodQuery.setClosingStatus("O");
        glPeriodQuery.setPeriodType(String.valueOf(GlPeriodType.RECEIVABLES_PERIOD.getCode()));
        final List<GlPeriodDto> glPeriodDTOs = glPeriodHelper.selectList(glPeriodQuery);
        if (CollectionUtils.isEmpty(glPeriodDTOs)) {
            logger.error("入参：{}，查询ERP分类账不存在，请核对下ERP组织配置信息", glPeriodQuery);
            throw new BizException(Code.ERROR, "分类账【" + ledgerId + "】，会计期间不存在");
        }

        // 会计期间最小的值
        glPeriodDTOs.sort(Comparator.comparing(GlPeriodDto::getPeriodName));

        final GlPeriodDto glPeriodDto = glPeriodDTOs.get(0);
        String needCarryoverPeriod = glPeriodDto.getPeriodName();
        query.setCarryoverPeriod(needCarryoverPeriod);

        // 满足结转期间的收入成本计划
        final List<SelectionRangeDto> incomeCostPlanSelectionRange = selectionRangeMapper.selectionRangeOfIncomeCostPlan(query);

        // 处理收入成本计划，移除不符合规则的数据
        if (CollectionUtils.isNotEmpty(incomeCostPlanSelectionRange)) {
            // 查询组织参数配置【收入总额不固定类型】
            final Set<String> organizationCustomDictSet = organizationCustomDictService.queryByName("收入总额不固定类型", projectOuId, OrgCustomDictOrgFrom.OU);
            if (CollectionUtils.isEmpty(organizationCustomDictSet)) {
                throw new BizException(Code.ERROR, projectOuId + "对应【收入总额不固定类型】组织参数未配置");
            }

            // 包含需要校验里程碑的项目ID列表
            // 11.13 目前只有人力外包项目在做收入成本结转时控制了，最后一期的收入需要在项目结项后才能做，需要调整为月度确认的所有项目都必须在
            // 项目结项后才能做最后一期的收入成本结转(BUG2019110867559)
            List<Long> needCheckProjectIdList = new ArrayList<>();
            incomeCostPlanSelectionRange.forEach(selectionRangeDto -> {
                // 根据前面2个条件找到的收入确认节点，需判断是否是项目收入计划最后一个节点，当是最后一个节点时，
                // 项目主里程碑的最后一个里程碑节点必须是“通过”状态，项目的最后一个收入节点才满足结转条件，
                // 显示在选择列表中，否则不能显示选择列表中

                // 判断此次结转的收入成本计划是否为对应项目的最后一个收入节点
                final Long milepostId = selectionRangeDto.getMilepostId();
                final Long projectId = selectionRangeDto.getProjectId();
                Project project = projectService.selectByPrimaryKey(projectId);
                Long type = project.getType();
                ProjectType projectType = projectTypeService.selectByPrimaryKey(type);

                boolean isLast = false;

                if (IncomePoint.MONTH.getCode().equals(projectType.getIncomePoint()) && CostMethod.COST.getCode().equals(projectType.getCostMethod())) {
                    if (null != project.getStatus() && (project.getStatus() == 10) || project.getStatus() == 16) {
                        isLast = true;
                    }
                } else {
                    isLast = projectIncomeCostPlanService.checkLastIncomeCostPlan(projectId, milepostId);
                }

                // 实际收入法不校验最后一个节点逻辑
                // 增加可结转的项目状态：终止审批中15、终止16
                String costMethod = projectType.getCostMethod();
                if (!CostMethod.ACTUALLY.getCode().equals(costMethod)) {
                    if (isLast) {
                        if (!Objects.equals(ProjectStatus.TERMINATION.getCode(), project.getStatus())) {
                            needCheckProjectIdList.add(projectId);
                        }
                    }
                }
            });

            if (CollectionUtils.isNotEmpty(needCheckProjectIdList)) {
                // 批量查询最后一个里程碑节点是通过状态的项目
                final List<Long> canUseProjectIds =
                        projectMilepostService.filterLastMilepostAndApproved(needCheckProjectIdList);

                // 不符合“最后一个里程碑节点必须是“通过”状态，需要删除的数据
                List<Long> needDeleteProjectIds = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(canUseProjectIds)) {
                    for (int i = needCheckProjectIdList.size() - 1; i >= 0; i--) {
                        final Long needCheckProjectId = needCheckProjectIdList.get(i);

                        if (!canUseProjectIds.contains(needCheckProjectId)) {
                            needDeleteProjectIds.add(needCheckProjectId);
                        }
                    }
                } else {
                    needDeleteProjectIds = needCheckProjectIdList;
                }

                // 去除不能显示在列表的数据
                final Iterator<SelectionRangeDto> iterator = incomeCostPlanSelectionRange.iterator();
                while (iterator.hasNext()) {
                    final SelectionRangeDto selectionRangeDto = iterator.next();
                    final Long projectId = selectionRangeDto.getProjectId();

                    if (needDeleteProjectIds.contains(projectId)) {
                        iterator.remove();
                    }
                }
            }
        }

        return incomeCostPlanSelectionRange;
    }

    /**
     * 排除锁定的批次号
     */
    private void filterLockBatchNum(List<SelectionRangeDto> list, Long userId) {
        if (ListUtils.isEmpty(list)) {
            return;
        }

        // 过滤
        // 所有锁定的批次号
        List<String> allLockBatchNum = carryoverBillBatchManageService.getAllLockBatchNum();
        // 自己锁定的批次号
        String myBatchNum = carryoverBillBatchManageService.get(userId);
        if (myBatchNum != null) {
            // 排除自己锁定的批次
            allLockBatchNum.remove(myBatchNum);
        }

        for (int i = list.size() - 1; i >= 0; i--) {
            SelectionRangeDto selectionRangeDto = list.get(i);
            String carryoverBatchNum = selectionRangeDto.getCarryoverBatchNum();
            // 里程碑已被其他人锁定
            if (StringUtils.isNotEmpty(carryoverBatchNum) && allLockBatchNum.contains(carryoverBatchNum)) {
                list.remove(i);
            }
        }
    }

    @Override
    public List<SelectionRangeDto> selectionRangeList(SelectionRangeDto query) {
        List<Integer> projectStatuses = new ArrayList<>();
        projectStatuses.add(ProjectStatus.APPROVALED.getCode());
        projectStatuses.add(ProjectStatus.CLOSE.getCode());
        projectStatuses.add(ProjectStatus.TERMINATION.getCode());
        query.setProjectStatuses(projectStatuses);

        List<SelectionRangeDto> list = new ArrayList<>();

        build(query);

        // 可结转的里程碑列表
        final List<SelectionRangeDto> milepostSelectionRange = selectionRangeMapper.selectionRangeOfMilepost(query);
        list.addAll(milepostSelectionRange);

        // 可结转的收入成本计划列表
        final List<SelectionRangeDto> selectionRangeOfIncomeCostPlan = getSelectionRangeOfIncomeCostPlan(query);
        list.addAll(selectionRangeOfIncomeCostPlan);

        // 过滤
        filterLockBatchNum(list, SystemContext.getUserId());

        return list;
    }

    private SelectionRangeDto build(SelectionRangeDto query) {
        String costMethod = query.getCostMethod();
        if (StringUtils.isNotEmpty(costMethod)) {
            List<String> costMethods = new ArrayList<>();
            String[] arrStr = costMethod.split(",");
            for (String s : arrStr) {
                if (StringUtils.isNotEmpty(s)) {
                    costMethods.add(s);
                }
            }
            query.setCostMethods(costMethods);
        }
        if (StringUtils.isNotEmpty(query.getUnitIdStr())) {
            query.setUnitIds(Arrays.stream(query.getUnitIdStr().split(",")).map(Long::parseLong).collect(Collectors.toList()));
        }
        return query;
    }

    @Override
    public CarryoverBillProcessScheduleDto getByBatchNumAndUserBy(String carryoverBatchNum, Long carryoverUserBy) {
        Asserts.notEmpty(carryoverBatchNum, ErrorCode.CTC_BATCH_NUM_NOT_NULL);
        CarryoverBillProcessScheduleDto dto = null;
        CarryoverBillProcessScheduleDto query = new CarryoverBillProcessScheduleDto();
        query.setCarryoverBatchNum(carryoverBatchNum);
        List<CarryoverBillProcessScheduleDto> list = this.selectList(query);

        if (ListUtil.isPresent(list)) {
            dto = list.get(0);

            int carryoversNumber = projectMilepostService.getCarryoverRangeMilepostIds(carryoverBatchNum, true).size()
                    + this.getCarryoverRangeIncomeCostPlanIds(carryoverBatchNum, "1").size();
            int removeNumber = projectMilepostService.getCarryoverRangeMilepostIds(carryoverBatchNum, false).size()
                    + this.getCarryoverRangeIncomeCostPlanIds(carryoverBatchNum, "0").size();
            int thisTimeCarryBillNum = carryoversNumber;
            int carryoversNumberOfMilepost = carryoversNumber;
            int removeNumberOfMilepost = removeNumber;

            dto.setCarryoversNumber(carryoversNumber);
            dto.setRemoveNumber(removeNumber);
            dto.setThisTimeCarryBillNum(thisTimeCarryBillNum);
            dto.setCarryoversNumberOfMilepost(carryoversNumberOfMilepost);
            dto.setRemoveNumberOfMilepost(removeNumberOfMilepost);
        }

        return dto;
    }

    @Override
    public CarryoverBillProcessScheduleDto getDetailByBatchNumAndUserBy(String carryoverBatchNum, Long carryoverUserBy) {
        CarryoverBillProcessScheduleDto dto = this.getByBatchNumAndUserBy(carryoverBatchNum, carryoverUserBy);

        List<Long> passProjectIds = new ArrayList<>();
        List<Long> noPassProjectIds = new ArrayList<>();
        if (dto != null) {
            List<CarryoverBill> carryoverBills = new ArrayList<>();

            List<ProjectMilepostDto> milepostDtos =
                    projectMilepostService.getCarryoverRangeMilepost(carryoverBatchNum, Boolean.TRUE);

            if (ListUtils.isNotEmpty(milepostDtos)) {
                for (ProjectMilepostDto milepostDto : milepostDtos) {
                    CarryoverBill carryoverBill = new CarryoverBillDto();
                    carryoverBill.setProjectId(milepostDto.getProjectId());
                    carryoverBill.setProjectMilepostName(milepostDto.getName());
                    carryoverBill.setProjectMilepostId(milepostDto.getId());

                    Project project = projectService.selectByPrimaryKey(milepostDto.getProjectId());
                    carryoverBill.setProjectName(project.getName());
                    carryoverBill.setProjectNum(project.getCode());
                    carryoverBill.setProjectType(project.getType());
                    carryoverBill.setResourceType(CarryoverBillResourceType.MILEPOST.getCode());

                    carryoverBills.add(carryoverBill);
                }
            }

            ProjectIncomeCostPlanExample example = new ProjectIncomeCostPlanExample();
            ProjectIncomeCostPlanExample.Criteria criteria1 = example.createCriteria();
            criteria1.andDeletedFlagEqualTo(Boolean.FALSE).andCarryoverBatchNumEqualTo(carryoverBatchNum).andCarryStatusEqualTo("1");
            List<ProjectIncomeCostPlan> projectIncomeCostPlans =
                    projectIncomeCostPlanMapper.selectByExample(example);
            if (ListUtils.isNotEmpty(projectIncomeCostPlans)) {
                for (ProjectIncomeCostPlan projectIncomeCostPlan : projectIncomeCostPlans) {
                    CarryoverBill carryoverBill = new CarryoverBill();
                    carryoverBill.setProjectId(projectIncomeCostPlan.getProjectId());
                    carryoverBill.setProjectMilepostName(projectIncomeCostPlan.getName());
                    carryoverBill.setProjectMilepostId(projectIncomeCostPlan.getId());

                    Project project = projectService.selectByPrimaryKey(projectIncomeCostPlan.getProjectId());
                    carryoverBill.setProjectName(project.getName());
                    carryoverBill.setProjectNum(project.getCode());
                    carryoverBill.setProjectType(project.getType());
                    carryoverBill.setResourceType(CarryoverBillResourceType.INCOME_COST_PLAN.getCode());

                    carryoverBills.add(carryoverBill);
                }
            }

            Map<Long, CarryoverBill> carryoverBillMap = new HashMap<>();
            if (ListUtils.isNotEmpty(carryoverBills)) {
                carryoverBills.forEach(carryoverBill -> {
                    passProjectIds.add(carryoverBill.getProjectId());
                    carryoverBillMap.put(carryoverBill.getProjectId(), carryoverBill);
                });
            }

            // 最后一个节点
            String checkMessage = dto.getCheckMessage();
            if (StringUtils.isNotEmpty(checkMessage)) {

                JSONArray checkResult = new JSONArray();
                JSONArray otherCheckResult = new JSONArray();

                JSONObject jsonObject = JSONObject.parseObject(checkMessage);
                Set<Map.Entry<String, Object>> entries = jsonObject.entrySet();
                for (Map.Entry<String, Object> entry : entries) {
                    String projectId = entry.getKey();
                    JSONObject value = (JSONObject) entry.getValue();

                    CarryoverBill carryoverBill = carryoverBillMap.get(Long.valueOf(projectId));
                    if (carryoverBill != null) {
                        String projectName = carryoverBill.getProjectName();
                        String projectMilepostName = carryoverBill.getProjectMilepostName();
                        Long projectMilepostId = carryoverBill.getProjectMilepostId();
                        Long projectTypeId = carryoverBill.getProjectType();
                        Integer resourceType = carryoverBill.getResourceType();
                        String projectNum = carryoverBill.getProjectNum();
                        Date actualStartTime = null;
                        Date actualEndTime = null;

                        if (Objects.equals(resourceType, CarryoverBillResourceType.MILEPOST.getCode())) {
                            ProjectMilepost projectMilepost = projectMilepostService.selectByPrimaryKey(projectMilepostId);
                            if (projectMilepost != null) {
                                actualStartTime = projectMilepost.getActualStartTime();
                                actualEndTime = projectMilepost.getActualEndTime();
                            }
                        } else if (Objects.equals(resourceType, CarryoverBillResourceType.INCOME_COST_PLAN.getCode())) {
                            ProjectIncomeCostPlan projectIncomeCostPlan = projectIncomeCostPlanMapper.selectByPrimaryKey(projectMilepostId);
                            if (projectIncomeCostPlan != null) {
                                actualStartTime = projectIncomeCostPlan.getActualStartTime();
                                actualEndTime = projectIncomeCostPlan.getActualEndTime();
                            }
                        }

                        ProjectType projectType = projectTypeService.selectByPrimaryKey(projectTypeId);
                        String projectTypeName = projectType != null ? projectType.getName() : null;
                        String detail = value.getString("detail");
                        if (StringUtils.isNotEmpty(detail)) {
                            List<String> checkItemList = StringUtils.splitToList(detail, ";");
                            for (String errorMsg : checkItemList) {
                                if (StringUtils.isNotEmpty(errorMsg)) {
                                    JSONObject checkItemObject = new JSONObject();
                                    checkItemObject.put("projectId", projectId);
                                    checkItemObject.put("projectName", projectName);
                                    checkItemObject.put("projectCode", projectNum);
                                    checkItemObject.put("projectMilepostName", projectMilepostName);
                                    checkItemObject.put("projectTypeName", projectTypeName);
                                    checkItemObject.put("actualStartTime", actualStartTime);
                                    checkItemObject.put("actualEndTime", actualEndTime);
                                    checkItemObject.put("checkItemMessage", errorMsg);

                                    checkResult.add(checkItemObject);
                                }
                            }
                        }
                        String otherDetail = value.getString("otherDetail");
                        if (StringUtils.isNotEmpty(otherDetail)) {
                            List<String> otherCheckItemList = StringUtils.splitToList(otherDetail, ";");
                            for (String errorMsg : otherCheckItemList) {
                                if (StringUtils.isNotEmpty(errorMsg)) {
                                    JSONObject checkItemObject = new JSONObject();
                                    checkItemObject.put("projectId", projectId);
                                    checkItemObject.put("projectName", projectName);
                                    checkItemObject.put("projectCode", projectNum);
                                    checkItemObject.put("projectMilepostName", projectMilepostName);
                                    checkItemObject.put("projectTypeName", projectTypeName);
                                    checkItemObject.put("actualStartTime", actualStartTime);
                                    checkItemObject.put("actualEndTime", actualEndTime);
                                    checkItemObject.put("checkItemMessage", errorMsg);

                                    otherCheckResult.add(checkItemObject);
                                }
                            }
                        }

                        // 去除检查异常的项目
                        passProjectIds.remove(Long.valueOf(projectId));
                        noPassProjectIds.add(Long.valueOf(projectId));
                    }
                }

                dto.setCheckResult(checkResult);
                dto.setOtherCheckResult(otherCheckResult);
            }

            dto.setNoPassProjectIds(noPassProjectIds);
            dto.setPassProjectIds(passProjectIds);

            // 设置检查文件信息
            Long checkAttachmentId = dto.getCheckAttachmentId();
            if (checkAttachmentId != null) {
                CtcAttachment ctcAttachment = ctcAttachmentMapper.selectByPrimaryKey(checkAttachmentId);
                dto.setCheckAttachment(ctcAttachment);
            }
        }

        return dto;
    }

    public List<Long> getCarryoverRangeIncomeCostPlanIds(String carryoverBatchNum, String carryStatus) {
        List<ProjectIncomeCostPlan> milepostDtos = this.getCarryoverRangeIncomeCostPlan(carryoverBatchNum, carryStatus);
        if (ListUtil.isBlank(milepostDtos)) {
            return new ArrayList<>();
        }
        List<Long> milepostIds = ListUtil.map(milepostDtos, "id");

        return milepostIds;
    }

    public List<ProjectIncomeCostPlan> getCarryoverRangeIncomeCostPlan(String carryoverBatchNum, String carryStatus) {
        Asserts.notEmpty(carryoverBatchNum, ErrorCode.CTC_BATCH_NUM_NOT_NULL);

        ProjectIncomeCostPlanExample projectIncomeCostPlanExample = new ProjectIncomeCostPlanExample();
        final ProjectIncomeCostPlanExample.Criteria criteria = projectIncomeCostPlanExample.createCriteria();
        criteria.andCarryoverBatchNumEqualTo(carryoverBatchNum);
        criteria.andCarryStatusEqualTo(carryStatus);
        criteria.andCarryoverBillIdIsNull();

        final List<ProjectIncomeCostPlan> list = projectIncomeCostPlanMapper.selectByExample(projectIncomeCostPlanExample);
        return list;
    }


    @Override
    public CarryoverBillProcessScheduleDto totalIncomeCalculation(String carryoverBatchNum, Long carryoverUserBy) {

        CarryoverBillProcessScheduleDto dto = carryoverBillService.totalIncomeCalculation(carryoverBatchNum);

        int carryoversNumber = projectMilepostService.getCarryoverRangeMilepostIds(carryoverBatchNum, true).size()
                + this.getCarryoverRangeIncomeCostPlanIds(carryoverBatchNum, "1").size();
        int removeNumber = projectMilepostService.getCarryoverRangeMilepostIds(carryoverBatchNum, false).size()
                + this.getCarryoverRangeIncomeCostPlanIds(carryoverBatchNum, "0").size();
        int carryoversNumberOfMilepost = carryoversNumber;
        int removeNumberOfMilepost = removeNumber;

        dto.setCurrentPeriodTotalIncome(dto.getCurrentPeriodIncome().add(dto.getCurrentPeriodAdjustmentForIncome()));
        dto.setCarryoversNumber(carryoversNumber);
        dto.setRemoveNumber(removeNumber);
        dto.setCarryoversNumberOfMilepost(carryoversNumberOfMilepost);
        dto.setRemoveNumberOfMilepost(removeNumberOfMilepost);
        return dto;
    }

    @Override
    @Transactional
    public Boolean moveAllIn(String carryoverBatchNum, Long carryoverUserBy, SelectionRangeDto query) {
        checkExistDto(carryoverBatchNum, carryoverUserBy);
        List<SelectionRangeDto> selectionRangeDtos = this.selectionRangeList(query);
        //checkCostMethod(selectionRangeDtos);

        if (CollectionUtils.isNotEmpty(selectionRangeDtos)) {
            for (SelectionRangeDto selectionRangeDto : selectionRangeDtos) {
                moveIn(carryoverBatchNum, selectionRangeDto);
            }
        }

        carryoverBillBatchManageService.reset(carryoverUserBy, carryoverBatchNum);

        return true;
    }

    @Override
    @Transactional
    public Boolean removeAll(String carryoverBatchNum, Long carryoverUserBy, SelectionRangeDto query) {
        // 生成结转流程进度表
        checkExistDto(carryoverBatchNum, carryoverUserBy);

        // 更新里程碑未结转
        ProjectMilepostDto milepostQuery = new ProjectMilepostDto();
        milepostQuery.setCarryoverBatchNum(carryoverBatchNum);
        milepostQuery.setCarryStatus(true);
        List<ProjectMilepostDto> projectMileposts = projectMilepostService.selectList(milepostQuery);
        for (ProjectMilepostDto projectMilepost : projectMileposts) {
            projectMilepost.setCarryStatus(false);
            projectMilepostService.updateByPrimaryKeySelective(projectMilepost);
        }

        // 更新月度节点未结转
        projectIncomeCostPlanService.updateCarryStatusToNo(carryoverBatchNum);

        // 锁定用户和批次号
        carryoverBillBatchManageService.unlock(carryoverUserBy, carryoverBatchNum);

        return true;
    }

    @Override
    @Transactional
    public Boolean batchMoveIn(String carryoverBatchNum, Long carryoverUserBy, List<SelectionRangeDto> selectionRangeDtoList) {

        //checkCostMethod(selectionRangeDtoList);
        checkExistDto(carryoverBatchNum, carryoverUserBy);

        // 过滤
        filterLockBatchNum(selectionRangeDtoList, SystemContext.getUserId());

        if (CollectionUtils.isNotEmpty(selectionRangeDtoList)) {
            for (SelectionRangeDto selectionRangeDto : selectionRangeDtoList) {
                moveIn(carryoverBatchNum, selectionRangeDto);
            }
        }

        carryoverBillBatchManageService.reset(carryoverUserBy, carryoverBatchNum);

        return true;
    }

    /**
     * 收入成本方法“FAT收入百分比”与其他类型的不能在同一结转批次中
     *
     * @param selectionRangeDtoList list
     */
    private void checkCostMethod(List<SelectionRangeDto> selectionRangeDtoList) {
        List<SelectionRangeDto> collect = selectionRangeDtoList
                .stream()
                .filter(s -> !s.getCostMethod().equals("0411")) //todo : 这里的FAT收入百分比成本方法编码需要从库中获取,
                .collect(Collectors.toList());
        if (collect.size() == 0 || selectionRangeDtoList.size() != collect.size()) {
            throw new BizException(Code.ERROR, "'FAT收入百分比'与其他类型的不能在同一结转批次中!");
        }
    }

    private void moveIn(String carryoverBatchNum, SelectionRangeDto selectionRangeDto) {
        // 里程碑
        if (Objects.equals(CarryoverBatchType.MILEPOST.getCode(), selectionRangeDto.getType())) {
            ProjectMilepost projectMilepost = new ProjectMilepost();
            projectMilepost.setId(selectionRangeDto.getMilepostId());
            projectMilepost.setCarryoverBatchNum(carryoverBatchNum);
            projectMilepost.setCarryStatus(true);
            projectMilepostService.updateByPrimaryKeySelective(projectMilepost);
            // 收入成本计划
        } else if (Objects.equals(CarryoverBatchType.INCOME_COST_PLAN.getCode(), selectionRangeDto.getType())) {
            ProjectIncomeCostPlan projectIncomeCostPlan = new ProjectIncomeCostPlan();
            projectIncomeCostPlan.setId(selectionRangeDto.getMilepostId());
            projectIncomeCostPlan.setCarryoverBatchNum(carryoverBatchNum);
            projectIncomeCostPlan.setCarryStatus("1");

            projectIncomeCostPlanMapper.updateByPrimaryKeySelective(projectIncomeCostPlan);
        }
    }

    @Override
    @Transactional
    public Boolean batchRemove(String carryoverBatchNum, Long carryoverUserBy, List<SelectionRangeDto> selectionRangeDtoList) {
        checkExistDto(carryoverBatchNum, carryoverUserBy);

        if (CollectionUtils.isNotEmpty(selectionRangeDtoList)) {
            selectionRangeDtoList.forEach(selectionRangeDto -> {
                // 里程碑
                if (Objects.equals(CarryoverBatchType.MILEPOST.getCode(), selectionRangeDto.getType())) {
                    ProjectMilepost projectMilepost = projectMilepostService.selectByPrimaryKey(selectionRangeDto.getMilepostId());
                    projectMilepost.setId(selectionRangeDto.getMilepostId());
                    projectMilepost.setCarryoverBatchNum(carryoverBatchNum);
                    projectMilepost.setCarryStatus(false);
                    projectMilepostService.updateByPrimaryKey(projectMilepost);
                    // 收入成本计划
                } else if (Objects.equals(CarryoverBatchType.INCOME_COST_PLAN.getCode(), selectionRangeDto.getType())) {
                    ProjectIncomeCostPlan projectIncomeCostPlan = projectIncomeCostPlanMapper.selectByPrimaryKey(selectionRangeDto.getMilepostId());
                    projectIncomeCostPlan.setId(selectionRangeDto.getMilepostId());
                    projectIncomeCostPlan.setCarryStatus("0");
                    projectIncomeCostPlan.setCarryoverBatchNum(carryoverBatchNum);

                    projectIncomeCostPlanMapper.updateByPrimaryKey(projectIncomeCostPlan);
                }
            });
        }

        return true;
    }

    @Transactional
    @Override
    public Boolean batchRemoveClearBatchNum(String carryoverBatchNum, Long carryoverUserBy, List<SelectionRangeDto> selectionRangeDtoList) {
        checkExistDto(carryoverBatchNum, carryoverUserBy);

        this.batchRemoveClearBatchNum(carryoverBatchNum, carryoverUserBy);

        //此方法用于FAT+项目里程碑的类型,计算自动成本补差
        if (ListUtils.isNotEmpty(selectionRangeDtoList)) {
            selectionRangeDtoList.forEach(selectionRangeDto -> {
                if (CostMethod.FAT_INCOME.getCode().equals(selectionRangeDto.getCostMethod())) { //将累计收入总额,累计收入比例,本期收入金额，本期收入比例,是否最后一个节点标识 存在缓存
                    boolean isLast = checkLast(selectionRangeDto.getMilepostId()); //校验是否最后一个节点
                    selectionRangeDto.setLastMilePost(isLast);
                    selectionRangeDto.setCarryoverBatchNum(carryoverBatchNum);
                    logger.info("结转批次号：{},累计收入总额：{},累计收入总额-本位币（不含本期）：{},累计收入比例：{},本期收入金额：{},本期收入比例：{},是否最后一个节点标识：{}", carryoverBatchNum, selectionRangeDto.getAutoloandailyTotal(), selectionRangeDto.getStandardAutoloandailyTotalHistory(), selectionRangeDto.getIncomeRatioTotal(), selectionRangeDto.getIncomeForTheCurrent(), selectionRangeDto.getCurrentIncomePercent(), isLast);
                    valueCache.set(buildKey(Constants.Prefix.FAT_INCOME + selectionRangeDto.getProjectId() + ":" + carryoverBatchNum), selectionRangeDto, 1, TimeUnit.HOURS);
                }
            });
        }

        if (ListUtils.isNotEmpty(selectionRangeDtoList)) {
            selectionRangeDtoList.forEach(selectionRangeDto -> {
                Long milepostId = selectionRangeDto.getMilepostId();
                Integer type = selectionRangeDto.getType();
                carryoverBillService.removeCarryoverBill(carryoverBatchNum, milepostId, type);
            });
        }

        return true;
    }

    private boolean checkLast(Long projectMilepostId) {
        return projectMilepostService.isLastMilepost(projectMilepostId);
    }

    private String buildKey(final String key) {
        return CacheKey.getNamespace() + ":" + key;
    }

    private CarryoverBillProcessScheduleExample buildCondition(CarryoverBillProcessScheduleDto query) {
        CarryoverBillProcessScheduleExample example = new CarryoverBillProcessScheduleExample();
        CarryoverBillProcessScheduleExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(DeletedFlag.VALID.code());

        if (query.getCarryoverBatchNum() != null) {
            criteria.andCarryoverBatchNumEqualTo(query.getCarryoverBatchNum());
        }
        if (query.getCarryoverUserBy() != null) {
            criteria.andCarryoverUserByEqualTo(query.getCarryoverUserBy());
        }
        if (query.getCarryoverBillId() != null) {
            criteria.andCarryoverBillIdEqualTo(query.getCarryoverBillId());
        }

        return example;
    }

    /**
     * 检查是否已存在对应的结转进度
     *
     * @param carryoverBatchNum
     * @param carryoverUserBy
     * @return
     */
    private CarryoverBillProcessScheduleDto checkExistDto(String carryoverBatchNum, Long carryoverUserBy) {
        Asserts.notEmpty(carryoverBatchNum, ErrorCode.CTC_BATCH_NUM_NOT_NULL);

        CarryoverBillProcessScheduleDto exist = null;
        exist = getByBatchNumAndUserBy(carryoverBatchNum, carryoverUserBy);
        if (exist != null) {
            return exist;
        }
        exist = new CarryoverBillProcessScheduleDto();
        exist.setCarryoverBatchNum(carryoverBatchNum);
        UserInfo carryoverUser = CacheDataUtils.findUserById(carryoverUserBy);
        if (carryoverUser != null) {
            exist.setCarryoverUserName(carryoverUser.getName());
        }
        exist.setCarryoversNumber(0);
        return this.save(exist, carryoverUserBy);
    }

    @Override
    public Integer batchRemoveClearBatchNum(String carryoverBatchNum, Long carryoverUserBy) {
        int releaseNum = projectMilepostService.releaseCarryoverDraftStatusByBatchNum(carryoverBatchNum, carryoverUserBy);
        logger.info("结转批次号：{}，释放里程碑记录：{} 条", carryoverBatchNum, releaseNum);

        int releaseIncomeCostNum = projectIncomeCostPlanService.releaseCarryoverDraftStatusByBatchNum(carryoverBatchNum, carryoverUserBy);
        logger.info("结转批次号：{}，释放收入成本计划记录：{} 条", carryoverBatchNum, releaseIncomeCostNum);

        return releaseNum + releaseIncomeCostNum;
    }

    @Override
    public boolean cancel(String batchNum) {
        carryoverBillBatchManageService.unlock(SystemContext.getUserId(), batchNum);
        return true;
    }

}
