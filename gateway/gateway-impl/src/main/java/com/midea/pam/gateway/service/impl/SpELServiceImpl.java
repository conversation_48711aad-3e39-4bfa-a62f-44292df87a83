package com.midea.pam.gateway.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.pam.common.basedata.dto.SpELDTO;
import com.midea.pam.common.enums.DeleteFlagEnum;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.gateway.entity.SpELLog;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.mapper.SpELLogMapper;
import com.midea.pam.gateway.service.SpELService;
import com.midea.pam.system.SystemContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.web.client.RestTemplate;

import java.util.Objects;
import java.util.Optional;

public class SpELServiceImpl implements SpELService {

    private static final Logger logger = LoggerFactory.getLogger(SpELServiceImpl.class);

    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private SpELLogMapper spELLogMapper;


    @Override
    public Object getValue(SpELDTO spELDTO) throws Exception {
        SpELLog spELLog = spELDTO.getSpELLog();
        spELLog.setStatus(1);
        try {
            Guard.notNull(spELDTO.getTarget(), "动态审批分支输入参数不能为空");
            if (Objects.equals(spELDTO.getType(), 1)) {
                spELLog.setApplyNo(String.valueOf(spELDTO.getTarget()));
                Guard.notNullOrEmpty(spELDTO.getContextUrl(), "动态审批分支上下文接口不能为空");
                buildTarget(spELDTO);
                Guard.isBlank(spELLog.getErrorMsg(), spELLog.getErrorMsg());
            }
            ExpressionParser parser = new SpelExpressionParser();
            Expression expression = parser.parseExpression(spELDTO.getExpression());
            EvaluationContext context = new StandardEvaluationContext();
            // context设置jsonObject的属性值
            JSONObject jsonObject = JSONObject.parseObject(String.valueOf(spELDTO.getTarget()));
            context.setVariable("target", jsonObject);
            Object value = expression.getValue(context);
            spELLog.setAnalysis(String.valueOf(value));
            return value;
        } catch (ClassCastException e) {
            logger.error("动态审批分支SpEL输入参数格式有误：", e);
            spELLog.setStatus(0);
            spELLog.setErrorMsg(buildMessage(e.getMessage()));
            throw new MipException("动态审批分支SpEL输入参数格式有误");
        } catch (Exception e) {
            logger.error("动态审批分支SpEL表达式配置有误：", e);
            spELLog.setStatus(0);
            spELLog.setErrorMsg(buildMessage(e.getMessage()));
            throw new MipException("动态审批分支SpEL表达式配置有误");
        } finally {
            try {
                saveLog(spELDTO);
            } catch (Exception e) {
                logger.error("动态审批分支保存日志报错：", e);
            }
        }
    }

    /**
     * 根据 contextUrl 和 applyNo 查询单据详情
     *
     * @param spELDTO
     * @throws Exception
     */
    public void buildTarget(SpELDTO spELDTO) throws Exception {
        SpELLog spELLog = spELDTO.getSpELLog();
        try {
            final String url = buildUrl(spELDTO.getContextUrl(), String.valueOf(spELDTO.getTarget()));
            final String res = restTemplate.getForEntity(url, String.class).getBody();
            DataResponse<Object> objectResponse = JSON.parseObject(res, new TypeReference<DataResponse<Object>>() {
            });
            String response = JSON.toJSONString(objectResponse);
            if (objectResponse != null && objectResponse.getCode() == 0 && objectResponse.getData() != null) {
                spELDTO.setTarget(objectResponse.getData());
            } else {
                logger.error("动态审批分支上下文接口调用出现异常，响应报文：" + response);
                spELLog.setErrorMsg(String.format("上下文接口调用出现异常，响应报文：%s", buildMessage(response)));
            }
            spELLog.setContextResult(response);
        } catch (Exception e) {
            logger.error("动态审批分支上下文接口配置有误", e);
            spELLog.setErrorMsg(buildMessage(e.getMessage()));
        }
    }

    /**
     * 构建查询单据详情URL
     *
     * @param contextUrl  填写格式：模块名称/详情接口     demo：pam-ctc/purchaseContract/detail?id=
     * @param applyNo   单据号
     * @return
     */
    public String buildUrl(final String contextUrl, final String applyNo) {
        int index = contextUrl.indexOf("/");
        String prefix = contextUrl.substring(0, index);
        String api = contextUrl.substring(index + 1);
        String baseUrl = ModelsEnum.getBaseUrlByCode(prefix);
        final StringBuffer url = new StringBuffer(baseUrl).append(api).append(applyNo);
        return url.toString();
    }

    /**
     * 保存解析日志
     *
     * @param spELDTO
     */
    public void saveLog(SpELDTO spELDTO) {
        SpELLog spELLog = spELDTO.getSpELLog();
        spELLog.setCompanyId(Optional.ofNullable(spELDTO.getCompanyId()).orElse(SystemContext.getUnitId()));
        spELLog.setExpression(spELDTO.getExpression());
        spELLog.setContextUrl(spELDTO.getContextUrl());
        spELLog.setFormUrl(spELDTO.getFormUrl());
        spELLog.setFormInstanceId(spELDTO.getFormInstanceId());
        spELLog.setDeletedFlag(DeleteFlagEnum.NOT_DELETED.getBolValue());
        if (spELDTO.getIfTest() == null) {
            spELLogMapper.insert(spELLog);
        }
    }

    public String buildMessage(String message) {
        if (StringUtils.isEmpty(message)) {
            return null;
        }
        if (message.length() > 3000) {
            return message.substring(0, 3000);
        }
        return message;
    }
}
