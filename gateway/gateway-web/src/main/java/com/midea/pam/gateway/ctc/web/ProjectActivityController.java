package com.midea.pam.gateway.ctc.web;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.mcomponent.core.util.Assert;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.ProjectActivityDto;
import com.midea.pam.common.ctc.excelVo.ProjectActivityExcelVo;
import com.midea.pam.common.ctc.vo.FeeItemFeeSettingModeDropdownBoxVO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api("项目活动")
@RestController
@RequestMapping("projectActivity")
public class ProjectActivityController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "查询项目活动")
    @GetMapping("findList")
    public Response findList(@ApiParam("编码") @RequestParam(required = false) String code,
                             @ApiParam("类别名称") @RequestParam(required = false) String name,
                             @ApiParam("类别描述") @RequestParam(required = false) String description,
                             @ApiParam("备注") @RequestParam(required = false) String remark,
                             @ApiParam("预算事项") @RequestParam(required = false) String budgetMattersStateStr,
                             @ApiParam("类别属性") @RequestParam(required = false) String typeStr,
                             @ApiParam("是否父级") @RequestParam(required = false) String parentStateStr,
                             @ApiParam("层级") @RequestParam(required = false) Long level) {
        Map<String, Object> param = new HashMap<>(10);
        param.put("code", code);
        param.put("name", name);
        param.put("description", description);
        param.put("remark", remark);
        param.put("budgetMattersStateStr", budgetMattersStateStr);
        param.put("typeStr", typeStr);
        param.put("parentStateStr", parentStateStr);
        param.put("level", level);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "projectActivity/findList", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<ProjectActivityDto>>>() {
        });
    }

    @ApiOperation(value = "查询层级")
    @GetMapping("findLevelList")
    public Response findLevelList(@ApiParam("组织id") @RequestParam Long unitId) {
        Map<String, Object> param = new HashMap<>(4);
        param.put("unitId", unitId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "projectActivity/findLevelList", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<String>>>() {
        });
    }

    @ApiOperation(value = "保存项目活动")
    @PostMapping("save")
    public Response save(@RequestBody List<ProjectActivityDto> projectActivityDtoList) {
        String url = String.format("%sprojectActivity/save", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, projectActivityDtoList, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
    }

    /**
     * 导出
     *
     * @param response
     */
    @GetMapping("export")
    public void export(HttpServletResponse response) {
        Map<String, Object> param = new HashMap<>(5);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "projectActivity/export", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<ProjectActivityExcelVo>> dataResponse = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<List<ProjectActivityExcelVo>>>() {
                });
        List<ProjectActivityExcelVo> dataList = dataResponse.getData();

        Assert.notEmpty(dataList, "无数据");
        //导出操作
        ExportExcelUtil.exportExcel(dataList,
                null,
                "Sheet1",
                ProjectActivityExcelVo.class,
                "项目活动事项维护.xls",
                response);
    }

    @ApiOperation(value = "费用类型设置方式”为项目活动事项方式时,查询项目活动事项数据")
    @PostMapping("feeItemJoinQuery")
    public Response feeItemJoinQueryProjectActivity(@RequestBody(required = false) FeeItemFeeSettingModeDropdownBoxVO param) {
        String url = String.format("%sprojectActivity/feeItemJoinQuery", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, param, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<FeeItemFeeSettingModeDropdownBoxVO>>>() {
        });
    }

    @ApiOperation(value = "根据编码模糊查询")
    @GetMapping("findByCode")
    public Response findByCode(
            @RequestParam(required = false) String code,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) Boolean isBudget){
        String url = ModelsEnum.CTC.getBaseUrl() + "projectActivity/findByCode";
        url += "?code="+(code!=null?code:"");
        url += "&type="+(type!=null?type:"");
        url += "&isBudget="+(isBudget!=null?isBudget:"");
        return restTemplate.getForObject(url,DataResponse.class);
    }
}
