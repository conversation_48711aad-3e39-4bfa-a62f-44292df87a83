package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.entity.EsbMassQueryRecord;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@RestController
@Api("接口日志")
@RequestMapping(value = {"esbMassQueryRecord","mobile/app/esbMassQueryRecord"})
public class EsbMassQueryRecordController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation("新增接口日志")
    @PostMapping({"add"})
    public Response add(@RequestBody EsbMassQueryRecord esbMassQueryRecord) {
        String url = String.format("%sesbMassQueryRecord/add",ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, esbMassQueryRecord, String.class);
        DataResponse<Integer> response = JSON.parseObject(cleanStr(responseEntity.getBody()),
                new TypeReference<DataResponse<Integer>>(){});
        return response;
    }

    @ApiOperation("修改接口日志")
    @PostMapping({"update"})
    public Response update(@RequestBody EsbMassQueryRecord esbMassQueryRecord) {
        String url = String.format("%sesbMassQueryRecord/update",ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, esbMassQueryRecord, String.class);
        DataResponse<Integer> dataResponse =  JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<Integer>>(){});
        return dataResponse;
    }

    @ApiOperation("删除接口日志")
    @GetMapping({"delete"})
    public Response delete(@RequestParam Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/esbMassQueryRecord/delete", param);
        DataResponse<String> response = Response.dataResponse();
        response.setData(restTemplate.getForObject(url, String.class));
        return response;
    }
}
