package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.constants.WbsBudgetFieldConstant;
import com.midea.pam.common.ctc.cache.ProjectActivityCache;
import com.midea.pam.common.ctc.cache.WbsTemplateRuleCache;
import com.midea.pam.common.ctc.dto.FeeItemExpenseTypeDto;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.dto.ProjectWbsBudgetDto;
import com.midea.pam.common.ctc.dto.ProjectWbsBudgetObjectDto;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.excelVo.ProjectWbsBudgetImportResponseExcelVO;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.FeeSettingModeEnum;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.BeanMapTool;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExcelUtil;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.ProjectWbsBudgetUtils;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.utils.FileUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFPalette;
import org.apache.poi.hssf.usermodel.HSSFRichTextString;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.ss.util.CellRangeAddress;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


@Api("项目wbs预算")
@RestController
@RequestMapping({"projectWbsBudget", "mobile/app/projectWbsBudget"})
public class ProjectWbsBudgetController extends ControllerHelper {

    private Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    private RestTemplate restTemplate;

    /**
     * json: {
     * - wbsTemplateInfoId : wbs模板信息id
     * - data : [{
     * - - projectCode : 项目编码
     * - - field_1 : 动态列
     * - - field_2 : 动态列
     * - - field_1 : 动态列
     * - - description : 描述
     * - - activityOrderNo ： 活动事项序号
     * - - activityCode : 活动事项编码
     * - - activityName : 活动类别名称
     * - - activityType : 活动类别属性
     * - - price : 预算金额
     * - }]
     * }
     */
    @ApiOperation(value = "新建项目-wbs预算汇总")
    @PostMapping("summaryWbsBudget")
    public Response summaryWbsBudget(@RequestBody Map map) {
        String url = String.format("%sprojectWbsBudget/summaryWbsBudget", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, map, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<Map>>>() {
        });
    }

    @ApiOperation(value = "新建项目-活动事项预算汇总")
    @PostMapping("summaryProjectActivityBudget")
    public Response summaryProjectActivityBudget(@RequestBody Map map) {
        String url = String.format("%sprojectWbsBudget/summaryProjectActivityBudget", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, map, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<Map>>>() {
        });
    }

    @ApiOperation(value = "获取wbs预算关联信息")
    @GetMapping("findWbsBudgetInfo")
    public Response findWbsBudgetInfo(@RequestParam Long projectId) {
        Map<String, Object> map = new HashMap();
        map.put("projectId", projectId);
        final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "projectWbsBudget/findWbsBudgetInfo", map);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<ProjectWbsBudgetObjectDto>>() {
        });
    }

    @ApiOperation(value = "WBS预算信息导入-模板下载")
    @GetMapping("exportTemplate")
    public void exportTemplate(HttpServletResponse response, @RequestParam @ApiParam("wbsTemplateInfoId") Long wbsTemplateInfoId) {

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("WBS预算导入模板_" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
        fileName.append(".xls");

        // 创建新的Excel 工作簿
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet("项目wbs预算导入模板");

        // 构建模板
        buildBatchNewTemplate(fileName.toString(), response, workbook, sheet, wbsTemplateInfoId);

        ExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    private void buildBatchNewTemplate(String fileName, HttpServletResponse response, HSSFWorkbook workbook, HSSFSheet sheet, Long wbsTemplateInfoId) {
        // 设置字体
        HSSFFont font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 10);
        font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
        // 设置样式
        HSSFCellStyle cellStyle0 = getHeaderCellStyle(workbook, (short) 10, (byte) 128, (byte) 128, (byte) 128);
        HSSFCellStyle cellStyle1 = getHeaderCellStyle(workbook, (short) 11, (byte) 237, (byte) 125, (byte) 49);
        HSSFCellStyle cellStyle2 = getHeaderCellStyle(workbook, (short) 12, (byte) 217, (byte) 217, (byte) 217);
        HSSFCellStyle cellStyle3 = getHeaderCellStyle(workbook, (short) 13, (byte) 255, (byte) 242, (byte) 204);
        cellStyle0.setFont(font);
        cellStyle1.setFont(font);
        // 在索引0的位置创建行（最顶端的行）
        sheet.createFreezePane(0, 3, 0, 3); //固定表头

        // 获取符合条件的wbs缓存
        List<WbsTemplateRuleCache> wbsCaches = ProjectWbsBudgetUtils.getEligibilityWbsCache(wbsTemplateInfoId);
        // 获取符合条件的活动事项缓存
        List<ProjectActivityCache> activityCaches = ProjectWbsBudgetUtils.getEligibilityActivityCache();

        if (ListUtils.isEmpty(wbsCaches) || ListUtils.isEmpty(activityCaches)) {
            ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
            return;
        }
        int wbsSize = wbsCaches.size();
        int activitySize = activityCaches.size();
        // 第一行 标题
        HSSFRow row0 = ExportExcelUtil.createCell(sheet, "wbs", 0, 0, cellStyle0);
        ExportExcelUtil.createCell(row0, "项目活动事项", wbsSize, cellStyle1);
        // 合并单元格
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, wbsSize - 1));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, wbsSize, wbsSize + activitySize - 1));
        // 第二行 活动事项名称
        HSSFRow row1 = ExportExcelUtil.createCell(sheet, "", 1, 0, cellStyle2);
        // 第三行 wbs+活动事项code
        HSSFRow row2 = ExportExcelUtil.createCell(sheet, "", 2, 0, cellStyle2);

        for (int i = 0; i < wbsSize; i++) {
            // 设置单元格长度
            sheet.setColumnWidth(wbsSize + i, wbsCaches.get(i).getRuleName().length() * 1000);
            ExportExcelUtil.createCell(row1, null, i, cellStyle2);
            ExportExcelUtil.createCell(row2, wbsCaches.get(i).getRuleName(), i, cellStyle2);
        }

        for (int i = 0; i < activitySize; i++) {
            // 设置单元格长度
            sheet.setColumnWidth(wbsSize + i, activityCaches.get(i).getCode().length() * 900);
            ExportExcelUtil.createCell(row1, activityCaches.get(i).getName(), wbsSize + i, cellStyle3);
            ExportExcelUtil.createCell(row2, activityCaches.get(i).getCode(), wbsSize + i, cellStyle3);
        }

        //单元格设为文本格式，默认10000行
        HSSFCellStyle textStyle = workbook.createCellStyle();
        textStyle.setDataFormat(workbook.createDataFormat().getFormat("@")); //文本格式
        for (int i = 3; i < 10000; i++) {
            HSSFRow row = sheet.createRow(i);
            for (int j = 0; j < wbsSize + activitySize; j++) {
                HSSFCell cell = row.createCell(j);
                cell.setCellStyle(textStyle);
            }
        }
    }

    private HSSFCellStyle getHeaderCellStyle(HSSFWorkbook workbook, short index, byte red, byte green, byte blue) {
        HSSFPalette palette = workbook.getCustomPalette(); //调色板实例
        palette.setColorAtIndex(index, red, green, blue);
        HSSFColor color = palette.findColor(red, green, blue);
        // 设置字体
        HSSFFont boldFont = workbook.createFont();
        boldFont.setFontName("宋体");
        boldFont.setFontHeightInPoints((short) 10);

        HSSFCellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        cellStyle.setFillForegroundColor(color.getIndex());
        cellStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);//水平居中
        cellStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);//垂直居中
        cellStyle.setFont(boldFont);
        cellStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        cellStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);
        cellStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        cellStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);
        cellStyle.setLeftBorderColor(HSSFColor.BLACK.index);
        cellStyle.setRightBorderColor(HSSFColor.BLACK.index);
        cellStyle.setBottomBorderColor(HSSFColor.BLACK.index);
        cellStyle.setTopBorderColor(HSSFColor.BLACK.index);
        return cellStyle;
    }

    @ApiOperation(value = "WBS预算批量导入检查", notes = "版本v2")
    @PostMapping("v2/checkImportWbsBudgetFromExcel")
    public Response checkWbsBudgetFromExcel(@ApiParam("附件") @RequestParam(value = "file") MultipartFile file,
                                            @ApiParam("WBS预算数据") @RequestParam(name = "data") String data,
                                            HttpServletResponse response) {
        Map<String, String> dataMap = (Map) JSON.parseObject(data);
        Long wbsTemplateInfoId = MapUtils.getLong(dataMap, WbsBudgetFieldConstant.WBS_TEMPLATE_INFO_ID);
        String projectCode = MapUtils.getString(dataMap, WbsBudgetFieldConstant.PROJECT_CODE);
        String projectName = MapUtils.getString(dataMap, WbsBudgetFieldConstant.PROJECT_NAME);
        String batchCode = MapUtils.getString(dataMap, "batchCode");

        // 获取有效的activity活动事项 预算事项=是 当前时间<结束时间
        List<ProjectActivityCache> projectActivityCaches = ProjectWbsBudgetUtils.getEligibilityActivityCache();

        // 获取有效的wbs动态列
        List<WbsTemplateRuleCache> wbsTemplateRuleCaches = ProjectWbsBudgetUtils.getEligibilityWbsCache(wbsTemplateInfoId);

        // excel有效数据
        List<Map> excelMapList;
        try {
            // 从excel第3行开始读，第3行是activity活动事项编码列
            List<List<String>> wbsExcelRows = FileUtil.importExcel(file, 0, 2, wbsTemplateRuleCaches.size() + projectActivityCaches.size());
            // 校验wbs模板导入title有效性
            validityImportWbsBudgetTitle(wbsExcelRows.get(0), wbsTemplateRuleCaches, projectActivityCaches);

            excelMapList = ProjectWbsBudgetUtils.wbsExcelRows2Map(wbsExcelRows,
                    projectActivityCaches,
                    wbsTemplateRuleCaches,
                    batchCode,
                    projectName,
                    projectCode);
        } catch (BizException bizException) {
            // 自定义内容不能被Exception拦截
            throw bizException;
        } catch (Exception e) {
            logger.error("wbs导入异常:" + e.getMessage(), e);
            throw new BizException(ErrorCode.CTC_PROJECT_WBS_BUDGET_NOT_TRUE);
        }
        if (CollectionUtils.isEmpty(excelMapList)) {
            throw new ApplicationBizException("当前导入Excel模板中无法匹配有效的记录，请检查");
        }

        ProjectWbsBudgetImportResponseExcelVO importResponseExcelVO = new ProjectWbsBudgetImportResponseExcelVO();
        importResponseExcelVO.setExcelMapList(excelMapList);

        final String url = String.format("%sprojectWbsBudget/v2/checkWbsBudgetFromExcel/batchCode/" + wbsTemplateInfoId, ModelsEnum.CTC.getBaseUrl());
        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(6000 * 1000);
        httpRequestFactory.setConnectTimeout(6000 * 1000);
        httpRequestFactory.setReadTimeout(6000 * 1000);
        restTemplate.setRequestFactory(httpRequestFactory);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, importResponseExcelVO, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<ProjectWbsBudgetImportResponseExcelVO>>() {
        });
    }

    /**
     * 校验wbs模板导入title有效性
     *
     * @param wbsExcelRows
     * @param wbsTemplateRuleCaches
     * @param projectActivityCaches
     */
    public static void validityImportWbsBudgetTitle(List<String> wbsExcelRows, List<WbsTemplateRuleCache> wbsTemplateRuleCaches, List<ProjectActivityCache> projectActivityCaches) {
        if (CollectionUtils.isEmpty(wbsExcelRows)) {
            throw new ApplicationBizException("导入模板有误，请检查");
        }

        for (int i = 0; i < wbsExcelRows.size(); i++) {
            if (i < wbsTemplateRuleCaches.size()) {
                if (!StringUtils.equals(wbsTemplateRuleCaches.get(i).getRuleName(), wbsExcelRows.get(i))) {
                    throw new ApplicationBizException("导入模板内容有更新，请下载最新模板");
                }
            } else {
                if (!StringUtils.equals(projectActivityCaches.get(i - wbsTemplateRuleCaches.size()).getCode(), wbsExcelRows.get(i))) {
                    throw new ApplicationBizException("导入模板内容有更新，请下载最新模板");
                }
            }
        }
    }

    @ApiOperation(value = "WBS预算批量导入下载报错文件", notes = "版本v2")
    @PostMapping("v2/downloadErrorWbsBudgetFromExcel")
    public void downloadErrorWbsBudgetFromExcel(@ApiParam("附件") @RequestParam(value = "file") MultipartFile file,
                                                @ApiParam("WBS预算数据") @RequestParam(name = "data") String data,
                                                HttpServletResponse response) throws IOException {
        Map<String, String> dataMap = (Map) JSON.parseObject(data);
        Long wbsTemplateInfoId = MapUtils.getLong(dataMap, WbsBudgetFieldConstant.WBS_TEMPLATE_INFO_ID);
        String projectCode = MapUtils.getString(dataMap, WbsBudgetFieldConstant.PROJECT_CODE);
        String projectName = MapUtils.getString(dataMap, WbsBudgetFieldConstant.PROJECT_NAME);
        String batchCode = MapUtils.getString(dataMap, "batchCode");

        // 获取有效的activity活动事项 预算事项=是 当前时间<结束时间
        List<ProjectActivityCache> projectActivityCaches = ProjectWbsBudgetUtils.getEligibilityActivityCache();
        // 获取有效的wbs动态列
        List<WbsTemplateRuleCache> wbsTemplateRuleCaches = ProjectWbsBudgetUtils.getEligibilityWbsCache(wbsTemplateInfoId);

        // excel有效数据
        List<Map> excelMapList;
        try {
            // 从excel第3行开始读，第3行是activity活动事项编码列
            List<List<String>> wbsExcelRows = FileUtil.importExcel(file, 0, 2, wbsTemplateRuleCaches.size() + projectActivityCaches.size());
            // 校验wbs模板导入title有效性
            validityImportWbsBudgetTitle(wbsExcelRows.get(0), wbsTemplateRuleCaches, projectActivityCaches);

            excelMapList = ProjectWbsBudgetUtils.wbsExcelRows2Map(wbsExcelRows,
                    projectActivityCaches,
                    wbsTemplateRuleCaches,
                    batchCode,
                    projectName,
                    projectCode);
        } catch (BizException bizException) {
            // 自定义内容不能被Exception拦截
            throw bizException;
        } catch (Exception e) {
            logger.error("wbs导入异常:" + e.getMessage(), e);
            throw new BizException(ErrorCode.CTC_PROJECT_WBS_BUDGET_NOT_TRUE);
        }
        if (CollectionUtils.isEmpty(excelMapList)) {
            throw new ApplicationBizException("当前导入Excel模板中无法匹配有效的记录，请检查");
        }

        ProjectWbsBudgetImportResponseExcelVO importResponseExcelVO = new ProjectWbsBudgetImportResponseExcelVO();
        importResponseExcelVO.setExcelMapList(excelMapList);

        final String url = String.format("%sprojectWbsBudget/v2/checkWbsBudgetFromExcel/batchCode/" + wbsTemplateInfoId, ModelsEnum.CTC.getBaseUrl());
        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(6000 * 1000);
        httpRequestFactory.setConnectTimeout(6000 * 1000);
        httpRequestFactory.setReadTimeout(6000 * 1000);
        restTemplate.setRequestFactory(httpRequestFactory);
        String res = restTemplate.postForEntity(url, importResponseExcelVO, String.class).getBody();
        Map<String, Object> resultMap = JSON.parseObject(res);

        Map<String, Object> resultDataMap = JSON.parseObject(String.valueOf(resultMap.get("data")));
        JSONArray jsonArray = JSON.parseArray(String.valueOf(resultDataMap.get("errorMsg")));
        if (jsonArray != null && jsonArray.size() > 0) {
            HSSFWorkbook workbook = new HSSFWorkbook(file.getInputStream());
            Sheet sheetAt = workbook.createSheet("报错信息");
            List<Pair<Integer, Integer>> pairList = new ArrayList<Pair<Integer, Integer>>();
            // 创建表头
            for (int i = 0; i < jsonArray.size(); i++) {
                sheetAt.setColumnWidth(0, 10000);
                Row row = sheetAt.createRow(i);
                Cell cell = row.createCell(0);
                HSSFRichTextString textString = new HSSFRichTextString("导入校验信息：" + jsonArray.get(i));
                HSSFCellStyle cellStyle = workbook.createCellStyle();
                cellStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
                cellStyle.setWrapText(true);
                cell.setCellStyle(cellStyle);
                HSSFFont font = workbook.createFont();
                font.setColor(HSSFColor.RED.index);
                textString.applyFont("批量导入校验信息：".length(), textString.length(), font);
                cell.setCellValue(textString);
                buildColorList(pairList, jsonArray.getString(i));
            }
            //chenchong 标红
            if (CollectionUtils.isNotEmpty(pairList) && pairList.size() > 0) {
                for (Pair<Integer, Integer> pair : pairList) {
                    Sheet sheet = workbook.getSheetAt(0);
                    Row firstRow = sheet.getRow(pair.getKey() - 1);
                    Cell titleCell = firstRow.getCell(pair.getValue() - 1);
                    if (Objects.nonNull(titleCell)) {
                        HSSFCellStyle style = getHeaderCellStyle(workbook, IndexedColors.DARK_RED.index, (byte) 255, (byte) 0, (byte) 0);
                        titleCell.setCellStyle(style);
                    }
                }
            }
            // 导入下载文件名称乱码
            ExportExcelUtil.downLoadExcel("报错信息_" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xls", response, workbook);
        }
    }


    private void buildColorList(List<Pair<Integer, Integer>> pairList, String str) {
        String[] split = str.split("，");
        Matcher matcher = Pattern.compile("([0-9]+)").matcher(split[0]);
        List<Integer> list = new LinkedList<Integer>();
        while (matcher.find()) {
            list.add(Integer.parseInt(matcher.group().trim()));
        }
        if (list.size() > 1) {
            Pair<Integer, Integer> pair = new ImmutablePair<>(list.get(0), list.get(1));
            pairList.add(pair);
        }
    }

    @ApiOperation(value = "WBS预算批量导入", notes = "版本v2")
    @PostMapping("v2/importWbsBudgetFromExcel")
    public Response importWbsBudgetFromExcelV2(@ApiParam("附件") @RequestParam(value = "file") MultipartFile file,
                                               @ApiParam("WBS预算数据") @RequestParam(name = "data") String data,
                                               HttpServletResponse response) {
        Map<String, String> dataMap = (Map) JSON.parseObject(data);
        // wbs模板id
        Long wbsTemplateInfoId = MapUtils.getLong(dataMap, WbsBudgetFieldConstant.WBS_TEMPLATE_INFO_ID);
        if (Objects.isNull(wbsTemplateInfoId)) {
            throw new ApplicationBizException(String.format("参数%s不能为空", WbsBudgetFieldConstant.WBS_TEMPLATE_INFO_ID));
        }
        // 项目编码
        String projectCode = MapUtils.getString(dataMap, WbsBudgetFieldConstant.PROJECT_CODE);
        if (StringUtils.isBlank(projectCode)) {
            throw new ApplicationBizException(String.format("参数%s不能为空", WbsBudgetFieldConstant.PROJECT_CODE));
        }
        // 项目名称
        String projectName = MapUtils.getString(dataMap, WbsBudgetFieldConstant.PROJECT_NAME);
        if (StringUtils.isBlank(projectName)) {
            throw new ApplicationBizException(String.format("参数%s不能为空", WbsBudgetFieldConstant.PROJECT_NAME));
        }
        // 批次号
        String batchCode = MapUtils.getString(dataMap, "batchCode");
        if (StringUtils.isBlank(batchCode)) {
            throw new ApplicationBizException(String.format("参数%s不能为空", "batchCode"));
        }
        // 导入方式（ADD：增量导入、COVER：覆盖导入）
        String type = MapUtils.getString(dataMap, WbsBudgetFieldConstant.TYPE);
        if (StringUtils.isBlank(type)) {
            throw new ApplicationBizException(String.format("参数%s不能为空", "type"));
        }
        // 项目类型
        Long projectType = MapUtils.getLong(dataMap, WbsBudgetFieldConstant.PROJECT_TYPE);
        if (Objects.isNull(projectType)) {
            throw new ApplicationBizException(String.format("参数%s不能为空", WbsBudgetFieldConstant.PROJECT_TYPE));
        }

        ProjectWbsBudgetImportResponseExcelVO importResponseExcelVO = new ProjectWbsBudgetImportResponseExcelVO();
        String wbsBudgetStr = MapUtils.getString(dataMap, "wbsBudgetList");
        String wbsBaselineStr = MapUtils.getString(dataMap, "wbsBudgetBaselineList");

        Type fastJsonType = new TypeReference<List<HashMap<String, Object>>>() {
        }.getType();

        // 预算
        importResponseExcelVO.setPageBudgetMapList(StringUtils.isBlank(wbsBudgetStr) ? new ArrayList<>() : JSON.parseArray(wbsBudgetStr).toJavaObject(fastJsonType));
        // 基线
        importResponseExcelVO.setPageBaselineMapList(StringUtils.isBlank(wbsBaselineStr) ? new ArrayList<>() : JSON.parseArray(wbsBaselineStr).toJavaObject(fastJsonType));

        // 获取有效的activity活动事项 预算事项=是 当前时间<结束时间
        List<ProjectActivityCache> projectActivityCaches = ProjectWbsBudgetUtils.getEligibilityActivityCache();
        // 获取有效的wbs动态列
        List<WbsTemplateRuleCache> wbsTemplateRuleCaches = ProjectWbsBudgetUtils.getEligibilityWbsCache(wbsTemplateInfoId);

        // excel有效数据
        List<Map> excelMapList;
        try {
            // 从excel第3行开始读，第3行是activity活动事项编码列
            List<List<String>> wbsExcelRows = FileUtil.importExcel(file, 0, 2, wbsTemplateRuleCaches.size() + projectActivityCaches.size());
            // 校验wbs模板导入title有效性
            validityImportWbsBudgetTitle(wbsExcelRows.get(0), wbsTemplateRuleCaches, projectActivityCaches);

            excelMapList = ProjectWbsBudgetUtils.wbsExcelRows2Map(wbsExcelRows,
                    projectActivityCaches,
                    wbsTemplateRuleCaches,
                    batchCode,
                    projectName,
                    projectCode);
        } catch (BizException bizException) {
            // 自定义内容不能被Exception拦截
            throw bizException;
        } catch (Exception e) {
            logger.error("wbs导入异常:" + e.getMessage(), e);
            throw new BizException(ErrorCode.CTC_PROJECT_WBS_BUDGET_NOT_TRUE);
        }
        if (CollectionUtils.isEmpty(excelMapList)) {
            throw new ApplicationBizException("当前导入Excel模板中无法匹配有效的记录，请检查");
        }
        importResponseExcelVO.setExcelMapList(excelMapList);
        importResponseExcelVO.setWbsTemplateInfoId(wbsTemplateInfoId);
        importResponseExcelVO.setProjectType(projectType);
        importResponseExcelVO.setBatchCode(batchCode);
        importResponseExcelVO.setImportType(type);

        final String url = String.format("%sprojectWbsBudget/v2/importWbsBudgetFromExcel", ModelsEnum.CTC.getBaseUrl());
        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(6000 * 1000);
        httpRequestFactory.setConnectTimeout(6000 * 1000);
        httpRequestFactory.setReadTimeout(6000 * 1000);
        restTemplate.setRequestFactory(httpRequestFactory);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, importResponseExcelVO, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse>() {
        });
    }

    @ApiOperation(value = "批量修改预算金额-模板下载", notes = "场景：WBS预算变更")
    @GetMapping("/batchUpdate/exportTemplate")
    public void exportTemplateUpdate(@RequestParam Long projectId, HttpServletResponse servletResponse) {

        /* 查询项目 */
        Map<String, Object> projectParamMap = new HashMap();
        projectParamMap.put("id", projectId);
        String url1 = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/findOnlyProjectById", projectParamMap);
        String res1 = restTemplate.getForEntity(url1, String.class).getBody();
        DataResponse<ProjectDto> projectResponse = JSON.parseObject(res1, new TypeReference<DataResponse<ProjectDto>>() {
        });
        ProjectDto projectDto = projectResponse.getData();

        /* 查询wbs预算 */
        Map<String, Object> paramMap = new HashMap();
        paramMap.put("projectId", projectId);
        String url2 = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/projectWbsBudget/findDetailWbsBudget", paramMap);
        String res2 = restTemplate.getForEntity(url2, String.class).getBody();
        DataResponse<List<ProjectWbsBudgetDto>> response = JSON.parseObject(res2, new TypeReference<DataResponse<List<ProjectWbsBudgetDto>>>() {
        });
        // wbs预算数据
        List<ProjectWbsBudgetDto> projectWbsBudgetDtoList = response.getData();
        if (CollectionUtils.isEmpty(projectWbsBudgetDtoList)) {
            throw new BizException(ErrorCode.CTC_PROJECT_WBS_BUDGET_NOT_FOUND);
        }
        projectWbsBudgetDtoList.removeIf(s -> Objects.equals(s.getActivityCode(), "System"));
        projectWbsBudgetDtoList.sort(Comparator.comparing(ProjectWbsBudgetDto::getWbsFullCode));

        try {
            downLoadBatchUpdateTemplate(projectDto, projectWbsBudgetDtoList, servletResponse);
        } catch (Exception e) {
            throw new BizException(ErrorCode.ERROR, "模板导出异常");
        }
    }

    private void downLoadBatchUpdateTemplate(Project project, List<ProjectWbsBudgetDto> projectWbsBudgetDtoList, HttpServletResponse servletResponse) {
        /* 导出Excel配置 */
        String fileName = String.format("%s批量修改预算金额%s%s", project.getCode(), DateUtils.format(new Date(), "yyyyMMddHHmmss"), ".xls");
        Workbook workbook = ExcelUtil.createWorkBook(fileName);
        Sheet sheet = ExcelUtil.createSheet(workbook, "sheet1");
        CellStyle titleStyle = ExcelUtil.creatTitleStyle(workbook);
        CellStyle style = ExcelUtil.creatCellStyle(workbook);
        CellStyle cellStyle0 = ExportExcelUtil.getCellStyle(workbook, HSSFCellStyle.ALIGN_CENTER, null, IndexedColors.GREY_25_PERCENT.getIndex(), Boolean.TRUE, Boolean.TRUE);
        CellStyle cellStyle1 = ExportExcelUtil.getCellStyle(workbook, HSSFCellStyle.ALIGN_CENTER, null, IndexedColors.YELLOW.getIndex(), Boolean.TRUE, Boolean.TRUE);

        // wbs预算组装序号
        List<Map<String, Object>> dataMaps = ProjectWbsBudgetDto.dto2MapBatch(projectWbsBudgetDtoList);
        for (int i = 0; i < dataMaps.size(); i++) {
            Map<String, Object> currentMap = dataMaps.get(i);
            if (StringUtils.isNotBlank(MapUtils.getString(currentMap, WbsBudgetFieldConstant.FEE_TYPE_NAME))) {
                // 能匹配到，boolean转字典
                currentMap.put(WbsBudgetFieldConstant.FEE_SYNC_EMS, Boolean.TRUE.equals(MapUtils.getBoolean(currentMap, WbsBudgetFieldConstant.FEE_SYNC_EMS)) ? "是" : "否");
            } else {
                // 匹配不到，不返回内容
                currentMap.remove(WbsBudgetFieldConstant.FEE_SYNC_EMS);
            }
            currentMap.put(WbsBudgetFieldConstant.ORDER_NO, i + 1);
        }
        // wbs动态列
        List<WbsTemplateRuleCache> wbsCaches = ProjectWbsBudgetUtils.getEligibilityWbsCache(project.getWbsTemplateInfoId());

        /* 导出数据JsonArray格式 */
        JSONArray dataJsonArray = JSONArray.parseArray(JSON.toJSONString(dataMaps));

        /* 标题 */
        LinkedHashMap<String, String> titleMap = new LinkedHashMap<>();
        titleMap.put(WbsBudgetFieldConstant.ORDER_NO, "序号");
        titleMap.put(WbsBudgetFieldConstant.PROJECT_CODE, "项目");
        if (CollectionUtils.isNotEmpty(wbsCaches)) {
            for (WbsTemplateRuleCache wbsCache : wbsCaches) {
                titleMap.put(wbsCache.getKey(), wbsCache.getRuleName());
            }
        }
        titleMap.put(WbsBudgetFieldConstant.WBS_SUMMARY_CODE, "WBS号");
        titleMap.put(WbsBudgetFieldConstant.DESCRIPTION, "描述");
        titleMap.put(WbsBudgetFieldConstant.ACTIVITY_CODE, "活动事项编码");
        titleMap.put(WbsBudgetFieldConstant.ACTIVITY_NAME, "类别名称");
        titleMap.put(WbsBudgetFieldConstant.ACTIVITY_TYPE, "类别属性");
        titleMap.put(WbsBudgetFieldConstant.FEE_TYPE_NAME, "经济事项");
        titleMap.put(WbsBudgetFieldConstant.FEE_SYNC_EMS, "是否同步EMS");
        titleMap.put(WbsBudgetFieldConstant.PRICE, "预算金额");
        titleMap.put(WbsBudgetFieldConstant.BASELINE_COST, "预算基线");
        titleMap.put(WbsBudgetFieldConstant.DEMAND_COST, "需求预算");
        titleMap.put(WbsBudgetFieldConstant.ON_THE_WAY_COST, "在途成本");
        titleMap.put(WbsBudgetFieldConstant.INCURRED_COST, "已发生成本");
        titleMap.put(WbsBudgetFieldConstant.REMAINING_COST, "剩余可用预算");
        titleMap.put(WbsBudgetFieldConstant.CHANGE_ACCUMULATE_COST, "累计变更金额");
        titleMap.put(WbsBudgetFieldConstant.AFTER_CHANGE_PRICE, "变更后预算金额（无变化不用填）");

        ExcelUtil.setExcelData(sheet, dataJsonArray, titleMap, null, null, null, titleStyle, style, 1);

//      ===================== 前面逻辑绝大部分引用自接口【projectWbsBudget/v1/exportByProjectId】 =====================
        // 列数
        int lastColNum = titleMap.size() - 1;

        // 设置表头
        ArrayList<String> arrayList = new ArrayList<>();
        arrayList.add("填写说明：");
        arrayList.add("1、灰色底色标题为现有预算明细内容，请勿修改");
        arrayList.add("2、黄色底色标题为变更后预算金额，没有修改不用填写，留空即可。");
        String content = String.join("\n", arrayList);
        Row row0 = sheet.createRow(0);
        row0.createCell(0).setCellValue(content);
        row0.setHeightInPoints(50);
        //创建一个单元格样式，用于设置换行
        CellStyle headStyle = workbook.createCellStyle();
        headStyle.setWrapText(true); // 启用自动换行
        row0.getCell(0).setCellStyle(headStyle); // 应用样式
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, lastColNum)); // 合并单元格

        // 标题设置样式
        Row row1 = sheet.getRow(1);
        for (int colNum = 0; colNum <= lastColNum; colNum++) {
            row1.getCell(colNum).setCellStyle(cellStyle0);
            if (colNum == lastColNum) {
                row1.getCell(colNum).setCellStyle(cellStyle1);
            }
        }
        row1.setHeightInPoints(15);

        // 设置列宽
        sheet.setColumnWidth(lastColNum, 8000);

        // 固定表头
        sheet.createFreezePane(0, 2, 0, 2);

        ExcelUtil.downLoadExcel(fileName, servletResponse, workbook);
    }

    @ApiOperation(value = "批量修改预算金额-导入模板", notes = "场景：WBS预算变更")
    @PostMapping("/batchUpdate/checkTemplate")
    public Response checkTemplateUpdate(@RequestParam MultipartFile file, @RequestParam Long projectId) {
        DataResponse<Map<String, Object>> response = Response.dataResponse();

        /* 查询项目 */
        Map<String, Object> projectParamMap = new HashMap();
        projectParamMap.put("id", projectId);
        String url1 = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/findOnlyProjectById", projectParamMap);
        String res1 = restTemplate.getForEntity(url1, String.class).getBody();
        DataResponse<ProjectDto> projectResponse = JSON.parseObject(res1, new TypeReference<DataResponse<ProjectDto>>() {
        });
        ProjectDto projectDto = projectResponse.getData();

        /* 查询wbs预算 */
        Map<String, Object> paramMap = new HashMap();
        paramMap.put("projectId", projectId);
        String url2 = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/projectWbsBudget/findDetailWbsBudget", paramMap);
        String res2 = restTemplate.getForEntity(url2, String.class).getBody();
        DataResponse<List<ProjectWbsBudgetDto>> budgetResponse = JSON.parseObject(res2, new TypeReference<DataResponse<List<ProjectWbsBudgetDto>>>() {
        });
        List<ProjectWbsBudgetDto> projectWbsBudgetDtoList = budgetResponse.getData();
        for (ProjectWbsBudgetDto dto : projectWbsBudgetDtoList) {
            dto.setWbsSummaryCode(dto.getProjectCode() + "-" + dto.getWbsFullCode());
        }

        // 获取有效的wbs动态列
        List<WbsTemplateRuleCache> wbsTemplateRuleCaches = ProjectWbsBudgetUtils.getEligibilityWbsCache(projectDto.getWbsTemplateInfoId());

        try {
            // 从excel第2行开始读，读取行数：wbs动态列数量 + 17固定列
            List<List<String>> wbsExcelRows = FileUtil.importExcel(file, 0, 1, wbsTemplateRuleCaches.size() + 17);
            // 校验wbs模板导入title有效性
            validityImportWbsBudgetTitle(wbsExcelRows.get(0), wbsTemplateRuleCaches);
            // 检验导入的数据
            Map<String, Object> resultMap = ProjectWbsBudgetUtils.wbsExcelRows2Map(wbsExcelRows, wbsTemplateRuleCaches, projectWbsBudgetDtoList);
            return response.setData(resultMap);
        } catch (BizException bizException) {
            // 自定义内容不能被Exception拦截
            throw bizException;
        } catch (Exception e) {
            logger.error("wbs导入异常:" + e.getMessage(), e);
            throw new BizException(ErrorCode.CTC_PROJECT_WBS_BUDGET_NOT_TRUE);
        }
    }

    /**
     * 校验wbs模板导入title有效性
     *
     * @param wbsExcelRows
     * @param wbsTemplateRuleCaches
     */
    public static void validityImportWbsBudgetTitle(List<String> wbsExcelRows, List<WbsTemplateRuleCache> wbsTemplateRuleCaches) {
        List<String> titleExcelRows = wbsExcelRows.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(titleExcelRows) || titleExcelRows.size() != wbsTemplateRuleCaches.size() + 17) {
            throw new BizException(Code.ERROR, "导入模板有误，请下载最新模板");
        }
        for (int i = 0; i < wbsTemplateRuleCaches.size(); i++) {
            if (!StringUtils.equals(wbsTemplateRuleCaches.get(i).getRuleName(), titleExcelRows.get(i + 2))) {
                throw new BizException(Code.ERROR, "导入模板有误，请下载最新模板");
            }
        }
    }

    @ApiOperation(value = "下载错误数据", notes = "场景：WBS预算变更")
    @PostMapping("/batchUpdate/downloadErrorMsg")
    public void downloadErrorMsg(@RequestPart("file") MultipartFile file, @RequestParam String errMsg, HttpServletResponse response) {
        List<String> errMsgList = null;
        try {
            errMsgList = JSONObject.parseArray(errMsg, String.class);
        } catch (Exception e) {
            throw new MipException("参数异常");
        }
        Workbook workbook = null;
        try {
            workbook = WorkbookFactory.create(file.getInputStream());
            Sheet sheet = workbook.createSheet("报错信息");
            for (int i = 0; i < errMsgList.size(); ++i) {
                Row row = sheet.createRow(i);
                row.createCell(0).setCellValue(errMsgList.get(i));
            }
        } catch (Exception e) {
            throw new BizException(Code.ERROR, "模板解析异常");
        }
        //导出
        ExportExcelUtil.downLoadExcel("报错信息_" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xls", response, workbook);
    }

    @ApiOperation(value = "Set0导出", notes = "场景：详细设计单据-WBS变更")
    @GetMapping("/Set0/exportTemplate")
    public void exportTemplateSet0(@RequestParam Long projectWbsReceiptsId, HttpServletResponse servletResponse) {

        //查询WBS变更单详情
        Map<String, Object> paramMap = new HashMap();
        paramMap.put("projectWbsReceiptsId", projectWbsReceiptsId);
        paramMap.put("tag", "Set0");
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/projectWbsReceipts/buildTemplateContent", paramMap);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<ProjectDto> response = JSON.parseObject(res, new TypeReference<DataResponse<ProjectDto>>() {
        });
        if (Objects.equals(response.getCode(), Code.ERROR.getCode())) {
            throw new BizException(ErrorCode.ERROR, response.getMsg());
        }
        ProjectDto projectDto = response.getData();

        try {
            downLoadBatchUpdateTemplate(projectDto, projectDto.getProjectWbsBudgetList(), servletResponse);
        } catch (Exception e) {
            throw new BizException(ErrorCode.ERROR, "模板导出异常");
        }
    }

    @ApiOperation(value = "New导出", notes = "场景：详细设计单据-WBS变更")
    @GetMapping("/New/exportTemplate")
    public void exportTemplateNew(@RequestParam Long projectWbsReceiptsId, HttpServletResponse servletResponse) {

        //查询WBS变更单详情
        Map<String, Object> paramMap = new HashMap();
        paramMap.put("projectWbsReceiptsId", projectWbsReceiptsId);
        paramMap.put("tag", "New");
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/projectWbsReceipts/buildTemplateContent", paramMap);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<ProjectDto> response = JSON.parseObject(res, new TypeReference<DataResponse<ProjectDto>>() {
        });
        if (Objects.equals(response.getCode(), Code.ERROR.getCode())) {
            throw new BizException(ErrorCode.ERROR, response.getMsg());
        }
        ProjectDto projectDto = response.getData();
        Long wbsTemplateInfoId = projectDto.getWbsTemplateInfoId();

        // 获取符合条件的wbs缓存
        List<WbsTemplateRuleCache> wbsCaches = ProjectWbsBudgetUtils.getEligibilityWbsCache(wbsTemplateInfoId);
        // 获取符合条件的活动事项缓存
        List<ProjectActivityCache> activityCaches = ProjectWbsBudgetUtils.getEligibilityActivityCache();

        // 文件名
        StringBuffer fileName = new StringBuffer();
        fileName.append("WBS预算导入模板_" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
        fileName.append(".xls");

        // 创建新的Excel 工作簿
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet("项目wbs预算导入模板");

        /** 构建模板 **/
        buildBatchNewTemplate(fileName.toString(), servletResponse, workbook, sheet, wbsTemplateInfoId);

        /** 构建数据 **/
        try {
            //标题（用于行数据匹配）
            LinkedHashMap<String, String> titleMap = new LinkedHashMap<>();
            if (CollectionUtils.isNotEmpty(wbsCaches)) {
                for (WbsTemplateRuleCache wbsCache : wbsCaches) {
                    titleMap.put(wbsCache.getKey(), wbsCache.getRuleName());
                }
            }
            if (CollectionUtils.isNotEmpty(activityCaches)) {
                for (ProjectActivityCache activityCache : activityCaches) {
                    titleMap.put(activityCache.getKey(), activityCache.getCode());
                }
            }

            //行数据
            List<Map<String, Object>> dataMaps = new ArrayList<>();
            for (ProjectWbsBudgetDto dto : projectDto.getProjectWbsBudgetList()) {
                // 构建wbs动态列
                Map<String, Object> detailMap = BeanMapTool.beanToMap(dto);
                if (StringUtils.isNotEmpty(dto.getDynamicFields()) && StringUtils.isNotEmpty(dto.getDynamicValues())) {
                    String[] dynamicFieldArr = dto.getDynamicFields().split(",");
                    String[] dynamicValueArr = dto.getDynamicValues().split(",");
                    for (int i = 0; i < dynamicFieldArr.length; i++) {
                        detailMap.put(dynamicFieldArr[i], dynamicValueArr[i]);
                    }
                }
                // 构建活动事项
                if (StringUtils.isNotEmpty(dto.getActivityOrderNo()) && dto.getAfterChangePrice() != null) {
                    detailMap.put("activity_" + dto.getActivityOrderNo(), dto.getAfterChangePrice());
                }
                dataMaps.add(detailMap);
            }

            /* 导出数据JsonArray格式 */
            JSONArray dataJsonArray = JSONArray.parseArray(JSON.toJSONString(dataMaps));

            // 设置数据
            ExcelUtil.setExcelData(sheet, dataJsonArray, titleMap, null, null, null, null, ExcelUtil.creatCellStyle(workbook), 2);

            // 导出操作
            ExcelUtil.downLoadExcel(fileName.toString(), servletResponse, workbook);
        } catch (Exception e) {
            throw new BizException(ErrorCode.ERROR, "模板导出异常");
        }
    }

    @ApiOperation(value = "项目wbs预算导出(数据来源数据库)", notes = "项目已保存-查看详情")
    @GetMapping("/v1/exportByProjectId")
    public void exportByProjectId(@RequestParam("projectId") Long projectId, HttpServletResponse servletResponse) {

        /* 查询项目 */
        Map<String, Object> projectParamMap = new HashMap();
        projectParamMap.put("id", projectId);
        String url1 = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/findOnlyProjectById", projectParamMap);
        String res1 = restTemplate.getForEntity(url1, String.class).getBody();
        DataResponse<ProjectDto> projectResponse = JSON.parseObject(res1, new TypeReference<DataResponse<ProjectDto>>() {
        });
        ProjectDto projectDto = projectResponse.getData();

        /* 查询wbs预算 */
        Map<String, Object> paramMap = new HashMap();
        paramMap.put("projectId", projectId);
        String url2 = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/projectWbsBudget/findDetailWbsBudget", paramMap);
        String res2 = restTemplate.getForEntity(url2, String.class).getBody();
        DataResponse<List<ProjectWbsBudgetDto>> response = JSON.parseObject(res2, new TypeReference<DataResponse<List<ProjectWbsBudgetDto>>>() {
        });
        // wbs预算数据
        List<ProjectWbsBudgetDto> projectWbsBudgetDtoList = response.getData();

        try {
            /* 导出Excel配置 */
            String fileName = String.format("%s%s%s", "项目wbs预算详情列表_", DateUtils.format(new Date(), "yyyyMMddHHmmss"), ".xls");
            Workbook workBook = ExcelUtil.createWorkBook(fileName);
            Sheet sheet = ExcelUtil.createSheet(workBook, "项目wbs预算详情列表");
            CellStyle titleStyle = ExcelUtil.creatTitleStyle(workBook);
            CellStyle style = ExcelUtil.creatCellStyle(workBook);

            // wbs预算组装序号
            List<Map<String, Object>> dataMaps = ProjectWbsBudgetDto.dto2MapBatch(projectWbsBudgetDtoList);
            for (int i = 0; i < dataMaps.size(); i++) {
                Map<String, Object> currentMap = dataMaps.get(i);
                if (StringUtils.isNotBlank(MapUtils.getString(currentMap, WbsBudgetFieldConstant.FEE_TYPE_NAME))) {
                    // 能匹配到，boolean转字典
                    currentMap.put(WbsBudgetFieldConstant.FEE_SYNC_EMS, Boolean.TRUE.equals(MapUtils.getBoolean(currentMap, WbsBudgetFieldConstant.FEE_SYNC_EMS)) ? "是" : "否");
                } else {
                    // 匹配不到，不返回内容
                    currentMap.remove(WbsBudgetFieldConstant.FEE_SYNC_EMS);
                }
                currentMap.put(WbsBudgetFieldConstant.ORDER_NO, i + 1);
            }
            // wbs动态列
            List<WbsTemplateRuleCache> wbsCaches = ProjectWbsBudgetUtils.getEligibilityWbsCache(projectDto.getWbsTemplateInfoId());

            /* 组装经济事项 */
//            exportByWebDataPackageFeeItem(projectDto.getType(), dataMaps, wbsCaches);

            /* 导出数据JsonArray格式 */
            JSONArray dataJsonArray = JSONArray.parseArray(JSON.toJSONString(dataMaps));

            /* 标题 */
            LinkedHashMap<String, String> titleMap = new LinkedHashMap<>();
            titleMap.put(WbsBudgetFieldConstant.ORDER_NO, "序号");
            titleMap.put(WbsBudgetFieldConstant.PROJECT_CODE, "项目");
            if (!org.springframework.util.CollectionUtils.isEmpty(wbsCaches)) {
                for (WbsTemplateRuleCache wbsCacge : wbsCaches) {
                    titleMap.put(wbsCacge.getKey(), wbsCacge.getRuleName());
                }
            }
            titleMap.put(WbsBudgetFieldConstant.WBS_SUMMARY_CODE, "WBS号");
            titleMap.put(WbsBudgetFieldConstant.DESCRIPTION, "描述");
            titleMap.put(WbsBudgetFieldConstant.ACTIVITY_CODE, "活动事项编码");
            titleMap.put(WbsBudgetFieldConstant.ACTIVITY_NAME, "类别名称");
            titleMap.put(WbsBudgetFieldConstant.ACTIVITY_TYPE, "类别属性");
            titleMap.put(WbsBudgetFieldConstant.FEE_TYPE_NAME, "经济事项");
            titleMap.put(WbsBudgetFieldConstant.FEE_SYNC_EMS, "是否同步EMS");
            titleMap.put(WbsBudgetFieldConstant.PRICE, "预算金额");
            titleMap.put(WbsBudgetFieldConstant.BASELINE_COST, "预算基线");
            titleMap.put(WbsBudgetFieldConstant.DEMAND_COST, "需求预算");
            titleMap.put(WbsBudgetFieldConstant.ON_THE_WAY_COST, "在途成本");
            titleMap.put(WbsBudgetFieldConstant.INCURRED_COST, "已发生成本");
            titleMap.put(WbsBudgetFieldConstant.REMAINING_COST, "剩余可用预算");
            titleMap.put(WbsBudgetFieldConstant.CHANGE_ACCUMULATE_COST, "累计变更金额");

            ExcelUtil.setExcelData(sheet, dataJsonArray, titleMap, null, null, null, titleStyle, style);
            ExcelUtil.downLoadExcel(fileName, servletResponse, workBook);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @ApiOperation(value = "项目wbs预算导出(数据来源数据库)", notes = "新建项目-审批页导出excel")
    @GetMapping("/v2/exportByProjectId")
    public void exportByProjectIdV2(@RequestParam("projectId") Long projectId, HttpServletResponse servletResponse) {

        /* 查询项目 */
        Map<String, Object> projectParamMap = new HashMap();
        projectParamMap.put("id", projectId);
        String url1 = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/project/findOnlyProjectById", projectParamMap);
        String res1 = restTemplate.getForEntity(url1, String.class).getBody();
        DataResponse<ProjectDto> projectResponse = JSON.parseObject(res1, new TypeReference<DataResponse<ProjectDto>>() {
        });
        ProjectDto projectDto = projectResponse.getData();

        /* 查询wbs预算 */
        Map<String, Object> paramMap = new HashMap();
        paramMap.put("projectId", projectId);
        String url2 = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/projectWbsBudget/findDetailWbsBudget", paramMap);
        String res2 = restTemplate.getForEntity(url2, String.class).getBody();
        DataResponse<List<ProjectWbsBudgetDto>> response = JSON.parseObject(res2, new TypeReference<DataResponse<List<ProjectWbsBudgetDto>>>() {
        });
        // wbs预算数据
        List<ProjectWbsBudgetDto> projectWbsBudgetDtoList = response.getData();

        try {
            /* 导出Excel配置 */
            String fileName = String.format("%s%s%s", "项目wbs预算详情列表_", DateUtils.format(new Date(), "yyyyMMddHHmmss"), ".xls");
            Workbook workBook = ExcelUtil.createWorkBook(fileName);
            Sheet sheet = ExcelUtil.createSheet(workBook, "项目wbs预算详情列表");
            CellStyle titleStyle = ExcelUtil.creatTitleStyle(workBook);
            CellStyle style = ExcelUtil.creatCellStyle(workBook);

            // wbs预算组装序号
            List<Map<String, Object>> dataMaps = ProjectWbsBudgetDto.dto2MapBatch(projectWbsBudgetDtoList);
            for (int i = 0; i < dataMaps.size(); i++) {
                if (StringUtils.isNotBlank(MapUtils.getString(dataMaps.get(i), WbsBudgetFieldConstant.FEE_TYPE_NAME))) {
                    // 能匹配到，boolean转字典
                    dataMaps.get(i).put(WbsBudgetFieldConstant.FEE_SYNC_EMS, Boolean.TRUE.equals(MapUtils.getBoolean(dataMaps.get(i), WbsBudgetFieldConstant.FEE_SYNC_EMS)) ? "是" : "否");
                } else {
                    // 匹配不到，不返回内容
                    dataMaps.get(i).remove(WbsBudgetFieldConstant.FEE_SYNC_EMS);
                }
                dataMaps.get(i).put(WbsBudgetFieldConstant.ORDER_NO, i + 1);
            }
            // wbs动态列
            List<WbsTemplateRuleCache> wbsCaches = ProjectWbsBudgetUtils.getEligibilityWbsCache(projectDto.getWbsTemplateInfoId());

            /* 组装经济事项 */
//            exportByWebDataPackageFeeItem(projectDto.getType(), dataMaps, wbsCaches);

            /* 导出数据JsonArray格式 */
            JSONArray dataJsonArray = JSONArray.parseArray(JSON.toJSONString(dataMaps));

            /* 标题 */
            LinkedHashMap<String, String> titleMap = new LinkedHashMap<>();
            titleMap.put(WbsBudgetFieldConstant.ORDER_NO, "序号");
            titleMap.put(WbsBudgetFieldConstant.PROJECT_CODE, "项目");
            if (!org.springframework.util.CollectionUtils.isEmpty(wbsCaches)) {
                for (WbsTemplateRuleCache wbsCacge : wbsCaches) {
                    titleMap.put(wbsCacge.getKey(), wbsCacge.getRuleName());
                }
            }
            titleMap.put(WbsBudgetFieldConstant.WBS_SUMMARY_CODE, "WBS号");
            titleMap.put(WbsBudgetFieldConstant.DESCRIPTION, "描述");
            titleMap.put(WbsBudgetFieldConstant.ACTIVITY_CODE, "活动事项编码");
            titleMap.put(WbsBudgetFieldConstant.ACTIVITY_NAME, "类别名称");
            titleMap.put(WbsBudgetFieldConstant.ACTIVITY_TYPE, "类别属性");
            titleMap.put(WbsBudgetFieldConstant.FEE_TYPE_NAME, "经济事项");
            titleMap.put(WbsBudgetFieldConstant.FEE_SYNC_EMS, "是否同步EMS");
            titleMap.put(WbsBudgetFieldConstant.PRICE, "预算金额");

            ExcelUtil.setExcelData(sheet, dataJsonArray, titleMap, null, null, null, titleStyle, style);
            ExcelUtil.downLoadExcel(fileName, servletResponse, workBook);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @ApiOperation(value = "项目wbs预算导出(数据来源前端页面)", notes = "项目未保存-查看详情")
    @PostMapping("/v1/exportByWebData")
    public void exportByWebData(HttpServletResponse httpServletResponse, @RequestBody(required = false) String jsonData) {

        JSONObject jsonObject = JSONObject.parseObject(jsonData);
        Long wbsTemplateInfoId = jsonObject.getLong("wbsTemplateInfoId");
        Long projectTypeId = jsonObject.getLong("projectTypeId");
        /* 导出场景：saveProject(项目立项) previewProject(预立项转正) */
        String exportScene = jsonObject.getString("exportScene");
        if (StringUtils.isEmpty(exportScene)) {
            exportScene = "saveProject";
        }

        /* 获取wbs预算 */
        String strData = jsonObject.getString("data");
        List<Map<String, Object>> dataMaps = (List<Map<String, Object>>) JSON.parse(strData);

        try {
            /* 导出Excel配置 */
            String fileName = String.format("%s%s%s", "项目wbs预算详情列表_", DateUtils.format(new Date(), "yyyyMMddHHmmss"), ".xls");
            Workbook workBook = ExcelUtil.createWorkBook(fileName);
            Sheet sheet = ExcelUtil.createSheet(workBook, "项目wbs预算详情列表");
            CellStyle titleStyle = ExcelUtil.creatTitleStyle(workBook);
            CellStyle style = ExcelUtil.creatCellStyle(workBook);

            // wbs预算组装序号
            for (int i = 0; i < dataMaps.size(); i++) {
                if (StringUtils.isNotBlank(MapUtils.getString(dataMaps.get(i), WbsBudgetFieldConstant.FEE_TYPE_NAME))) {
                    // 能匹配到，boolean转字典
                    dataMaps.get(i).put(WbsBudgetFieldConstant.FEE_SYNC_EMS, Boolean.TRUE.equals(MapUtils.getBoolean(dataMaps.get(i), WbsBudgetFieldConstant.FEE_SYNC_EMS)) ? "是" : "否");
                } else {
                    // 匹配不到，不返回内容
                    dataMaps.get(i).remove(WbsBudgetFieldConstant.FEE_SYNC_EMS);
                }
                dataMaps.get(i).put(WbsBudgetFieldConstant.ORDER_NO, i + 1);
            }
            // wbs动态列
            List<WbsTemplateRuleCache> wbsCaches = ProjectWbsBudgetUtils.getEligibilityWbsCache(wbsTemplateInfoId);

            /* 组装经济事项 */
//            exportByWebDataPackageFeeItem(projectTypeId, dataMaps, wbsCaches);

            /* 导出数据JsonArray格式 */
            JSONArray dataJsonArray = JSONArray.parseArray(JSON.toJSONString(dataMaps));

            /* 标题 */
            LinkedHashMap<String, String> titleMap = new LinkedHashMap<>();
            titleMap.put(WbsBudgetFieldConstant.ORDER_NO, "序号");
            titleMap.put(WbsBudgetFieldConstant.PROJECT_CODE, "项目");
            if (!org.springframework.util.CollectionUtils.isEmpty(wbsCaches)) {
                for (WbsTemplateRuleCache wbsCacge : wbsCaches) {
                    titleMap.put(wbsCacge.getKey(), wbsCacge.getRuleName());
                }
            }
            titleMap.put(WbsBudgetFieldConstant.WBS_SUMMARY_CODE, "WBS号");
            titleMap.put(WbsBudgetFieldConstant.DESCRIPTION, "描述");
            titleMap.put(WbsBudgetFieldConstant.ACTIVITY_CODE, "活动事项编码");
            titleMap.put(WbsBudgetFieldConstant.ACTIVITY_NAME, "类别名称");
            titleMap.put(WbsBudgetFieldConstant.ACTIVITY_TYPE, "类别属性");
            titleMap.put(WbsBudgetFieldConstant.FEE_TYPE_NAME, "经济事项");
            titleMap.put(WbsBudgetFieldConstant.FEE_SYNC_EMS, "是否同步EMS");
            if (exportScene.equals("saveProject")) {
                titleMap.put(WbsBudgetFieldConstant.PRICE, "预算金额");
            } else if (exportScene.equals("previewProject")) {
                titleMap.put(WbsBudgetFieldConstant.AFTER_CHANGE_PRICE, "预算金额");
            }

            ExcelUtil.setExcelData(sheet, dataJsonArray, titleMap, null, null, null, titleStyle, style);
            ExcelUtil.downLoadExcel(fileName, httpServletResponse, workBook);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 组装经济事项
     *
     * @param projectTypeId
     * @param dataMaps
     * @param wbsCacheList
     */
    @Deprecated
    private void exportByWebDataPackageFeeItem(Long projectTypeId, List<Map<String, Object>> dataMaps, List<WbsTemplateRuleCache> wbsCacheList) {
        if (CollectionUtils.isEmpty(dataMaps)) {
            return;
        }

        /* 获取费用类型经济事项 */
        Map<String, Object> paramMap = new HashMap();
        paramMap.put("projectTypeId", projectTypeId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/projectType/selectFeeItemExpense", paramMap);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<FeeItemExpenseTypeDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<FeeItemExpenseTypeDto>>>() {
        });
        // wbs预算数据
        List<FeeItemExpenseTypeDto> feeItemExpenseTypeDtoList = response.getData();


        // wbs最后一个动态列的field
        String wbsLastField = wbsCacheList.get(wbsCacheList.size() - 1).getKey();

        for (Map data : dataMaps) {
            String wbsLastCode = MapUtils.getString(data, wbsLastField);
            String activityCode = MapUtils.getString(data, WbsBudgetFieldConstant.ACTIVITY_CODE);

            if (CollectionUtils.isNotEmpty(feeItemExpenseTypeDtoList)) {
                // wbs经济事项
                List<FeeItemExpenseTypeDto> wbsFeeItems = feeItemExpenseTypeDtoList.stream().filter(a -> Objects.equals(a.getName(), wbsLastCode) && Objects.equals(a.getFeeSettingMode(), FeeSettingModeEnum.WBS.getCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(wbsFeeItems)) {
                    FeeItemExpenseTypeDto wbsFeeItem = wbsFeeItems.get(0);
                    data.put(WbsBudgetFieldConstant.FEE_TYPE_NAME, wbsFeeItem.getFeeTypeName());
                    data.put(WbsBudgetFieldConstant.SYNC_EMS, Boolean.TRUE.equals(wbsFeeItem.getSyncEMS()) ? "是" : "否");
                    continue;
                }

                // activity经济事项
                List<FeeItemExpenseTypeDto> activityFeeItems = feeItemExpenseTypeDtoList.stream().filter(a -> Objects.equals(a.getName(), activityCode) && Objects.equals(a.getFeeSettingMode(), FeeSettingModeEnum.PROJECT_ACTIVITY.getCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(activityFeeItems)) {
                    FeeItemExpenseTypeDto activityFeeItem = activityFeeItems.get(0);
                    data.put(WbsBudgetFieldConstant.FEE_TYPE_NAME, activityFeeItem.getFeeTypeName());
                    data.put(WbsBudgetFieldConstant.SYNC_EMS, Boolean.TRUE.equals(activityFeeItem.getSyncEMS()) ? "是" : "否");
                    continue;
                }
            }
        }
    }

    @ApiOperation(value = "获取用户可选的wbs")
    @GetMapping("findUserWbsBudget")
    public Response findUserWbsBudget(@RequestParam("projectId") Long projectId) {
        String url = ModelsEnum.CTC.getBaseUrl() + "projectWbsBudget/findUserWbsBudget?projectId=" + projectId;
        return restTemplate.getForObject(url, DataResponse.class);
    }

    @ApiOperation(value = "获取项目下所有wbs编码")
    @GetMapping("findWbsByProjectId")
    public Response findWbsByProjectId(@RequestParam("projectId") Long projectId) {
        String url = ModelsEnum.CTC.getBaseUrl() + "projectWbsBudget/findWbsByProjectId?projectId=" + projectId;
        return restTemplate.getForObject(url, DataResponse.class);
    }
}
