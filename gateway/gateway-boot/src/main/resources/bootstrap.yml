app_id: pam
tenant_id: 1103942863773581312

feign:
  name: mframework-provider
  pam-basedata-service-name: pam-basedata
  pam-basedata-service-path: /pam-basedata
  pam-ctc-service-name: pam-ctc
  pam-ctc-service-path: /


management:
  hreatbeat:
    domainName:
  server:
    port: 19089
  endpoints:
    web:
      exposure:
        include: "*"

spring:
  application:
    name: pam
  main:
    allow-bean-definition-overriding: true
  datasource-cluster:
    nodes:
      - name: mainNode
        writeHost: datasource
        readHost: datasource
        isDefault: true
  druid:
    allow:
    deny:
    loginUsername: admin
    loginPassword: 123456
    resetEnable: true
    #访问路径
    druidUrl: /druid/*
    exclusions: /druid/*,*.js,*.gif,*.jpg,*.png,*.css,*.ico
    monitorPackages: com.midea.*.service.*,com.midea.*.service.impl.*,com.midea.*.mapper.* #GroupId
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    short-date-format: yyyy-MM-dd
    time-zone: GMT+8
  messages:
    basename: i18n/messages

mybatis:
  mapperLocations: classpath*:com/midea/*/**/mapper/xml/*Mapper.xml
  typeAliasesPackage: com.midea.*.**.entity

server:
  port: 9080
  connection-timeout: 18000000
  servlet:
    context-path: /pam
    session:
      timeout: 30
  tomcat:
    uri-encoding: UTF-8
    max-http-post-size: 10485760
error:
  path: error/error

logging:
  level.com.xxl.job.core.thread: debug
  config: classpath:logback-spring.xml
security:
  auth:
    permission:
      enable: false
  conf:
    masAppId: bMVL530J3ajNMQo5M  #需要业务系统找A4开通masAppId与masAppKey
    masAppKey: jBcs2b9TqLSBQPwwhrJkynUUJk6JFfYa
    masLoginUrl: https://signinuat.midea.com
    masSecurityKey: MAS_TGC_UAT
    successUrl: onLoginSuccess
    domain: .midea.com
    unauthorizedUrl:
    loginView: doLogin.html
    openMideaToken: true
    sessionTimeout: 3600000
    securityKey: mip_sso_dev_id # 配置ShiroId 生产环境: mip_sso_id , DEV环境: mip_sso_dev_id , SIT环境: mip_sso_sit_id , UAT环境: mip_sso_uat_id
    sso-service: http://************:8080/mideaidm-ssows/sumwebservice/accessServiceImpl/getAccessService
    reLogin: 1
    sysanon:
      - /unauthorized
      - /unLogin
      - /onLoginSuccess
      - /logoutSuccess
      - /**/comment/sysComment/anonymous/**
      - /webjars/springfox-swagger-ui/**
      - /swagger-ui.html
      - /swagger-resources/**
      - /v2/api-docs/**
      - /refreshInstance
      - /workflow/callback
      - /**/skipSecurityInterceptor/**
      - /file/**
      - /mobile/app/**
      - /workflow/getToDoContentInfo
      - /glegalworkflow/callback
      - /authority/pushUnit
      - /authority/pushRoleData
      - /authority/pushUserRoleData
      - /authority/pushReportGroupInfo
      - /authority/pushReportGroupGrantUserInfo
      - /authority/writeRoleInfo
      - /authority/writeReportGroupGrantUserInfo
      - /authority/pushUnitOrgInfo
      - /authority/pushUserUnitRelInfo
      - /authority/writeUserUnitRelInfo
      - /mrp/**
      - /xxl-job/**
      - /invoiceReceivable/syncInvoiceInfoFromMif
      - /sdpBuyers/syncSdpBuyers
      - /mbf/**
      - /sdp/callback
      - /sdp/project/getEmsBudgetInfoList
      - /statistics/materialGetReturnOutside/**

      - /paymentApply/sdpReceivePaymentApply
      - /gsc/invoice/isp/request
      - /gsc/invoice/request
light:
  mybatis:
    id:
      enabled: true
    audit:
      enabled: true

file:
  path:
    root: /apps/pam/files
zuul:
  #  prefix: /api
  routes:
    pam-crm:
      path: /crm/**
      serviceId: pam-crm
      stripPrefix: false
    achievementGoal:
      path: /achievementGoal/**
      serviceId: pam-crm
      stripPrefix: false
    attributionUnit:
      path: /attributionUnit/**
      serviceId: pam-crm
      stripPrefix: false
    business:
      path: /business/**
      serviceId: pam-crm
      stripPrefix: false
    businessFollowRecord:
      path: /businessFollowRecord/**
      serviceId: pam-crm
      stripPrefix: false
    #    customer:
    #      path: /customer/**
    #      serviceId: pam-crm
    #      stripPrefix: false
    lead:
      path: /lead/**
      serviceId: pam-crm
      stripPrefix: false
    #    plan:
    #      path: /plan/**
    #      serviceId: pam-crm
    #      stripPrefix: false
    productAttribute:
      path: /productAttribute/**
      serviceId: pam-crm
      stripPrefix: false
    product:
      path: /product/**
      serviceId: pam-crm
      stripPrefix: false
    quotation:
      path: /quotation/**
      serviceId: pam-crm
      stripPrefix: false
    taxRateConfiguration:
      path: /taxRateConfiguration/**
      serviceId: pam-crm
      stripPrefix: false
    tianyancha:
      path: /tianyancha/**
      serviceId: pam-crm
      stripPrefix: false
    pam-ctc:
      path: /ctc/**
      serviceId: pam-ctc
      stripPrefix: false
    project:
      path: /project/**
      serviceId: pam-ctc
      stripPrefix: false
ribbon:
  #连接超时时间
  connectTimeout: 360000
  #读超时时间
  ReadTimeout: 360000
  # 同一实例最大重试次数，不包括首次调用。默认值为0
  MaxAutoRetries: 0
  # 同一个微服务其他实例的最大重试次数，不包括第一次调用的实例。默认值为1
  MaxAutoRetriesNextServer: 0
  # 是否所有操作（GET、POST等）都允许重试。默认值为false
  OkToRetryOnAllOperations: false
ok:
  http:
    connect-timeout: 30
    read-timeout: 30
    write-timeout: 30
    max-idle-connections: 100 # 连接池中整体的空闲连接的最大数量
    keep-alive-duration: 300 # 连接空闲时间最多为 300 秒
