package com.midea.pam.common.basedata.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.util.Date;

public class CustTrxType extends LongIdEntity implements Serializable {
    private Long id;

    private Long operatingUnitId;

    private Long custTrxTypeId;

    private String custTrxTypeIdName;

    private String description;

    private String trxType;

    private Long cmTypeId;

    private String cmTypeName;

    private String glRecConc;

    private String glRevConc;

    private String trxStatus;

    private Date endDate;

    private Date lastUpdateDate;

    private Long createBy;

    private Date createAt;

    private Long updateBy;

    private Date updateAt;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOperatingUnitId() {
        return operatingUnitId;
    }

    public void setOperatingUnitId(Long operatingUnitId) {
        this.operatingUnitId = operatingUnitId;
    }

    public Long getCustTrxTypeId() {
        return custTrxTypeId;
    }

    public void setCustTrxTypeId(Long custTrxTypeId) {
        this.custTrxTypeId = custTrxTypeId;
    }

    public String getCustTrxTypeIdName() {
        return custTrxTypeIdName;
    }

    public void setCustTrxTypeIdName(String custTrxTypeIdName) {
        this.custTrxTypeIdName = custTrxTypeIdName == null ? null : custTrxTypeIdName.trim();
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    public String getTrxType() {
        return trxType;
    }

    public void setTrxType(String trxType) {
        this.trxType = trxType == null ? null : trxType.trim();
    }

    public Long getCmTypeId() {
        return cmTypeId;
    }

    public void setCmTypeId(Long cmTypeId) {
        this.cmTypeId = cmTypeId;
    }

    public String getCmTypeName() {
        return cmTypeName;
    }

    public void setCmTypeName(String cmTypeName) {
        this.cmTypeName = cmTypeName == null ? null : cmTypeName.trim();
    }

    public String getGlRecConc() {
        return glRecConc;
    }

    public void setGlRecConc(String glRecConc) {
        this.glRecConc = glRecConc == null ? null : glRecConc.trim();
    }

    public String getGlRevConc() {
        return glRevConc;
    }

    public void setGlRevConc(String glRevConc) {
        this.glRevConc = glRevConc == null ? null : glRevConc.trim();
    }

    public String getTrxStatus() {
        return trxStatus;
    }

    public void setTrxStatus(String trxStatus) {
        this.trxStatus = trxStatus == null ? null : trxStatus.trim();
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Date getCreateAt() {
        return createAt;
    }

    public void setCreateAt(Date createAt) {
        this.createAt = createAt;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(Date updateAt) {
        this.updateAt = updateAt;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", operatingUnitId=").append(operatingUnitId);
        sb.append(", custTrxTypeId=").append(custTrxTypeId);
        sb.append(", custTrxTypeIdName=").append(custTrxTypeIdName);
        sb.append(", description=").append(description);
        sb.append(", trxType=").append(trxType);
        sb.append(", cmTypeId=").append(cmTypeId);
        sb.append(", cmTypeName=").append(cmTypeName);
        sb.append(", glRecConc=").append(glRecConc);
        sb.append(", glRevConc=").append(glRevConc);
        sb.append(", trxStatus=").append(trxStatus);
        sb.append(", endDate=").append(endDate);
        sb.append(", lastUpdateDate=").append(lastUpdateDate);
        sb.append(", createBy=").append(createBy);
        sb.append(", createAt=").append(createAt);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateAt=").append(updateAt);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}