package com.midea.pam.common.ctc.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "项目活动事项表")
public class ProjectActivity extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "序号")
    private String orderNo;

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "类别名称")
    private String name;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "预算事项(0否/1是)")
    private Boolean budgetMattersState;

    @ApiModelProperty(value = "类别属性")
    private String type;

    @ApiModelProperty(value = "启用日期")
    private Date startTime;

    @ApiModelProperty(value = "失效日期")
    private Date endTime;

    @ApiModelProperty(value = "是否父级(0否/1是)")
    private Boolean parentState;

    @ApiModelProperty(value = "层级")
    private String level;

    @ApiModelProperty(value = "使用单位id")
    private Long unitId;

    @ApiModelProperty(value = "利润表事项（对应report_sample表的code）")
    private String reportProjectMarginCode;

    @ApiModelProperty(value = "删除状态(1失效/0有效)")
    private Boolean deletedFlag;

    @ApiModelProperty(value = "版本号")
    private Long version;

    private static final long serialVersionUID = 1L;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Boolean getBudgetMattersState() {
        return budgetMattersState;
    }

    public void setBudgetMattersState(Boolean budgetMattersState) {
        this.budgetMattersState = budgetMattersState;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Boolean getParentState() {
        return parentState;
    }

    public void setParentState(Boolean parentState) {
        this.parentState = parentState;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level == null ? null : level.trim();
    }

    public Long getUnitId() {
        return unitId;
    }

    public void setUnitId(Long unitId) {
        this.unitId = unitId;
    }

    public String getReportProjectMarginCode() {
        return reportProjectMarginCode;
    }

    public void setReportProjectMarginCode(String reportProjectMarginCode) {
        this.reportProjectMarginCode = reportProjectMarginCode == null ? null : reportProjectMarginCode.trim();
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", orderNo=").append(orderNo);
        sb.append(", code=").append(code);
        sb.append(", name=").append(name);
        sb.append(", description=").append(description);
        sb.append(", remark=").append(remark);
        sb.append(", budgetMattersState=").append(budgetMattersState);
        sb.append(", type=").append(type);
        sb.append(", startTime=").append(startTime);
        sb.append(", endTime=").append(endTime);
        sb.append(", parentState=").append(parentState);
        sb.append(", level=").append(level);
        sb.append(", unitId=").append(unitId);
        sb.append(", reportProjectMarginCode=").append(reportProjectMarginCode);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", version=").append(version);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}