package com.midea.pam.common.ctc.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class WorkingHourAccountingExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public WorkingHourAccountingExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andOuIdIsNull() {
            addCriterion("ou_id is null");
            return (Criteria) this;
        }

        public Criteria andOuIdIsNotNull() {
            addCriterion("ou_id is not null");
            return (Criteria) this;
        }

        public Criteria andOuIdEqualTo(Long value) {
            addCriterion("ou_id =", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotEqualTo(Long value) {
            addCriterion("ou_id <>", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdGreaterThan(Long value) {
            addCriterion("ou_id >", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdGreaterThanOrEqualTo(Long value) {
            addCriterion("ou_id >=", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdLessThan(Long value) {
            addCriterion("ou_id <", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdLessThanOrEqualTo(Long value) {
            addCriterion("ou_id <=", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdIn(List<Long> values) {
            addCriterion("ou_id in", values, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotIn(List<Long> values) {
            addCriterion("ou_id not in", values, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdBetween(Long value1, Long value2) {
            addCriterion("ou_id between", value1, value2, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotBetween(Long value1, Long value2) {
            addCriterion("ou_id not between", value1, value2, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuNameIsNull() {
            addCriterion("ou_name is null");
            return (Criteria) this;
        }

        public Criteria andOuNameIsNotNull() {
            addCriterion("ou_name is not null");
            return (Criteria) this;
        }

        public Criteria andOuNameEqualTo(String value) {
            addCriterion("ou_name =", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotEqualTo(String value) {
            addCriterion("ou_name <>", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameGreaterThan(String value) {
            addCriterion("ou_name >", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameGreaterThanOrEqualTo(String value) {
            addCriterion("ou_name >=", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLessThan(String value) {
            addCriterion("ou_name <", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLessThanOrEqualTo(String value) {
            addCriterion("ou_name <=", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLike(String value) {
            addCriterion("ou_name like", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotLike(String value) {
            addCriterion("ou_name not like", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameIn(List<String> values) {
            addCriterion("ou_name in", values, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotIn(List<String> values) {
            addCriterion("ou_name not in", values, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameBetween(String value1, String value2) {
            addCriterion("ou_name between", value1, value2, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotBetween(String value1, String value2) {
            addCriterion("ou_name not between", value1, value2, "ouName");
            return (Criteria) this;
        }

        public Criteria andCodeIsNull() {
            addCriterion("code is null");
            return (Criteria) this;
        }

        public Criteria andCodeIsNotNull() {
            addCriterion("code is not null");
            return (Criteria) this;
        }

        public Criteria andCodeEqualTo(String value) {
            addCriterion("code =", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotEqualTo(String value) {
            addCriterion("code <>", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeGreaterThan(String value) {
            addCriterion("code >", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeGreaterThanOrEqualTo(String value) {
            addCriterion("code >=", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLessThan(String value) {
            addCriterion("code <", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLessThanOrEqualTo(String value) {
            addCriterion("code <=", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLike(String value) {
            addCriterion("code like", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotLike(String value) {
            addCriterion("code not like", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeIn(List<String> values) {
            addCriterion("code in", values, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotIn(List<String> values) {
            addCriterion("code not in", values, "code");
            return (Criteria) this;
        }

        public Criteria andCodeBetween(String value1, String value2) {
            addCriterion("code between", value1, value2, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotBetween(String value1, String value2) {
            addCriterion("code not between", value1, value2, "code");
            return (Criteria) this;
        }

        public Criteria andTotalWorkingHourIsNull() {
            addCriterion("total_working_hour is null");
            return (Criteria) this;
        }

        public Criteria andTotalWorkingHourIsNotNull() {
            addCriterion("total_working_hour is not null");
            return (Criteria) this;
        }

        public Criteria andTotalWorkingHourEqualTo(BigDecimal value) {
            addCriterion("total_working_hour =", value, "totalWorkingHour");
            return (Criteria) this;
        }

        public Criteria andTotalWorkingHourNotEqualTo(BigDecimal value) {
            addCriterion("total_working_hour <>", value, "totalWorkingHour");
            return (Criteria) this;
        }

        public Criteria andTotalWorkingHourGreaterThan(BigDecimal value) {
            addCriterion("total_working_hour >", value, "totalWorkingHour");
            return (Criteria) this;
        }

        public Criteria andTotalWorkingHourGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_working_hour >=", value, "totalWorkingHour");
            return (Criteria) this;
        }

        public Criteria andTotalWorkingHourLessThan(BigDecimal value) {
            addCriterion("total_working_hour <", value, "totalWorkingHour");
            return (Criteria) this;
        }

        public Criteria andTotalWorkingHourLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_working_hour <=", value, "totalWorkingHour");
            return (Criteria) this;
        }

        public Criteria andTotalWorkingHourIn(List<BigDecimal> values) {
            addCriterion("total_working_hour in", values, "totalWorkingHour");
            return (Criteria) this;
        }

        public Criteria andTotalWorkingHourNotIn(List<BigDecimal> values) {
            addCriterion("total_working_hour not in", values, "totalWorkingHour");
            return (Criteria) this;
        }

        public Criteria andTotalWorkingHourBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_working_hour between", value1, value2, "totalWorkingHour");
            return (Criteria) this;
        }

        public Criteria andTotalWorkingHourNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_working_hour not between", value1, value2, "totalWorkingHour");
            return (Criteria) this;
        }

        public Criteria andTotalLaborCostIsNull() {
            addCriterion("total_labor_cost is null");
            return (Criteria) this;
        }

        public Criteria andTotalLaborCostIsNotNull() {
            addCriterion("total_labor_cost is not null");
            return (Criteria) this;
        }

        public Criteria andTotalLaborCostEqualTo(BigDecimal value) {
            addCriterion("total_labor_cost =", value, "totalLaborCost");
            return (Criteria) this;
        }

        public Criteria andTotalLaborCostNotEqualTo(BigDecimal value) {
            addCriterion("total_labor_cost <>", value, "totalLaborCost");
            return (Criteria) this;
        }

        public Criteria andTotalLaborCostGreaterThan(BigDecimal value) {
            addCriterion("total_labor_cost >", value, "totalLaborCost");
            return (Criteria) this;
        }

        public Criteria andTotalLaborCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_labor_cost >=", value, "totalLaborCost");
            return (Criteria) this;
        }

        public Criteria andTotalLaborCostLessThan(BigDecimal value) {
            addCriterion("total_labor_cost <", value, "totalLaborCost");
            return (Criteria) this;
        }

        public Criteria andTotalLaborCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_labor_cost <=", value, "totalLaborCost");
            return (Criteria) this;
        }

        public Criteria andTotalLaborCostIn(List<BigDecimal> values) {
            addCriterion("total_labor_cost in", values, "totalLaborCost");
            return (Criteria) this;
        }

        public Criteria andTotalLaborCostNotIn(List<BigDecimal> values) {
            addCriterion("total_labor_cost not in", values, "totalLaborCost");
            return (Criteria) this;
        }

        public Criteria andTotalLaborCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_labor_cost between", value1, value2, "totalLaborCost");
            return (Criteria) this;
        }

        public Criteria andTotalLaborCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_labor_cost not between", value1, value2, "totalLaborCost");
            return (Criteria) this;
        }

        public Criteria andGlPeriodIsNull() {
            addCriterion("gl_period is null");
            return (Criteria) this;
        }

        public Criteria andGlPeriodIsNotNull() {
            addCriterion("gl_period is not null");
            return (Criteria) this;
        }

        public Criteria andGlPeriodEqualTo(String value) {
            addCriterion("gl_period =", value, "glPeriod");
            return (Criteria) this;
        }

        public Criteria andGlPeriodNotEqualTo(String value) {
            addCriterion("gl_period <>", value, "glPeriod");
            return (Criteria) this;
        }

        public Criteria andGlPeriodGreaterThan(String value) {
            addCriterion("gl_period >", value, "glPeriod");
            return (Criteria) this;
        }

        public Criteria andGlPeriodGreaterThanOrEqualTo(String value) {
            addCriterion("gl_period >=", value, "glPeriod");
            return (Criteria) this;
        }

        public Criteria andGlPeriodLessThan(String value) {
            addCriterion("gl_period <", value, "glPeriod");
            return (Criteria) this;
        }

        public Criteria andGlPeriodLessThanOrEqualTo(String value) {
            addCriterion("gl_period <=", value, "glPeriod");
            return (Criteria) this;
        }

        public Criteria andGlPeriodLike(String value) {
            addCriterion("gl_period like", value, "glPeriod");
            return (Criteria) this;
        }

        public Criteria andGlPeriodNotLike(String value) {
            addCriterion("gl_period not like", value, "glPeriod");
            return (Criteria) this;
        }

        public Criteria andGlPeriodIn(List<String> values) {
            addCriterion("gl_period in", values, "glPeriod");
            return (Criteria) this;
        }

        public Criteria andGlPeriodNotIn(List<String> values) {
            addCriterion("gl_period not in", values, "glPeriod");
            return (Criteria) this;
        }

        public Criteria andGlPeriodBetween(String value1, String value2) {
            addCriterion("gl_period between", value1, value2, "glPeriod");
            return (Criteria) this;
        }

        public Criteria andGlPeriodNotBetween(String value1, String value2) {
            addCriterion("gl_period not between", value1, value2, "glPeriod");
            return (Criteria) this;
        }

        public Criteria andGlDateIsNull() {
            addCriterion("gl_date is null");
            return (Criteria) this;
        }

        public Criteria andGlDateIsNotNull() {
            addCriterion("gl_date is not null");
            return (Criteria) this;
        }

        public Criteria andGlDateEqualTo(Date value) {
            addCriterion("gl_date =", value, "glDate");
            return (Criteria) this;
        }

        public Criteria andGlDateNotEqualTo(Date value) {
            addCriterion("gl_date <>", value, "glDate");
            return (Criteria) this;
        }

        public Criteria andGlDateGreaterThan(Date value) {
            addCriterion("gl_date >", value, "glDate");
            return (Criteria) this;
        }

        public Criteria andGlDateGreaterThanOrEqualTo(Date value) {
            addCriterion("gl_date >=", value, "glDate");
            return (Criteria) this;
        }

        public Criteria andGlDateLessThan(Date value) {
            addCriterion("gl_date <", value, "glDate");
            return (Criteria) this;
        }

        public Criteria andGlDateLessThanOrEqualTo(Date value) {
            addCriterion("gl_date <=", value, "glDate");
            return (Criteria) this;
        }

        public Criteria andGlDateIn(List<Date> values) {
            addCriterion("gl_date in", values, "glDate");
            return (Criteria) this;
        }

        public Criteria andGlDateNotIn(List<Date> values) {
            addCriterion("gl_date not in", values, "glDate");
            return (Criteria) this;
        }

        public Criteria andGlDateBetween(Date value1, Date value2) {
            addCriterion("gl_date between", value1, value2, "glDate");
            return (Criteria) this;
        }

        public Criteria andGlDateNotBetween(Date value1, Date value2) {
            addCriterion("gl_date not between", value1, value2, "glDate");
            return (Criteria) this;
        }

        public Criteria andApplyMonthIsNull() {
            addCriterion("apply_month is null");
            return (Criteria) this;
        }

        public Criteria andApplyMonthIsNotNull() {
            addCriterion("apply_month is not null");
            return (Criteria) this;
        }

        public Criteria andApplyMonthEqualTo(String value) {
            addCriterion("apply_month =", value, "applyMonth");
            return (Criteria) this;
        }

        public Criteria andApplyMonthNotEqualTo(String value) {
            addCriterion("apply_month <>", value, "applyMonth");
            return (Criteria) this;
        }

        public Criteria andApplyMonthGreaterThan(String value) {
            addCriterion("apply_month >", value, "applyMonth");
            return (Criteria) this;
        }

        public Criteria andApplyMonthGreaterThanOrEqualTo(String value) {
            addCriterion("apply_month >=", value, "applyMonth");
            return (Criteria) this;
        }

        public Criteria andApplyMonthLessThan(String value) {
            addCriterion("apply_month <", value, "applyMonth");
            return (Criteria) this;
        }

        public Criteria andApplyMonthLessThanOrEqualTo(String value) {
            addCriterion("apply_month <=", value, "applyMonth");
            return (Criteria) this;
        }

        public Criteria andApplyMonthLike(String value) {
            addCriterion("apply_month like", value, "applyMonth");
            return (Criteria) this;
        }

        public Criteria andApplyMonthNotLike(String value) {
            addCriterion("apply_month not like", value, "applyMonth");
            return (Criteria) this;
        }

        public Criteria andApplyMonthIn(List<String> values) {
            addCriterion("apply_month in", values, "applyMonth");
            return (Criteria) this;
        }

        public Criteria andApplyMonthNotIn(List<String> values) {
            addCriterion("apply_month not in", values, "applyMonth");
            return (Criteria) this;
        }

        public Criteria andApplyMonthBetween(String value1, String value2) {
            addCriterion("apply_month between", value1, value2, "applyMonth");
            return (Criteria) this;
        }

        public Criteria andApplyMonthNotBetween(String value1, String value2) {
            addCriterion("apply_month not between", value1, value2, "applyMonth");
            return (Criteria) this;
        }

        public Criteria andApproveMonthIsNull() {
            addCriterion("approve_month is null");
            return (Criteria) this;
        }

        public Criteria andApproveMonthIsNotNull() {
            addCriterion("approve_month is not null");
            return (Criteria) this;
        }

        public Criteria andApproveMonthEqualTo(String value) {
            addCriterion("approve_month =", value, "approveMonth");
            return (Criteria) this;
        }

        public Criteria andApproveMonthNotEqualTo(String value) {
            addCriterion("approve_month <>", value, "approveMonth");
            return (Criteria) this;
        }

        public Criteria andApproveMonthGreaterThan(String value) {
            addCriterion("approve_month >", value, "approveMonth");
            return (Criteria) this;
        }

        public Criteria andApproveMonthGreaterThanOrEqualTo(String value) {
            addCriterion("approve_month >=", value, "approveMonth");
            return (Criteria) this;
        }

        public Criteria andApproveMonthLessThan(String value) {
            addCriterion("approve_month <", value, "approveMonth");
            return (Criteria) this;
        }

        public Criteria andApproveMonthLessThanOrEqualTo(String value) {
            addCriterion("approve_month <=", value, "approveMonth");
            return (Criteria) this;
        }

        public Criteria andApproveMonthLike(String value) {
            addCriterion("approve_month like", value, "approveMonth");
            return (Criteria) this;
        }

        public Criteria andApproveMonthNotLike(String value) {
            addCriterion("approve_month not like", value, "approveMonth");
            return (Criteria) this;
        }

        public Criteria andApproveMonthIn(List<String> values) {
            addCriterion("approve_month in", values, "approveMonth");
            return (Criteria) this;
        }

        public Criteria andApproveMonthNotIn(List<String> values) {
            addCriterion("approve_month not in", values, "approveMonth");
            return (Criteria) this;
        }

        public Criteria andApproveMonthBetween(String value1, String value2) {
            addCriterion("approve_month between", value1, value2, "approveMonth");
            return (Criteria) this;
        }

        public Criteria andApproveMonthNotBetween(String value1, String value2) {
            addCriterion("approve_month not between", value1, value2, "approveMonth");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNull() {
            addCriterion("currency is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNotNull() {
            addCriterion("currency is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyEqualTo(String value) {
            addCriterion("currency =", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotEqualTo(String value) {
            addCriterion("currency <>", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThan(String value) {
            addCriterion("currency >", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThanOrEqualTo(String value) {
            addCriterion("currency >=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThan(String value) {
            addCriterion("currency <", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThanOrEqualTo(String value) {
            addCriterion("currency <=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLike(String value) {
            addCriterion("currency like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotLike(String value) {
            addCriterion("currency not like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyIn(List<String> values) {
            addCriterion("currency in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotIn(List<String> values) {
            addCriterion("currency not in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyBetween(String value1, String value2) {
            addCriterion("currency between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotBetween(String value1, String value2) {
            addCriterion("currency not between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andDailyBatchNumIsNull() {
            addCriterion("daily_batch_num is null");
            return (Criteria) this;
        }

        public Criteria andDailyBatchNumIsNotNull() {
            addCriterion("daily_batch_num is not null");
            return (Criteria) this;
        }

        public Criteria andDailyBatchNumEqualTo(String value) {
            addCriterion("daily_batch_num =", value, "dailyBatchNum");
            return (Criteria) this;
        }

        public Criteria andDailyBatchNumNotEqualTo(String value) {
            addCriterion("daily_batch_num <>", value, "dailyBatchNum");
            return (Criteria) this;
        }

        public Criteria andDailyBatchNumGreaterThan(String value) {
            addCriterion("daily_batch_num >", value, "dailyBatchNum");
            return (Criteria) this;
        }

        public Criteria andDailyBatchNumGreaterThanOrEqualTo(String value) {
            addCriterion("daily_batch_num >=", value, "dailyBatchNum");
            return (Criteria) this;
        }

        public Criteria andDailyBatchNumLessThan(String value) {
            addCriterion("daily_batch_num <", value, "dailyBatchNum");
            return (Criteria) this;
        }

        public Criteria andDailyBatchNumLessThanOrEqualTo(String value) {
            addCriterion("daily_batch_num <=", value, "dailyBatchNum");
            return (Criteria) this;
        }

        public Criteria andDailyBatchNumLike(String value) {
            addCriterion("daily_batch_num like", value, "dailyBatchNum");
            return (Criteria) this;
        }

        public Criteria andDailyBatchNumNotLike(String value) {
            addCriterion("daily_batch_num not like", value, "dailyBatchNum");
            return (Criteria) this;
        }

        public Criteria andDailyBatchNumIn(List<String> values) {
            addCriterion("daily_batch_num in", values, "dailyBatchNum");
            return (Criteria) this;
        }

        public Criteria andDailyBatchNumNotIn(List<String> values) {
            addCriterion("daily_batch_num not in", values, "dailyBatchNum");
            return (Criteria) this;
        }

        public Criteria andDailyBatchNumBetween(String value1, String value2) {
            addCriterion("daily_batch_num between", value1, value2, "dailyBatchNum");
            return (Criteria) this;
        }

        public Criteria andDailyBatchNumNotBetween(String value1, String value2) {
            addCriterion("daily_batch_num not between", value1, value2, "dailyBatchNum");
            return (Criteria) this;
        }

        public Criteria andDailyBatchNameIsNull() {
            addCriterion("daily_batch_name is null");
            return (Criteria) this;
        }

        public Criteria andDailyBatchNameIsNotNull() {
            addCriterion("daily_batch_name is not null");
            return (Criteria) this;
        }

        public Criteria andDailyBatchNameEqualTo(String value) {
            addCriterion("daily_batch_name =", value, "dailyBatchName");
            return (Criteria) this;
        }

        public Criteria andDailyBatchNameNotEqualTo(String value) {
            addCriterion("daily_batch_name <>", value, "dailyBatchName");
            return (Criteria) this;
        }

        public Criteria andDailyBatchNameGreaterThan(String value) {
            addCriterion("daily_batch_name >", value, "dailyBatchName");
            return (Criteria) this;
        }

        public Criteria andDailyBatchNameGreaterThanOrEqualTo(String value) {
            addCriterion("daily_batch_name >=", value, "dailyBatchName");
            return (Criteria) this;
        }

        public Criteria andDailyBatchNameLessThan(String value) {
            addCriterion("daily_batch_name <", value, "dailyBatchName");
            return (Criteria) this;
        }

        public Criteria andDailyBatchNameLessThanOrEqualTo(String value) {
            addCriterion("daily_batch_name <=", value, "dailyBatchName");
            return (Criteria) this;
        }

        public Criteria andDailyBatchNameLike(String value) {
            addCriterion("daily_batch_name like", value, "dailyBatchName");
            return (Criteria) this;
        }

        public Criteria andDailyBatchNameNotLike(String value) {
            addCriterion("daily_batch_name not like", value, "dailyBatchName");
            return (Criteria) this;
        }

        public Criteria andDailyBatchNameIn(List<String> values) {
            addCriterion("daily_batch_name in", values, "dailyBatchName");
            return (Criteria) this;
        }

        public Criteria andDailyBatchNameNotIn(List<String> values) {
            addCriterion("daily_batch_name not in", values, "dailyBatchName");
            return (Criteria) this;
        }

        public Criteria andDailyBatchNameBetween(String value1, String value2) {
            addCriterion("daily_batch_name between", value1, value2, "dailyBatchName");
            return (Criteria) this;
        }

        public Criteria andDailyBatchNameNotBetween(String value1, String value2) {
            addCriterion("daily_batch_name not between", value1, value2, "dailyBatchName");
            return (Criteria) this;
        }

        public Criteria andDebitSubjectIsNull() {
            addCriterion("debit_subject is null");
            return (Criteria) this;
        }

        public Criteria andDebitSubjectIsNotNull() {
            addCriterion("debit_subject is not null");
            return (Criteria) this;
        }

        public Criteria andDebitSubjectEqualTo(String value) {
            addCriterion("debit_subject =", value, "debitSubject");
            return (Criteria) this;
        }

        public Criteria andDebitSubjectNotEqualTo(String value) {
            addCriterion("debit_subject <>", value, "debitSubject");
            return (Criteria) this;
        }

        public Criteria andDebitSubjectGreaterThan(String value) {
            addCriterion("debit_subject >", value, "debitSubject");
            return (Criteria) this;
        }

        public Criteria andDebitSubjectGreaterThanOrEqualTo(String value) {
            addCriterion("debit_subject >=", value, "debitSubject");
            return (Criteria) this;
        }

        public Criteria andDebitSubjectLessThan(String value) {
            addCriterion("debit_subject <", value, "debitSubject");
            return (Criteria) this;
        }

        public Criteria andDebitSubjectLessThanOrEqualTo(String value) {
            addCriterion("debit_subject <=", value, "debitSubject");
            return (Criteria) this;
        }

        public Criteria andDebitSubjectLike(String value) {
            addCriterion("debit_subject like", value, "debitSubject");
            return (Criteria) this;
        }

        public Criteria andDebitSubjectNotLike(String value) {
            addCriterion("debit_subject not like", value, "debitSubject");
            return (Criteria) this;
        }

        public Criteria andDebitSubjectIn(List<String> values) {
            addCriterion("debit_subject in", values, "debitSubject");
            return (Criteria) this;
        }

        public Criteria andDebitSubjectNotIn(List<String> values) {
            addCriterion("debit_subject not in", values, "debitSubject");
            return (Criteria) this;
        }

        public Criteria andDebitSubjectBetween(String value1, String value2) {
            addCriterion("debit_subject between", value1, value2, "debitSubject");
            return (Criteria) this;
        }

        public Criteria andDebitSubjectNotBetween(String value1, String value2) {
            addCriterion("debit_subject not between", value1, value2, "debitSubject");
            return (Criteria) this;
        }

        public Criteria andCreditSubjectIsNull() {
            addCriterion("credit_subject is null");
            return (Criteria) this;
        }

        public Criteria andCreditSubjectIsNotNull() {
            addCriterion("credit_subject is not null");
            return (Criteria) this;
        }

        public Criteria andCreditSubjectEqualTo(String value) {
            addCriterion("credit_subject =", value, "creditSubject");
            return (Criteria) this;
        }

        public Criteria andCreditSubjectNotEqualTo(String value) {
            addCriterion("credit_subject <>", value, "creditSubject");
            return (Criteria) this;
        }

        public Criteria andCreditSubjectGreaterThan(String value) {
            addCriterion("credit_subject >", value, "creditSubject");
            return (Criteria) this;
        }

        public Criteria andCreditSubjectGreaterThanOrEqualTo(String value) {
            addCriterion("credit_subject >=", value, "creditSubject");
            return (Criteria) this;
        }

        public Criteria andCreditSubjectLessThan(String value) {
            addCriterion("credit_subject <", value, "creditSubject");
            return (Criteria) this;
        }

        public Criteria andCreditSubjectLessThanOrEqualTo(String value) {
            addCriterion("credit_subject <=", value, "creditSubject");
            return (Criteria) this;
        }

        public Criteria andCreditSubjectLike(String value) {
            addCriterion("credit_subject like", value, "creditSubject");
            return (Criteria) this;
        }

        public Criteria andCreditSubjectNotLike(String value) {
            addCriterion("credit_subject not like", value, "creditSubject");
            return (Criteria) this;
        }

        public Criteria andCreditSubjectIn(List<String> values) {
            addCriterion("credit_subject in", values, "creditSubject");
            return (Criteria) this;
        }

        public Criteria andCreditSubjectNotIn(List<String> values) {
            addCriterion("credit_subject not in", values, "creditSubject");
            return (Criteria) this;
        }

        public Criteria andCreditSubjectBetween(String value1, String value2) {
            addCriterion("credit_subject between", value1, value2, "creditSubject");
            return (Criteria) this;
        }

        public Criteria andCreditSubjectNotBetween(String value1, String value2) {
            addCriterion("credit_subject not between", value1, value2, "creditSubject");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andErpStatusIsNull() {
            addCriterion("erp_status is null");
            return (Criteria) this;
        }

        public Criteria andErpStatusIsNotNull() {
            addCriterion("erp_status is not null");
            return (Criteria) this;
        }

        public Criteria andErpStatusEqualTo(Integer value) {
            addCriterion("erp_status =", value, "erpStatus");
            return (Criteria) this;
        }

        public Criteria andErpStatusNotEqualTo(Integer value) {
            addCriterion("erp_status <>", value, "erpStatus");
            return (Criteria) this;
        }

        public Criteria andErpStatusGreaterThan(Integer value) {
            addCriterion("erp_status >", value, "erpStatus");
            return (Criteria) this;
        }

        public Criteria andErpStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("erp_status >=", value, "erpStatus");
            return (Criteria) this;
        }

        public Criteria andErpStatusLessThan(Integer value) {
            addCriterion("erp_status <", value, "erpStatus");
            return (Criteria) this;
        }

        public Criteria andErpStatusLessThanOrEqualTo(Integer value) {
            addCriterion("erp_status <=", value, "erpStatus");
            return (Criteria) this;
        }

        public Criteria andErpStatusIn(List<Integer> values) {
            addCriterion("erp_status in", values, "erpStatus");
            return (Criteria) this;
        }

        public Criteria andErpStatusNotIn(List<Integer> values) {
            addCriterion("erp_status not in", values, "erpStatus");
            return (Criteria) this;
        }

        public Criteria andErpStatusBetween(Integer value1, Integer value2) {
            addCriterion("erp_status between", value1, value2, "erpStatus");
            return (Criteria) this;
        }

        public Criteria andErpStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("erp_status not between", value1, value2, "erpStatus");
            return (Criteria) this;
        }

        public Criteria andErpMessageIsNull() {
            addCriterion("erp_message is null");
            return (Criteria) this;
        }

        public Criteria andErpMessageIsNotNull() {
            addCriterion("erp_message is not null");
            return (Criteria) this;
        }

        public Criteria andErpMessageEqualTo(String value) {
            addCriterion("erp_message =", value, "erpMessage");
            return (Criteria) this;
        }

        public Criteria andErpMessageNotEqualTo(String value) {
            addCriterion("erp_message <>", value, "erpMessage");
            return (Criteria) this;
        }

        public Criteria andErpMessageGreaterThan(String value) {
            addCriterion("erp_message >", value, "erpMessage");
            return (Criteria) this;
        }

        public Criteria andErpMessageGreaterThanOrEqualTo(String value) {
            addCriterion("erp_message >=", value, "erpMessage");
            return (Criteria) this;
        }

        public Criteria andErpMessageLessThan(String value) {
            addCriterion("erp_message <", value, "erpMessage");
            return (Criteria) this;
        }

        public Criteria andErpMessageLessThanOrEqualTo(String value) {
            addCriterion("erp_message <=", value, "erpMessage");
            return (Criteria) this;
        }

        public Criteria andErpMessageLike(String value) {
            addCriterion("erp_message like", value, "erpMessage");
            return (Criteria) this;
        }

        public Criteria andErpMessageNotLike(String value) {
            addCriterion("erp_message not like", value, "erpMessage");
            return (Criteria) this;
        }

        public Criteria andErpMessageIn(List<String> values) {
            addCriterion("erp_message in", values, "erpMessage");
            return (Criteria) this;
        }

        public Criteria andErpMessageNotIn(List<String> values) {
            addCriterion("erp_message not in", values, "erpMessage");
            return (Criteria) this;
        }

        public Criteria andErpMessageBetween(String value1, String value2) {
            addCriterion("erp_message between", value1, value2, "erpMessage");
            return (Criteria) this;
        }

        public Criteria andErpMessageNotBetween(String value1, String value2) {
            addCriterion("erp_message not between", value1, value2, "erpMessage");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andWriteOffReasonIsNull() {
            addCriterion("write_off_reason is null");
            return (Criteria) this;
        }

        public Criteria andWriteOffReasonIsNotNull() {
            addCriterion("write_off_reason is not null");
            return (Criteria) this;
        }

        public Criteria andWriteOffReasonEqualTo(String value) {
            addCriterion("write_off_reason =", value, "writeOffReason");
            return (Criteria) this;
        }

        public Criteria andWriteOffReasonNotEqualTo(String value) {
            addCriterion("write_off_reason <>", value, "writeOffReason");
            return (Criteria) this;
        }

        public Criteria andWriteOffReasonGreaterThan(String value) {
            addCriterion("write_off_reason >", value, "writeOffReason");
            return (Criteria) this;
        }

        public Criteria andWriteOffReasonGreaterThanOrEqualTo(String value) {
            addCriterion("write_off_reason >=", value, "writeOffReason");
            return (Criteria) this;
        }

        public Criteria andWriteOffReasonLessThan(String value) {
            addCriterion("write_off_reason <", value, "writeOffReason");
            return (Criteria) this;
        }

        public Criteria andWriteOffReasonLessThanOrEqualTo(String value) {
            addCriterion("write_off_reason <=", value, "writeOffReason");
            return (Criteria) this;
        }

        public Criteria andWriteOffReasonLike(String value) {
            addCriterion("write_off_reason like", value, "writeOffReason");
            return (Criteria) this;
        }

        public Criteria andWriteOffReasonNotLike(String value) {
            addCriterion("write_off_reason not like", value, "writeOffReason");
            return (Criteria) this;
        }

        public Criteria andWriteOffReasonIn(List<String> values) {
            addCriterion("write_off_reason in", values, "writeOffReason");
            return (Criteria) this;
        }

        public Criteria andWriteOffReasonNotIn(List<String> values) {
            addCriterion("write_off_reason not in", values, "writeOffReason");
            return (Criteria) this;
        }

        public Criteria andWriteOffReasonBetween(String value1, String value2) {
            addCriterion("write_off_reason between", value1, value2, "writeOffReason");
            return (Criteria) this;
        }

        public Criteria andWriteOffReasonNotBetween(String value1, String value2) {
            addCriterion("write_off_reason not between", value1, value2, "writeOffReason");
            return (Criteria) this;
        }

        public Criteria andWriteOffUserIsNull() {
            addCriterion("write_off_user is null");
            return (Criteria) this;
        }

        public Criteria andWriteOffUserIsNotNull() {
            addCriterion("write_off_user is not null");
            return (Criteria) this;
        }

        public Criteria andWriteOffUserEqualTo(Long value) {
            addCriterion("write_off_user =", value, "writeOffUser");
            return (Criteria) this;
        }

        public Criteria andWriteOffUserNotEqualTo(Long value) {
            addCriterion("write_off_user <>", value, "writeOffUser");
            return (Criteria) this;
        }

        public Criteria andWriteOffUserGreaterThan(Long value) {
            addCriterion("write_off_user >", value, "writeOffUser");
            return (Criteria) this;
        }

        public Criteria andWriteOffUserGreaterThanOrEqualTo(Long value) {
            addCriterion("write_off_user >=", value, "writeOffUser");
            return (Criteria) this;
        }

        public Criteria andWriteOffUserLessThan(Long value) {
            addCriterion("write_off_user <", value, "writeOffUser");
            return (Criteria) this;
        }

        public Criteria andWriteOffUserLessThanOrEqualTo(Long value) {
            addCriterion("write_off_user <=", value, "writeOffUser");
            return (Criteria) this;
        }

        public Criteria andWriteOffUserIn(List<Long> values) {
            addCriterion("write_off_user in", values, "writeOffUser");
            return (Criteria) this;
        }

        public Criteria andWriteOffUserNotIn(List<Long> values) {
            addCriterion("write_off_user not in", values, "writeOffUser");
            return (Criteria) this;
        }

        public Criteria andWriteOffUserBetween(Long value1, Long value2) {
            addCriterion("write_off_user between", value1, value2, "writeOffUser");
            return (Criteria) this;
        }

        public Criteria andWriteOffUserNotBetween(Long value1, Long value2) {
            addCriterion("write_off_user not between", value1, value2, "writeOffUser");
            return (Criteria) this;
        }

        public Criteria andWriteOffTimeIsNull() {
            addCriterion("write_off_time is null");
            return (Criteria) this;
        }

        public Criteria andWriteOffTimeIsNotNull() {
            addCriterion("write_off_time is not null");
            return (Criteria) this;
        }

        public Criteria andWriteOffTimeEqualTo(Date value) {
            addCriterion("write_off_time =", value, "writeOffTime");
            return (Criteria) this;
        }

        public Criteria andWriteOffTimeNotEqualTo(Date value) {
            addCriterion("write_off_time <>", value, "writeOffTime");
            return (Criteria) this;
        }

        public Criteria andWriteOffTimeGreaterThan(Date value) {
            addCriterion("write_off_time >", value, "writeOffTime");
            return (Criteria) this;
        }

        public Criteria andWriteOffTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("write_off_time >=", value, "writeOffTime");
            return (Criteria) this;
        }

        public Criteria andWriteOffTimeLessThan(Date value) {
            addCriterion("write_off_time <", value, "writeOffTime");
            return (Criteria) this;
        }

        public Criteria andWriteOffTimeLessThanOrEqualTo(Date value) {
            addCriterion("write_off_time <=", value, "writeOffTime");
            return (Criteria) this;
        }

        public Criteria andWriteOffTimeIn(List<Date> values) {
            addCriterion("write_off_time in", values, "writeOffTime");
            return (Criteria) this;
        }

        public Criteria andWriteOffTimeNotIn(List<Date> values) {
            addCriterion("write_off_time not in", values, "writeOffTime");
            return (Criteria) this;
        }

        public Criteria andWriteOffTimeBetween(Date value1, Date value2) {
            addCriterion("write_off_time between", value1, value2, "writeOffTime");
            return (Criteria) this;
        }

        public Criteria andWriteOffTimeNotBetween(Date value1, Date value2) {
            addCriterion("write_off_time not between", value1, value2, "writeOffTime");
            return (Criteria) this;
        }

        public Criteria andWriteOffFileIsNull() {
            addCriterion("write_off_file is null");
            return (Criteria) this;
        }

        public Criteria andWriteOffFileIsNotNull() {
            addCriterion("write_off_file is not null");
            return (Criteria) this;
        }

        public Criteria andWriteOffFileEqualTo(String value) {
            addCriterion("write_off_file =", value, "writeOffFile");
            return (Criteria) this;
        }

        public Criteria andWriteOffFileNotEqualTo(String value) {
            addCriterion("write_off_file <>", value, "writeOffFile");
            return (Criteria) this;
        }

        public Criteria andWriteOffFileGreaterThan(String value) {
            addCriterion("write_off_file >", value, "writeOffFile");
            return (Criteria) this;
        }

        public Criteria andWriteOffFileGreaterThanOrEqualTo(String value) {
            addCriterion("write_off_file >=", value, "writeOffFile");
            return (Criteria) this;
        }

        public Criteria andWriteOffFileLessThan(String value) {
            addCriterion("write_off_file <", value, "writeOffFile");
            return (Criteria) this;
        }

        public Criteria andWriteOffFileLessThanOrEqualTo(String value) {
            addCriterion("write_off_file <=", value, "writeOffFile");
            return (Criteria) this;
        }

        public Criteria andWriteOffFileLike(String value) {
            addCriterion("write_off_file like", value, "writeOffFile");
            return (Criteria) this;
        }

        public Criteria andWriteOffFileNotLike(String value) {
            addCriterion("write_off_file not like", value, "writeOffFile");
            return (Criteria) this;
        }

        public Criteria andWriteOffFileIn(List<String> values) {
            addCriterion("write_off_file in", values, "writeOffFile");
            return (Criteria) this;
        }

        public Criteria andWriteOffFileNotIn(List<String> values) {
            addCriterion("write_off_file not in", values, "writeOffFile");
            return (Criteria) this;
        }

        public Criteria andWriteOffFileBetween(String value1, String value2) {
            addCriterion("write_off_file between", value1, value2, "writeOffFile");
            return (Criteria) this;
        }

        public Criteria andWriteOffFileNotBetween(String value1, String value2) {
            addCriterion("write_off_file not between", value1, value2, "writeOffFile");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andWriteOffStatusIsNull() {
            addCriterion("write_off_status is null");
            return (Criteria) this;
        }

        public Criteria andWriteOffStatusIsNotNull() {
            addCriterion("write_off_status is not null");
            return (Criteria) this;
        }

        public Criteria andWriteOffStatusEqualTo(Integer value) {
            addCriterion("write_off_status =", value, "writeOffStatus");
            return (Criteria) this;
        }

        public Criteria andWriteOffStatusNotEqualTo(Integer value) {
            addCriterion("write_off_status <>", value, "writeOffStatus");
            return (Criteria) this;
        }

        public Criteria andWriteOffStatusGreaterThan(Integer value) {
            addCriterion("write_off_status >", value, "writeOffStatus");
            return (Criteria) this;
        }

        public Criteria andWriteOffStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("write_off_status >=", value, "writeOffStatus");
            return (Criteria) this;
        }

        public Criteria andWriteOffStatusLessThan(Integer value) {
            addCriterion("write_off_status <", value, "writeOffStatus");
            return (Criteria) this;
        }

        public Criteria andWriteOffStatusLessThanOrEqualTo(Integer value) {
            addCriterion("write_off_status <=", value, "writeOffStatus");
            return (Criteria) this;
        }

        public Criteria andWriteOffStatusIn(List<Integer> values) {
            addCriterion("write_off_status in", values, "writeOffStatus");
            return (Criteria) this;
        }

        public Criteria andWriteOffStatusNotIn(List<Integer> values) {
            addCriterion("write_off_status not in", values, "writeOffStatus");
            return (Criteria) this;
        }

        public Criteria andWriteOffStatusBetween(Integer value1, Integer value2) {
            addCriterion("write_off_status between", value1, value2, "writeOffStatus");
            return (Criteria) this;
        }

        public Criteria andWriteOffStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("write_off_status not between", value1, value2, "writeOffStatus");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}