package com.midea.pam.common.ctc.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Description 领料单打印
 * @version 1.0
 * @date 2021/3/19 下午 15:52
 */
@Getter
@Setter
public class MaterialOrderPrintDto {
    @ApiModelProperty(value = "领料单号")
    private String PICKING_REQUISITION_NO;

    @ApiModelProperty(value = "工单任务号")
    private String ORDER_TASK_CODE;

    @ApiModelProperty(value = "领料类型")
    private String PICKING_TYPE;

    @ApiModelProperty(value = "申请日期")
    private String APPLY_DATE;

    @ApiModelProperty(value = "项目编号")
    private String PROJECT_CODE;

    @ApiModelProperty(value = "库存组织")
    private String INVENTORY_ORG;

    @ApiModelProperty(value = "仓库编号")
    private String STORE_CODE;

    @ApiModelProperty(value = "领料货位")
    private String PICKING_SHELVES;

    @ApiModelProperty(value = "模组号")
    private String MODULE_CODE;

    @ApiModelProperty(value = "模组名称")
    private String MODULE_NAME;

    @ApiModelProperty(value = "装配件描述")
    private String ASSEMBLY_DES;

    @ApiModelProperty(value = "备注")
    private String MARK;

    @ApiModelProperty(value = "序号")
    private String INDEX;

    @ApiModelProperty(value = "物料编号")
    private String MATERIAL_CODE;

    @ApiModelProperty(value = "物料描述")
    private String MATERIAL_DES;

    @ApiModelProperty(value = "单位")
    private String UNIT;

    @ApiModelProperty(value = "货架")
    private String SHELVES;

    @ApiModelProperty(value = "申请数量")
    private String APPLY_NUM;

    @ApiModelProperty(value = "实发数量")
    private String NUM;

    @ApiModelProperty(value = "备注2")
    private String MARK2;

    @ApiModelProperty(value = "领料人")
    private String PICKING_BY;

    @ApiModelProperty(value = "制单人")
    private String PRODUCER;

    @ApiModelProperty(value = "审批人")
    private String APPROVED_BY;

    @ApiModelProperty(value = "WBS")
    private String WBS_SUMMARY_CODE;

    @ApiModelProperty(value = "WBS描述")
    private String WBS_DESCRIPTION;

}
