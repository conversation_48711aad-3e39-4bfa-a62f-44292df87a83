package com.midea.pam.common.gateway.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;

public class RocketMQMsgConsumerRecord extends LongIdEntity implements Serializable {
    private String operationId;

    private String topic;

    private String msgId;

    private Boolean deletedFlag;

    private byte[] messages;

    private static final long serialVersionUID = 1L;

    public String getOperationId() {
        return operationId;
    }

    public void setOperationId(String operationId) {
        this.operationId = operationId == null ? null : operationId.trim();
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic == null ? null : topic.trim();
    }

    public String getMsgId() {
        return msgId;
    }

    public void setMsgId(String msgId) {
        this.msgId = msgId == null ? null : msgId.trim();
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public byte[] getMessages() {
        return messages;
    }

    public void setMessages(byte[] messages) {
        this.messages = messages;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", operationId=").append(operationId);
        sb.append(", topic=").append(topic);
        sb.append(", msgId=").append(msgId);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", messages=").append(messages);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}