package com.midea.pam.common.ctc.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "工时成本入账")
public class WorkingHourAccounting extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "业务实体id")
    private Long ouId;

    @ApiModelProperty(value = "业务实体名称")
    private String ouName;

    @ApiModelProperty(value = "单号")
    private String code;

    @ApiModelProperty(value = "已填报工时")
    private BigDecimal totalWorkingHour;

    @ApiModelProperty(value = "已发生人工成本")
    private BigDecimal totalLaborCost;

    @ApiModelProperty(value = "会计期间")
    private String glPeriod;

    @ApiModelProperty(value = "总账日期")
    private Date glDate;

    @ApiModelProperty(value = "出勤月份")
    private String applyMonth;

    @ApiModelProperty(value = "审批月份")
    private String approveMonth;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "日记账批名")
    private String dailyBatchNum;

    @ApiModelProperty(value = "日记账单名称")
    private String dailyBatchName;

    @ApiModelProperty(value = "借方科目")
    private String debitSubject;

    @ApiModelProperty(value = "贷方科目")
    private String creditSubject;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "ERP记账状态")
    private Integer erpStatus;

    @ApiModelProperty(value = "ERP失败原因")
    private String erpMessage;

    private Boolean deletedFlag;

    @ApiModelProperty(value = "冲销原因")
    private String writeOffReason;

    @ApiModelProperty(value = "冲销人")
    private Long writeOffUser;

    @ApiModelProperty(value = "冲销日期")
    private Date writeOffTime;

    @ApiModelProperty(value = "冲销附件")
    private String writeOffFile;

    @ApiModelProperty(value = "生效状态,，0-生效，1-作废")
    private Integer status;

    @ApiModelProperty(value = "红冲标识，1：未冲销，2：已冲销，3：不可冲销")
    private Integer writeOffStatus;

    private static final long serialVersionUID = 1L;

    public Long getOuId() {
        return ouId;
    }

    public void setOuId(Long ouId) {
        this.ouId = ouId;
    }

    public String getOuName() {
        return ouName;
    }

    public void setOuName(String ouName) {
        this.ouName = ouName == null ? null : ouName.trim();
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    public BigDecimal getTotalWorkingHour() {
        return totalWorkingHour;
    }

    public void setTotalWorkingHour(BigDecimal totalWorkingHour) {
        this.totalWorkingHour = totalWorkingHour;
    }

    public BigDecimal getTotalLaborCost() {
        return totalLaborCost;
    }

    public void setTotalLaborCost(BigDecimal totalLaborCost) {
        this.totalLaborCost = totalLaborCost;
    }

    public String getGlPeriod() {
        return glPeriod;
    }

    public void setGlPeriod(String glPeriod) {
        this.glPeriod = glPeriod == null ? null : glPeriod.trim();
    }

    public Date getGlDate() {
        return glDate;
    }

    public void setGlDate(Date glDate) {
        this.glDate = glDate;
    }

    public String getApplyMonth() {
        return applyMonth;
    }

    public void setApplyMonth(String applyMonth) {
        this.applyMonth = applyMonth == null ? null : applyMonth.trim();
    }

    public String getApproveMonth() {
        return approveMonth;
    }

    public void setApproveMonth(String approveMonth) {
        this.approveMonth = approveMonth == null ? null : approveMonth.trim();
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    public String getDailyBatchNum() {
        return dailyBatchNum;
    }

    public void setDailyBatchNum(String dailyBatchNum) {
        this.dailyBatchNum = dailyBatchNum == null ? null : dailyBatchNum.trim();
    }

    public String getDailyBatchName() {
        return dailyBatchName;
    }

    public void setDailyBatchName(String dailyBatchName) {
        this.dailyBatchName = dailyBatchName == null ? null : dailyBatchName.trim();
    }

    public String getDebitSubject() {
        return debitSubject;
    }

    public void setDebitSubject(String debitSubject) {
        this.debitSubject = debitSubject == null ? null : debitSubject.trim();
    }

    public String getCreditSubject() {
        return creditSubject;
    }

    public void setCreditSubject(String creditSubject) {
        this.creditSubject = creditSubject == null ? null : creditSubject.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Integer getErpStatus() {
        return erpStatus;
    }

    public void setErpStatus(Integer erpStatus) {
        this.erpStatus = erpStatus;
    }

    public String getErpMessage() {
        return erpMessage;
    }

    public void setErpMessage(String erpMessage) {
        this.erpMessage = erpMessage == null ? null : erpMessage.trim();
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public String getWriteOffReason() {
        return writeOffReason;
    }

    public void setWriteOffReason(String writeOffReason) {
        this.writeOffReason = writeOffReason == null ? null : writeOffReason.trim();
    }

    public Long getWriteOffUser() {
        return writeOffUser;
    }

    public void setWriteOffUser(Long writeOffUser) {
        this.writeOffUser = writeOffUser;
    }

    public Date getWriteOffTime() {
        return writeOffTime;
    }

    public void setWriteOffTime(Date writeOffTime) {
        this.writeOffTime = writeOffTime;
    }

    public String getWriteOffFile() {
        return writeOffFile;
    }

    public void setWriteOffFile(String writeOffFile) {
        this.writeOffFile = writeOffFile == null ? null : writeOffFile.trim();
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getWriteOffStatus() {
        return writeOffStatus;
    }

    public void setWriteOffStatus(Integer writeOffStatus) {
        this.writeOffStatus = writeOffStatus;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", ouId=").append(ouId);
        sb.append(", ouName=").append(ouName);
        sb.append(", code=").append(code);
        sb.append(", totalWorkingHour=").append(totalWorkingHour);
        sb.append(", totalLaborCost=").append(totalLaborCost);
        sb.append(", glPeriod=").append(glPeriod);
        sb.append(", glDate=").append(glDate);
        sb.append(", applyMonth=").append(applyMonth);
        sb.append(", approveMonth=").append(approveMonth);
        sb.append(", currency=").append(currency);
        sb.append(", dailyBatchNum=").append(dailyBatchNum);
        sb.append(", dailyBatchName=").append(dailyBatchName);
        sb.append(", debitSubject=").append(debitSubject);
        sb.append(", creditSubject=").append(creditSubject);
        sb.append(", remark=").append(remark);
        sb.append(", erpStatus=").append(erpStatus);
        sb.append(", erpMessage=").append(erpMessage);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", writeOffReason=").append(writeOffReason);
        sb.append(", writeOffUser=").append(writeOffUser);
        sb.append(", writeOffTime=").append(writeOffTime);
        sb.append(", writeOffFile=").append(writeOffFile);
        sb.append(", status=").append(status);
        sb.append(", writeOffStatus=").append(writeOffStatus);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}