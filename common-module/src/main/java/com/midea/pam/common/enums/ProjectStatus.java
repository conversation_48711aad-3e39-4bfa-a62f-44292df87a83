package com.midea.pam.common.enums;

import java.util.Objects;

/**
 * 项目状态枚举类.
 *
 * <AUTHOR>
 */
public enum ProjectStatus {

    SAVE(0, "草稿"),
    APPROVALING(3, "审批中"),
    APPROVALED(4, "项目进行中"),
    CHANGING(9, "项目变更中"),
    REFUSE(-2, "审批驳回"),
    RETURN(11, "审批撤回"),
    INVALID(12, "作废"),

    RETURN_PRE(13, "预立项审批撤回"),
    REFUSE_PRE(-4, "预立项审批驳回"),

    APPROVALING_PREVIEW(7, "预立项转正审批中"),
    REFUSE_PREVIEW(-3, "预立项转正驳回"),
    APPROVE_PREVIEWTOFORMAL_RETURN(14, "预立项转正撤回"),  //无引用

    CLOSE(10, "结项"),
    TERMINATIONING(15, "项目终止审批中"),
    TERMINATION(16, "项目终止"),
    CANCEL(17, "项目流程删除"),              //前端无枚举，数据库不存在但引用很多
    CLOSED_ITEM(18, "结项项目重新打开审批中"),
    END_ITEM(19, "终止项目重新打开审批中"),
    REFUSE_FINANCE(-1, "财务驳回"),         //前端无枚举
    FINANCE_CONFIRMING(1, "财务确认中"),     //无引用
    ;


    private int code;
    private String name;

    ProjectStatus(int code, String name) {
        this.name = name;
        this.code = code;
    }

    //覆盖方法
    @Override
    public String toString() {
        return this.code + "_" + this.name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getValue(final Integer code) {
        for (ProjectStatus status : ProjectStatus.values()) {
            if (status.getCode() == code) {
                return status.getName();
            }
        }
        return null;
    }

    public boolean is(Integer code) {
        return Objects.equals(this.code, code);
    }
}
