package com.midea.pam.common.basedata.entity;

import com.midea.pam.common.base.LongIdEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@ToString
public class MilepostDesignPlanMiddle extends LongIdEntity implements Serializable {
    private Long id;

    private Long organizationId;

    private Long formId;

    private Long markId;

    private String pamCode;

    private String erpCode;

    private String materielDescr;

    private String unit;

    private String unitCode;

    private BigDecimal number;

    private String name;

    private String model;

    private String brand;

    private String materialClassification;

    private String materielType;

    private String materialCategory;

    private String codingMiddleClass;

    private String figureNumber;

    private String chartVersion;

    private String brandMaterialCode;

    private String orSparePartsMask;

    private Long perchasingLeadtime;

    private Long minPerchaseQuantity;

    private Long minPackageQuantity;

    private String machiningPartType;

    private String material;

    private BigDecimal unitWeight;

    private String materialProcessing;

    private Boolean deletedFlag;

    private Long createBy;

    private Date createAt;

    private Long updateBy;

    private Date updateAt;

    @ApiModelProperty(value = "项目预算的WBS的上层WBS或;项目预算的WBS;")
    private String wbsSummaryCode;

    @ApiModelProperty(value = "WBS层级")
    private String wbsLayer;

    @ApiModelProperty(value = "是否急件")
    private Boolean dispatchIs;

    @ApiModelProperty(value = "是否外包")
    private Boolean extIs;

    @ApiModelProperty(value = "活动事项编码")
    private String activityCode;

    @ApiModelProperty(value = "项目预算类别属性")
    private String projectBudgetType;

    @ApiModelProperty(value = "设计人员")
    private String planDesigner;

    @ApiModelProperty(value = "设计发布批次号")
    private String designReleaseLotNumber;

    @ApiModelProperty(value = "备注")
    private String description;

    @ApiModelProperty(value = "最大图纸版本号")
    private Long maximumDrawVersionNum;

    private static final long serialVersionUID = 1L;

}