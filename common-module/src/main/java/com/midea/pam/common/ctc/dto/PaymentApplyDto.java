package com.midea.pam.common.ctc.dto;

import com.midea.pam.common.basedata.dto.VendorSiteBankDto;
import com.midea.pam.common.ctc.entity.PaymentApply;
import com.midea.pam.common.ctc.entity.PaymentApplyExternalAttachment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@ApiModel(value = "付款申请数据传输对象")
public class PaymentApplyDto extends PaymentApply {

    @ApiModelProperty(value = "附件列表")
    private List<CtcAttachmentDto> attachmentRelations;

    @ApiModelProperty(value = "关联里程碑信息")
    private ProjectMilepostDto projectMilepostDto;

    @ApiModelProperty(value = "罚扣款信息")
    private List<PaymentPenaltyProfitDto> paymentPenaltyProfits;

    @ApiModelProperty(value = "发票信息头")
    private List<PaymentInvoiceDto> paymentInvoices;

    @ApiModelProperty(value = "付款计划笔数")
    private Integer num;

    @ApiModelProperty(value = "发票信息行")
    private List<PaymentInvoiceDetailDto> paymentInvoiceDetails;

    @ApiModelProperty(value = "法务合同编号")
    private String legalAffairsCode;

    @ApiModelProperty(value = "实际付款记录列表")
    private List<PaymentRecordDto> paymentRecordDtos;

    @ApiModelProperty(value = "已退款金额")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "认款金额")
    private BigDecimal claimAmount;

    @ApiModelProperty(value = "累计付款金额（含税）-来源于付款申请关联的计划")
    private BigDecimal actualAmount;

    @ApiModelProperty(value = "累计在途金额（含税）-来源于付款申请关联的计划")
    private BigDecimal totalOnTheWayAmount;

    @ApiModelProperty(value = "累计罚扣款（含税）-来源于付款申请关联的计划")
    private BigDecimal totalpenaltyAmount;

    @ApiModelProperty(value = "付款申请状态（1草稿,2审核中,3驳回,4生效（支付中）,5作废，6支付成功，7、取消支付、8、部分支付、9、异常完结）")
    private String bizStatus;

    @ApiModelProperty("入账日期")
    private Date accountingDate;

    @ApiModelProperty(value = "联行号")
    private String combineBankNummber;

    @ApiModelProperty("供应商信息")
    private VendorSiteBankDto vendorSiteBank;

    @ApiModelProperty(value = "核销记录")
    private List<PaymentWriteOffRecordDto> writeOffRecordList;

    @ApiModelProperty("已入账税票金额（不含税）")
    private BigDecimal amountOfTaxReceipts;

    @ApiModelProperty("采购合同进度执行金额（不含税）")
    private BigDecimal purchasingContractProgressAmount;

    @ApiModelProperty("银行账号id")
    private Long bankAccountId;

    @ApiModelProperty("银行账号名")
    private String bankAccountName;

    private String contractTypeName;

    @ApiModelProperty(value = "是否显示外汇付款信息区域")
    private Boolean foreignFlag;

    @ApiModelProperty(value = "付款方法代码")
    private String paymentMethodCode;

    @ApiModelProperty(value = "付款方法类型：prepayment_type / payment_type / third_bill_payment_type")
    private String paymentMethodType;

    @ApiModelProperty(value = "外部附件列表")
    private List<PaymentApplyExternalAttachment> externalAttachmentList;
}
