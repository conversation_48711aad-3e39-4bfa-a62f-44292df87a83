package com.midea.pam.common.constants;

import io.swagger.annotations.ApiModelProperty;

public class ProjectWbsCostConstant {


    public static final String WBS = "wbs";
    public static final String ACTIVITY = "activity";
    public static final String SYSTEM_ACTIVITY = "System";
    public static final String ID = "id";
    public static final String ROW_ID = "rowId";
    public static final String ORIGIN_ID = "originId";
    public static final String PARENT_ID = "parentId";

    public static final String WBS_TEMPLATE_INFO_ID = "wbsTemplateInfoId";
    public static final String SYSTEM_ACTIVITY_CODE = "System";
    public static final String SYSTEM_ACTIVITY_ORDER_NO = "99999";
    public static final String SYSTEM_ACTIVITY_TYPE = "系统生成";
    public static final String SYSTEM_ACTIVITY_NAME = null;

    @ApiModelProperty(value = "活动事项编码")
    public static final String ACTIVITY_CODE = "activityCode";
    @ApiModelProperty(value = "活动类别名称")
    public static final String ACTIVITY_NAME = "activityName";
    @ApiModelProperty(value = "活动类别属性")
    public static final String ACTIVITY_TYPE = "activityType";
    @ApiModelProperty(value = "活动事项序号")
    public static final String ACTIVITY_ORDER_NO = "activityOrderNo";


    @ApiModelProperty(value = "预算金额")
    public static final String PRICE = "price";
    @ApiModelProperty(value = "预算基线")
    public static final String BASELINE_COST = "baselineCost";
    @ApiModelProperty(value = "预算基线-变更后")
    public static final String AFTER_CHANGE_BASELINE_COST = "afterChangeBaselineCost";
    @ApiModelProperty(value = "预算基线差异")
    public static final String DIFF_BASELINE_COST = "diffBaselineCost";
    @ApiModelProperty(value = "需求预算")
    public static final String DEMAND_COST = "demandCost";
    @ApiModelProperty(value = "在途成本")
    public static final String ON_THE_WAY_COST = "onTheWayCost";
    @ApiModelProperty(value = "已发生成本")
    public static final String INCURRED_COST = "incurredCost";
    @ApiModelProperty(value = "剩余可用预算", notes = "= 预算金额 - 需求预算 - 在途成本 - 已发生成本")
    public static final String REMAINING_COST = "remainingCost";
    @ApiModelProperty(value = "累计变更金额", notes = "= 预算金额 - 预算基线")
    public static final String CHANGE_ACCUMULATE_COST = "changeAccumulateCost";
    @ApiModelProperty(value = "累计变更金额", notes = "= 累计变更金额")
    public static final String CHANGE_PRICE = "changePrice";

    @ApiModelProperty(value = "拼接全wbs编码")
    public static final String WBS_FULL_CODE = "wbsFullCode";
    @ApiModelProperty(value = "最底层wbs编码")
    public static final String WBS_LAST_CODE = "wbsLastCode";


    @ApiModelProperty(value = "excel行数")
    public static final String EXCEL_ROW_NUM = "excelRowNum";

    @ApiModelProperty("汇总明细查询标记")
    public static final String PROJECT_DETAIL_SELECT_FLAG = "projectDetailSelectFlag";

    public static final String CODE = "code";

    @ApiModelProperty(value = "汇总查询编码")
    public static final String SUMMARY_CODE = "summaryCode";
    public static final String SUMMARY_TYPE = "summaryType";
    public static final String REMARK = "remark";
    public static final String PROJECT_ID = "projectId";
    public static final String PROJECT_CODE = "projectCode";
    public static final String PROJECT_NAME = "projectName";
    public static final String DELETED_FLAG = "deletedFlag";
    public static final String DESCRIPTION = "description";

    @ApiModelProperty(value = "项目成本待计算的范围")
    public static final String PROJECT_COST_REGION = "project_cost_region";
}
