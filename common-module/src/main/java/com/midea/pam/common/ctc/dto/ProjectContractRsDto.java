package com.midea.pam.common.ctc.dto;

import com.midea.pam.common.ctc.entity.ProjectContractRs;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel(value = "项目关联合同")
public class ProjectContractRsDto extends ProjectContractRs {

    @ApiModelProperty("原id")
    private Long originId;

    @ApiModelProperty("合同编码")
    private String contractCode;

    @ApiModelProperty("合同名称")
    private String contractName;

    @ApiModelProperty("客户id")
    private Long customerId;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("销售经理id")
    private Long salesManager;

    @ApiModelProperty("销售经理名称")
    private String salesManagerName;

    @ApiModelProperty("项目编码")
    private String projectCode;

    @ApiModelProperty("项目名称")
    private String projectName;

}
