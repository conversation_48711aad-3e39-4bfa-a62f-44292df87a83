package com.midea.pam.common.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 分布式自旋锁
 * <AUTHOR>
 */
public class DistributedCASLock {

    private static Logger logger = LoggerFactory.getLogger(DistributedCASLock.class);

    private static StringRedisTemplate stringRedisTemplate = null;

    /**
     * 尝试加锁，未获取锁直接返回
     *
     * @param key
     * @param value
     * @return
     */
    public static boolean tryLock(String key, String value) {
        if (null == stringRedisTemplate) {
            stringRedisTemplate = (StringRedisTemplate) ApplicationContextProvider.getBean("stringRedisTemplate");
        }
        return stringRedisTemplate.opsForValue().setIfAbsent(key, value);
    }

    /**
     * 加锁,未获取锁一直等待
     *
     * @param key
     * @param value
     * @return
     */
    public static boolean lock(String key, String value) {
        if (null == stringRedisTemplate) {
            stringRedisTemplate = (StringRedisTemplate) ApplicationContextProvider.getBean("stringRedisTemplate");
        }
        boolean flag = true;

        while (!stringRedisTemplate.opsForValue().setIfAbsent(key, value)) {
            try {
                TimeUnit.MICROSECONDS.sleep(100);
            } catch (InterruptedException e) {
                logger.error("lock异常", e);
                flag = false;
                // Restore interrupted state...      
                Thread.currentThread().interrupt();
                break;
            }
        }
        return flag;
    }

    /**
     * 加锁,等待一定时间
     *
     * @param key
     * @param value
     * @param timeOut
     * @return
     */
    public static boolean lock(String key, String value, long timeOut) {
        if (null == stringRedisTemplate) {
            stringRedisTemplate = (StringRedisTemplate) ApplicationContextProvider.getBean("stringRedisTemplate");
        }
        boolean flag = true;

        long time = System.currentTimeMillis(), maxTime = time + timeOut;
        while (!(flag = stringRedisTemplate.opsForValue().setIfAbsent(key, value)) && time <= maxTime) { // 判断自旋等待时间
            try {
                TimeUnit.MICROSECONDS.sleep(100);
            } catch (InterruptedException e) {
                logger.error("lock异常", e);
                flag = false;
                // Restore interrupted state...      
                Thread.currentThread().interrupt();
                break;
            }

            time = System.currentTimeMillis();
        }
        return flag;
    }

    /**
     * 加锁,等待一定时间
     *
     * @param key
     *            锁的key
     * @param value
     *            锁的值
     * @param timeOut
     *            等待超时时间
     * @param autoReleaseTime
     *            锁自动释放时间
     * @return
     */
    public static boolean lock(String key, String value, long timeOut, long autoReleaseTime) {
        if (null == stringRedisTemplate) {
            stringRedisTemplate = (StringRedisTemplate) ApplicationContextProvider.getBean("stringRedisTemplate");
        }
        boolean flag = true;

        long time = System.currentTimeMillis(), maxTime = time + timeOut;
        while (!stringRedisTemplate.opsForValue().setIfAbsent(key, value) && time <= maxTime) { // 判断自旋等待时间
            try {
                TimeUnit.MICROSECONDS.sleep(100);
            } catch (InterruptedException e) {
                logger.error("lock异常", e);
                flag = false;
                // Restore interrupted state...
                Thread.currentThread().interrupt();
                break;
            }

            time = System.currentTimeMillis();
        }

        if (flag) { // 设置过期时间
            stringRedisTemplate.expire(key, autoReleaseTime, TimeUnit.MILLISECONDS);
        }

        return flag;
    }

    /**
     * 解锁
     *
     * @param key
     * @param value
     */
    public static void unLock(String key, String value) {
        if (null == stringRedisTemplate) {
            stringRedisTemplate = (StringRedisTemplate) ApplicationContextProvider.getBean("stringRedisTemplate");
        }
        try {
            if (Objects.equals(stringRedisTemplate.opsForValue().get(key), value)) { // 判断点前释放持有锁
                stringRedisTemplate.delete(key); // 移除数据
            }
        } catch (Exception e) {
            logger.error("unLock异常", e);
        }
    }

    /**
     * 刷新
     * @param key
     * @param value
     * @param autoReleaseTime
     */
    public static void refresh(String key, String value, Long autoReleaseTime) {
        if (null == stringRedisTemplate) {
            stringRedisTemplate = (StringRedisTemplate) ApplicationContextProvider.getBean("stringRedisTemplate");
        }
        try {
            if (stringRedisTemplate.opsForValue().get(key).equals(value)) { // 判断点前释放持有锁
                stringRedisTemplate.expire(key, autoReleaseTime, TimeUnit.MILLISECONDS);
            }
        } catch (Exception e) {
            logger.error("refresh异常", e);
        }
    }
}
