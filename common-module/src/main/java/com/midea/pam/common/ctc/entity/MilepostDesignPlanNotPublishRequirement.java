package com.midea.pam.common.ctc.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "详设未发布需求")
public class MilepostDesignPlanNotPublishRequirement extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "状态名称")
    private String statusName;

    @ApiModelProperty(value = "详设id")
    private Long designPlanDetailId;

    @ApiModelProperty(value = "物料描述")
    private String materielDescr;

    @ApiModelProperty(value = "PAM编码")
    private String pamCode;

    @ApiModelProperty(value = "ERP物料编码")
    private String erpCode;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "物料类型")
    private String materialCategory;

    @ApiModelProperty(value = "采购需求总数")
    private BigDecimal totalNum;

    @ApiModelProperty(value = "已生成采购需求数量")
    private BigDecimal requirementNum;

    @ApiModelProperty(value = "交货时间")
    private Date deliveryTime;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "型号/规格/图号")
    private String model;

    @ApiModelProperty(value = "图号")
    private String figureNumber;

    @ApiModelProperty(value = "物料大类")
    private String materialClassification;

    @ApiModelProperty(value = "物料中类")
    private String codingMiddleClass;

    @ApiModelProperty(value = "物料小类")
    private String materielType;

    @ApiModelProperty(value = "项目经理")
    private String managerName;

    @ApiModelProperty(value = "项目经理id")
    private Long managerId;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目类型")
    private String projectTypeName;

    @ApiModelProperty(value = "项目类型id")
    private Long projectType;

    @ApiModelProperty(value = "单位id")
    private Long unitId;

    @ApiModelProperty(value = "业务分类")
    private String unitName;

    @ApiModelProperty(value = "提交日期")
    private Date publishAt;

    @ApiModelProperty(value = "提交人")
    private Long publishBy;

    @ApiModelProperty(value = "里程碑id")
    private Long milepostId;

    @ApiModelProperty(value = "启用WBS（0否1是）")
    private Boolean wbsEnabled;

    @ApiModelProperty(value = "是否删除")
    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName == null ? null : statusName.trim();
    }

    public Long getDesignPlanDetailId() {
        return designPlanDetailId;
    }

    public void setDesignPlanDetailId(Long designPlanDetailId) {
        this.designPlanDetailId = designPlanDetailId;
    }

    public String getMaterielDescr() {
        return materielDescr;
    }

    public void setMaterielDescr(String materielDescr) {
        this.materielDescr = materielDescr == null ? null : materielDescr.trim();
    }

    public String getPamCode() {
        return pamCode;
    }

    public void setPamCode(String pamCode) {
        this.pamCode = pamCode == null ? null : pamCode.trim();
    }

    public String getErpCode() {
        return erpCode;
    }

    public void setErpCode(String erpCode) {
        this.erpCode = erpCode == null ? null : erpCode.trim();
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit == null ? null : unit.trim();
    }

    public String getMaterialCategory() {
        return materialCategory;
    }

    public void setMaterialCategory(String materialCategory) {
        this.materialCategory = materialCategory == null ? null : materialCategory.trim();
    }

    public BigDecimal getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(BigDecimal totalNum) {
        this.totalNum = totalNum;
    }

    public BigDecimal getRequirementNum() {
        return requirementNum;
    }

    public void setRequirementNum(BigDecimal requirementNum) {
        this.requirementNum = requirementNum;
    }

    public Date getDeliveryTime() {
        return deliveryTime;
    }

    public void setDeliveryTime(Date deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand == null ? null : brand.trim();
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model == null ? null : model.trim();
    }

    public String getFigureNumber() {
        return figureNumber;
    }

    public void setFigureNumber(String figureNumber) {
        this.figureNumber = figureNumber == null ? null : figureNumber.trim();
    }

    public String getMaterialClassification() {
        return materialClassification;
    }

    public void setMaterialClassification(String materialClassification) {
        this.materialClassification = materialClassification == null ? null : materialClassification.trim();
    }

    public String getCodingMiddleClass() {
        return codingMiddleClass;
    }

    public void setCodingMiddleClass(String codingMiddleClass) {
        this.codingMiddleClass = codingMiddleClass == null ? null : codingMiddleClass.trim();
    }

    public String getMaterielType() {
        return materielType;
    }

    public void setMaterielType(String materielType) {
        this.materielType = materielType == null ? null : materielType.trim();
    }

    public String getManagerName() {
        return managerName;
    }

    public void setManagerName(String managerName) {
        this.managerName = managerName == null ? null : managerName.trim();
    }

    public Long getManagerId() {
        return managerId;
    }

    public void setManagerId(Long managerId) {
        this.managerId = managerId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public String getProjectTypeName() {
        return projectTypeName;
    }

    public void setProjectTypeName(String projectTypeName) {
        this.projectTypeName = projectTypeName == null ? null : projectTypeName.trim();
    }

    public Long getProjectType() {
        return projectType;
    }

    public void setProjectType(Long projectType) {
        this.projectType = projectType;
    }

    public Long getUnitId() {
        return unitId;
    }

    public void setUnitId(Long unitId) {
        this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName == null ? null : unitName.trim();
    }

    public Date getPublishAt() {
        return publishAt;
    }

    public void setPublishAt(Date publishAt) {
        this.publishAt = publishAt;
    }

    public Long getPublishBy() {
        return publishBy;
    }

    public void setPublishBy(Long publishBy) {
        this.publishBy = publishBy;
    }

    public Long getMilepostId() {
        return milepostId;
    }

    public void setMilepostId(Long milepostId) {
        this.milepostId = milepostId;
    }

    public Boolean getWbsEnabled() {
        return wbsEnabled;
    }

    public void setWbsEnabled(Boolean wbsEnabled) {
        this.wbsEnabled = wbsEnabled;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", statusName=").append(statusName);
        sb.append(", designPlanDetailId=").append(designPlanDetailId);
        sb.append(", materielDescr=").append(materielDescr);
        sb.append(", pamCode=").append(pamCode);
        sb.append(", erpCode=").append(erpCode);
        sb.append(", unit=").append(unit);
        sb.append(", materialCategory=").append(materialCategory);
        sb.append(", totalNum=").append(totalNum);
        sb.append(", requirementNum=").append(requirementNum);
        sb.append(", deliveryTime=").append(deliveryTime);
        sb.append(", name=").append(name);
        sb.append(", brand=").append(brand);
        sb.append(", model=").append(model);
        sb.append(", figureNumber=").append(figureNumber);
        sb.append(", materialClassification=").append(materialClassification);
        sb.append(", codingMiddleClass=").append(codingMiddleClass);
        sb.append(", materielType=").append(materielType);
        sb.append(", managerName=").append(managerName);
        sb.append(", managerId=").append(managerId);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", projectTypeName=").append(projectTypeName);
        sb.append(", projectType=").append(projectType);
        sb.append(", unitId=").append(unitId);
        sb.append(", unitName=").append(unitName);
        sb.append(", publishAt=").append(publishAt);
        sb.append(", publishBy=").append(publishBy);
        sb.append(", milepostId=").append(milepostId);
        sb.append(", wbsEnabled=").append(wbsEnabled);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}