package com.midea.pam.common.ctc.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class WorkingHourAccountingDetailSubjectExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public WorkingHourAccountingDetailSubjectExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andWorkingHourAccountingIdIsNull() {
            addCriterion("working_hour_accounting_id is null");
            return (Criteria) this;
        }

        public Criteria andWorkingHourAccountingIdIsNotNull() {
            addCriterion("working_hour_accounting_id is not null");
            return (Criteria) this;
        }

        public Criteria andWorkingHourAccountingIdEqualTo(Long value) {
            addCriterion("working_hour_accounting_id =", value, "workingHourAccountingId");
            return (Criteria) this;
        }

        public Criteria andWorkingHourAccountingIdNotEqualTo(Long value) {
            addCriterion("working_hour_accounting_id <>", value, "workingHourAccountingId");
            return (Criteria) this;
        }

        public Criteria andWorkingHourAccountingIdGreaterThan(Long value) {
            addCriterion("working_hour_accounting_id >", value, "workingHourAccountingId");
            return (Criteria) this;
        }

        public Criteria andWorkingHourAccountingIdGreaterThanOrEqualTo(Long value) {
            addCriterion("working_hour_accounting_id >=", value, "workingHourAccountingId");
            return (Criteria) this;
        }

        public Criteria andWorkingHourAccountingIdLessThan(Long value) {
            addCriterion("working_hour_accounting_id <", value, "workingHourAccountingId");
            return (Criteria) this;
        }

        public Criteria andWorkingHourAccountingIdLessThanOrEqualTo(Long value) {
            addCriterion("working_hour_accounting_id <=", value, "workingHourAccountingId");
            return (Criteria) this;
        }

        public Criteria andWorkingHourAccountingIdIn(List<Long> values) {
            addCriterion("working_hour_accounting_id in", values, "workingHourAccountingId");
            return (Criteria) this;
        }

        public Criteria andWorkingHourAccountingIdNotIn(List<Long> values) {
            addCriterion("working_hour_accounting_id not in", values, "workingHourAccountingId");
            return (Criteria) this;
        }

        public Criteria andWorkingHourAccountingIdBetween(Long value1, Long value2) {
            addCriterion("working_hour_accounting_id between", value1, value2, "workingHourAccountingId");
            return (Criteria) this;
        }

        public Criteria andWorkingHourAccountingIdNotBetween(Long value1, Long value2) {
            addCriterion("working_hour_accounting_id not between", value1, value2, "workingHourAccountingId");
            return (Criteria) this;
        }

        public Criteria andDebitCreditIsNull() {
            addCriterion("debit_credit is null");
            return (Criteria) this;
        }

        public Criteria andDebitCreditIsNotNull() {
            addCriterion("debit_credit is not null");
            return (Criteria) this;
        }

        public Criteria andDebitCreditEqualTo(String value) {
            addCriterion("debit_credit =", value, "debitCredit");
            return (Criteria) this;
        }

        public Criteria andDebitCreditNotEqualTo(String value) {
            addCriterion("debit_credit <>", value, "debitCredit");
            return (Criteria) this;
        }

        public Criteria andDebitCreditGreaterThan(String value) {
            addCriterion("debit_credit >", value, "debitCredit");
            return (Criteria) this;
        }

        public Criteria andDebitCreditGreaterThanOrEqualTo(String value) {
            addCriterion("debit_credit >=", value, "debitCredit");
            return (Criteria) this;
        }

        public Criteria andDebitCreditLessThan(String value) {
            addCriterion("debit_credit <", value, "debitCredit");
            return (Criteria) this;
        }

        public Criteria andDebitCreditLessThanOrEqualTo(String value) {
            addCriterion("debit_credit <=", value, "debitCredit");
            return (Criteria) this;
        }

        public Criteria andDebitCreditLike(String value) {
            addCriterion("debit_credit like", value, "debitCredit");
            return (Criteria) this;
        }

        public Criteria andDebitCreditNotLike(String value) {
            addCriterion("debit_credit not like", value, "debitCredit");
            return (Criteria) this;
        }

        public Criteria andDebitCreditIn(List<String> values) {
            addCriterion("debit_credit in", values, "debitCredit");
            return (Criteria) this;
        }

        public Criteria andDebitCreditNotIn(List<String> values) {
            addCriterion("debit_credit not in", values, "debitCredit");
            return (Criteria) this;
        }

        public Criteria andDebitCreditBetween(String value1, String value2) {
            addCriterion("debit_credit between", value1, value2, "debitCredit");
            return (Criteria) this;
        }

        public Criteria andDebitCreditNotBetween(String value1, String value2) {
            addCriterion("debit_credit not between", value1, value2, "debitCredit");
            return (Criteria) this;
        }

        public Criteria andSubjectIsNull() {
            addCriterion("subject is null");
            return (Criteria) this;
        }

        public Criteria andSubjectIsNotNull() {
            addCriterion("subject is not null");
            return (Criteria) this;
        }

        public Criteria andSubjectEqualTo(String value) {
            addCriterion("subject =", value, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectNotEqualTo(String value) {
            addCriterion("subject <>", value, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectGreaterThan(String value) {
            addCriterion("subject >", value, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectGreaterThanOrEqualTo(String value) {
            addCriterion("subject >=", value, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectLessThan(String value) {
            addCriterion("subject <", value, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectLessThanOrEqualTo(String value) {
            addCriterion("subject <=", value, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectLike(String value) {
            addCriterion("subject like", value, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectNotLike(String value) {
            addCriterion("subject not like", value, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectIn(List<String> values) {
            addCriterion("subject in", values, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectNotIn(List<String> values) {
            addCriterion("subject not in", values, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectBetween(String value1, String value2) {
            addCriterion("subject between", value1, value2, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectNotBetween(String value1, String value2) {
            addCriterion("subject not between", value1, value2, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectDescribeIsNull() {
            addCriterion("subject_describe is null");
            return (Criteria) this;
        }

        public Criteria andSubjectDescribeIsNotNull() {
            addCriterion("subject_describe is not null");
            return (Criteria) this;
        }

        public Criteria andSubjectDescribeEqualTo(String value) {
            addCriterion("subject_describe =", value, "subjectDescribe");
            return (Criteria) this;
        }

        public Criteria andSubjectDescribeNotEqualTo(String value) {
            addCriterion("subject_describe <>", value, "subjectDescribe");
            return (Criteria) this;
        }

        public Criteria andSubjectDescribeGreaterThan(String value) {
            addCriterion("subject_describe >", value, "subjectDescribe");
            return (Criteria) this;
        }

        public Criteria andSubjectDescribeGreaterThanOrEqualTo(String value) {
            addCriterion("subject_describe >=", value, "subjectDescribe");
            return (Criteria) this;
        }

        public Criteria andSubjectDescribeLessThan(String value) {
            addCriterion("subject_describe <", value, "subjectDescribe");
            return (Criteria) this;
        }

        public Criteria andSubjectDescribeLessThanOrEqualTo(String value) {
            addCriterion("subject_describe <=", value, "subjectDescribe");
            return (Criteria) this;
        }

        public Criteria andSubjectDescribeLike(String value) {
            addCriterion("subject_describe like", value, "subjectDescribe");
            return (Criteria) this;
        }

        public Criteria andSubjectDescribeNotLike(String value) {
            addCriterion("subject_describe not like", value, "subjectDescribe");
            return (Criteria) this;
        }

        public Criteria andSubjectDescribeIn(List<String> values) {
            addCriterion("subject_describe in", values, "subjectDescribe");
            return (Criteria) this;
        }

        public Criteria andSubjectDescribeNotIn(List<String> values) {
            addCriterion("subject_describe not in", values, "subjectDescribe");
            return (Criteria) this;
        }

        public Criteria andSubjectDescribeBetween(String value1, String value2) {
            addCriterion("subject_describe between", value1, value2, "subjectDescribe");
            return (Criteria) this;
        }

        public Criteria andSubjectDescribeNotBetween(String value1, String value2) {
            addCriterion("subject_describe not between", value1, value2, "subjectDescribe");
            return (Criteria) this;
        }

        public Criteria andLaborCostIsNull() {
            addCriterion("labor_cost is null");
            return (Criteria) this;
        }

        public Criteria andLaborCostIsNotNull() {
            addCriterion("labor_cost is not null");
            return (Criteria) this;
        }

        public Criteria andLaborCostEqualTo(BigDecimal value) {
            addCriterion("labor_cost =", value, "laborCost");
            return (Criteria) this;
        }

        public Criteria andLaborCostNotEqualTo(BigDecimal value) {
            addCriterion("labor_cost <>", value, "laborCost");
            return (Criteria) this;
        }

        public Criteria andLaborCostGreaterThan(BigDecimal value) {
            addCriterion("labor_cost >", value, "laborCost");
            return (Criteria) this;
        }

        public Criteria andLaborCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("labor_cost >=", value, "laborCost");
            return (Criteria) this;
        }

        public Criteria andLaborCostLessThan(BigDecimal value) {
            addCriterion("labor_cost <", value, "laborCost");
            return (Criteria) this;
        }

        public Criteria andLaborCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("labor_cost <=", value, "laborCost");
            return (Criteria) this;
        }

        public Criteria andLaborCostIn(List<BigDecimal> values) {
            addCriterion("labor_cost in", values, "laborCost");
            return (Criteria) this;
        }

        public Criteria andLaborCostNotIn(List<BigDecimal> values) {
            addCriterion("labor_cost not in", values, "laborCost");
            return (Criteria) this;
        }

        public Criteria andLaborCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("labor_cost between", value1, value2, "laborCost");
            return (Criteria) this;
        }

        public Criteria andLaborCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("labor_cost not between", value1, value2, "laborCost");
            return (Criteria) this;
        }

        public Criteria andWorkingHourIsNull() {
            addCriterion("working_hour is null");
            return (Criteria) this;
        }

        public Criteria andWorkingHourIsNotNull() {
            addCriterion("working_hour is not null");
            return (Criteria) this;
        }

        public Criteria andWorkingHourEqualTo(BigDecimal value) {
            addCriterion("working_hour =", value, "workingHour");
            return (Criteria) this;
        }

        public Criteria andWorkingHourNotEqualTo(BigDecimal value) {
            addCriterion("working_hour <>", value, "workingHour");
            return (Criteria) this;
        }

        public Criteria andWorkingHourGreaterThan(BigDecimal value) {
            addCriterion("working_hour >", value, "workingHour");
            return (Criteria) this;
        }

        public Criteria andWorkingHourGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("working_hour >=", value, "workingHour");
            return (Criteria) this;
        }

        public Criteria andWorkingHourLessThan(BigDecimal value) {
            addCriterion("working_hour <", value, "workingHour");
            return (Criteria) this;
        }

        public Criteria andWorkingHourLessThanOrEqualTo(BigDecimal value) {
            addCriterion("working_hour <=", value, "workingHour");
            return (Criteria) this;
        }

        public Criteria andWorkingHourIn(List<BigDecimal> values) {
            addCriterion("working_hour in", values, "workingHour");
            return (Criteria) this;
        }

        public Criteria andWorkingHourNotIn(List<BigDecimal> values) {
            addCriterion("working_hour not in", values, "workingHour");
            return (Criteria) this;
        }

        public Criteria andWorkingHourBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("working_hour between", value1, value2, "workingHour");
            return (Criteria) this;
        }

        public Criteria andWorkingHourNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("working_hour not between", value1, value2, "workingHour");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeIsNull() {
            addCriterion("labor_cost_type is null");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeIsNotNull() {
            addCriterion("labor_cost_type is not null");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeEqualTo(String value) {
            addCriterion("labor_cost_type =", value, "laborCostType");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeNotEqualTo(String value) {
            addCriterion("labor_cost_type <>", value, "laborCostType");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeGreaterThan(String value) {
            addCriterion("labor_cost_type >", value, "laborCostType");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeGreaterThanOrEqualTo(String value) {
            addCriterion("labor_cost_type >=", value, "laborCostType");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeLessThan(String value) {
            addCriterion("labor_cost_type <", value, "laborCostType");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeLessThanOrEqualTo(String value) {
            addCriterion("labor_cost_type <=", value, "laborCostType");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeLike(String value) {
            addCriterion("labor_cost_type like", value, "laborCostType");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeNotLike(String value) {
            addCriterion("labor_cost_type not like", value, "laborCostType");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeIn(List<String> values) {
            addCriterion("labor_cost_type in", values, "laborCostType");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeNotIn(List<String> values) {
            addCriterion("labor_cost_type not in", values, "laborCostType");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeBetween(String value1, String value2) {
            addCriterion("labor_cost_type between", value1, value2, "laborCostType");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeNotBetween(String value1, String value2) {
            addCriterion("labor_cost_type not between", value1, value2, "laborCostType");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}