package com.midea.pam.common.statistics.dto;

import com.midea.pam.common.statistics.entity.PortalTarget;

public class PortalTargetDto extends PortalTarget {
    private String moduleCodeStr;

    private Long managerId;

    private String targetCode;

    private String fuzzyLike;

    public String getModuleCodeStr() {
        return moduleCodeStr;
    }

    public void setModuleCodeStr(String moduleCodeStr) {
        this.moduleCodeStr = moduleCodeStr;
    }

    public Long getManagerId() {
        return managerId;
    }

    public void setManagerId(Long managerId) {
        this.managerId = managerId;
    }

    public String getFuzzyLike() {
        return fuzzyLike;
    }

    public void setFuzzyLike(String fuzzyLike) {
        this.fuzzyLike = fuzzyLike;
    }

    public String getTargetCode() {
        return targetCode;
    }

    public void setTargetCode(String targetCode) {
        this.targetCode = targetCode;
    }
}