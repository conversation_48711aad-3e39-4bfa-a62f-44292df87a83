-- 商机备份脚本
CREATE TABLE pam_backup.plan_20231124_chenchong_02_bak (
	`id` bigint(20) NOT NULL COMMENT 'ID',
	PRIMARY KEY (`id`)
) AS
select * from pam_crm.plan where id in ( 1153265931524112384,1153368823253827584,1154388213125185536,1164956764203888640);
UPDATE pam_crm.plan SET summary_total_cost=680,total_cost=680 WHERE id=1153265931524112384 and total_cost = 0 and summary_total_cost = 0 ;
UPDATE pam_crm.plan SET summary_total_cost=11947,total_cost=11947 WHERE id=1153368823253827584 and total_cost = 0 and summary_total_cost = 0 ;
UPDATE pam_crm.plan SET summary_total_cost=39823,total_cost=39823 WHERE id=1154388213125185536 and total_cost = 0 and summary_total_cost = 0 ;
UPDATE pam_crm.plan SET summary_total_cost=934307.47,product_total_product_cost=934307.47,total_cost=934307.47
WHERE id=1164956764203888640 and total_cost = 0 and summary_total_cost = 0 and product_total_product_cost = 916917.470000 ;

-- 商机备份脚本
CREATE TABLE pam_backup.plan_summary_20231124_chenchong_02_bak (
	`id` bigint(20) NOT NULL COMMENT 'ID',
	PRIMARY KEY (`id`)
) AS
select * from pam_crm.plan_summary where id in ( 1154835701599961088,1156175202343702528,1156253763360509952,1162054665300246528
,1162432459266613248,1163887896126103552,1164886584893972480,1162054665317023744);

UPDATE pam_crm.plan_summary SET total_cost_percent = 0.0018 WHERE id = 1154835701599961088 and total_cost_percent = 0.980800;
UPDATE pam_crm.plan_summary SET total_cost_percent = 0.0277 WHERE id = 1156175202343702528 and total_cost_percent = 0.923500;
UPDATE pam_crm.plan_summary SET total_cost_percent = 0.0305 WHERE id = 1156253763360509952 and total_cost_percent = 0.030400;
UPDATE pam_crm.plan_summary SET total_cost_percent = 0.1381 WHERE id = 1162054665300246528 and total_cost_percent = 0.138300;
UPDATE pam_crm.plan_summary SET total_cost_percent = 0.0044 WHERE id = 1162432459266613248 and total_cost_percent = 0.674900;
UPDATE pam_crm.plan_summary SET total_cost_percent = 0.0293 WHERE id = 1163887896126103552 and total_cost_percent = 0.029400;
UPDATE pam_crm.plan_summary SET total_cost_percent = 0.1122 WHERE id = 1164886584893972480 and total_cost_percent = 0.909200;
UPDATE pam_crm.plan_summary SET deleted_flag=1 WHERE id=1162054665317023744 and deleted_flag = 0;


INSERT INTO pam_crm.plan_summary (id,plan_id,detail_type,cost_type_name,total_cost,total_cost_percent,total_price,cost_coefficient,deleted_flag,create_by,create_at) VALUES
(1156253737494256486,1154814421268791296,1,'产品/物料成本-外购',13188227.01,0.979,54358679,0,0,864702756772904960,'2023-09-22 17:44:47'),
(1156253737494256401,1155916325000364032,1,'产品/物料成本-外购',1624661.12,0.8958,7577948.42,0,0,878473491777585152,'2023-09-26 10:27:28'),
(1156253737494256421,1156248176197943296,1,'产品/物料成本-外购',1609566.48,0.7644,4466335.6,0,0,981029274267942912,'2023-09-26 15:39:33'),
(1162432459178539981,1162430796795502592,1,'产品/物料成本-外购',1760445,0.6705,5043264,0,0,610430727644774400,'2023-10-13 16:51:35'),
(1163881660496281234,1163870111945793536,1,'产品/物料成本-外购',1038266.71,0.6355,2996652,0,0,1015821417679159296,'2023-10-17 16:50:11'),
(1164886584822661234,1164886138315321344,1,'产品/物料成本-外购',3764705.55,0.797,10519573.68,0,0,981029274267942912,'2023-10-20 11:23:24');

