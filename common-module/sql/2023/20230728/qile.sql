CREATE TABLE pam_statistics.`project_cost_total_fee_record` (
                                                                `id` bigint(20) NOT NULL COMMENT '主键',
                                                                `execute_id` bigint(20) DEFAULT NULL COMMENT '执行ID',
                                                                `project_code` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '项目编号',
                                                                `project_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '项目名称',
                                                                `fee_item_id` bigint(20) DEFAULT NULL COMMENT '费用类型ID',
                                                                `fee_item_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '费用类型',
                                                                `total_cost` decimal(20,2) DEFAULT NULL COMMENT '成本合计',
                                                                `incurred_cost` decimal(20,2) DEFAULT NULL COMMENT '已发生成本',
                                                                `pending_cost` decimal(20,2) DEFAULT NULL COMMENT '待处理费用',
                                                                `ea_available_amount` decimal(20,2) DEFAULT NULL COMMENT 'EA可用金额，取ems_budget_occupy_detail表中项目对应费用类型的overplus_ea_amount字段金额的汇总值',
                                                                `budget` decimal(20,2) DEFAULT NULL COMMENT '预算',
                                                                `remainder_budget` decimal(20,2) DEFAULT NULL COMMENT '剩余预算',
                                                                `incurred_ratio` decimal(20,2) DEFAULT NULL COMMENT '已发生成本比例',
                                                                `annual_accrual` decimal(20,2) DEFAULT NULL COMMENT '年度预提汇总',
                                                                `monthly_accrual` decimal(20,2) DEFAULT NULL COMMENT '月度预提汇总',
                                                                `type` int(11) DEFAULT NULL COMMENT '类型 1：差旅；2：非差旅',
                                                                `deleted_flag` tinyint(1) DEFAULT NULL COMMENT '删除标示',
                                                                `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
                                                                `create_at` datetime DEFAULT NULL COMMENT '创建时间',
                                                                `update_by` bigint(20) DEFAULT NULL COMMENT '更新人',
                                                                `update_at` datetime DEFAULT NULL COMMENT '更新时间',
                                                                PRIMARY KEY (`id`),
                                                                KEY `index_execute_id` (`execute_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='项目费用成本汇总-报表';

ALTER TABLE pam_statistics.project_cost_detail ADD project_id bigint(20) DEFAULT NULL NULL COMMENT '项目id';
ALTER TABLE pam_statistics.project_cost_detail ADD INDEX `project_id_index` (`project_id`);

ALTER TABLE pam_statistics.project_cost_detail ADD cost_execute_id bigint(20) DEFAULT NULL NULL COMMENT '项目成本生成批次id';
