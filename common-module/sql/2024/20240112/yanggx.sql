-- 迪链付款申请：SFK22090141，在erp误修复为取消支付（实际已经有付款记录），导致pam同步付款申请状态过来时，数据有误
-- 更新表【pam_ctc.payment_apply】；更新字段【total_cancel_amount/really_pay_included_price】；影响行数【1】
CREATE TABLE pam_backup.payment_apply_2024011201_ygx_bak (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
select *
from pam_ctc.payment_apply
where id = 1023955858150653952
;
UPDATE pam_ctc.payment_apply
  SET total_cancel_amount=251686.38,really_pay_included_price=132513.62
  WHERE id=1023955858150653952 and total_cancel_amount=384200.00 and really_pay_included_price=0;

-- 更新表【pam_ctc.payment_invoice】；更新字段【surplus_amount】；影响行数【1】
CREATE TABLE pam_backup.payment_invoice_2024011201_ygx_bak (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
select *
from pam_ctc.payment_invoice
where id = 1016732332905725952
;
UPDATE pam_ctc.payment_invoice
  SET surplus_amount=0
  WHERE id=1016732332905725952 and surplus_amount=768400.00;


-- 调整下采购合同模板的默认值
-- 更新表【pam_ctc.purchase_contract_template_config】；更新字段【invoice_info/project_schedule】；影响行数【4】
CREATE TABLE pam_backup.purchase_contract_template_config_2024011201_ygx_bak (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
select * from
pam_ctc.purchase_contract_template_config
where id in
(
20230922001,
20230922002,
20230922003,
20230922004
)
;
update pam_ctc.purchase_contract_template_config
set
invoice_info = "<p>纸质或电子(打印2份)增值税专用发票及确认单快递至【江苏省昆山市玉山镇莱斯路1号】联系人【何良红】 联系方式【13636643587，<EMAIL>】</p>",
project_schedule = "<p>(1)&nbsp;乙方应严格根据下述时间表完成工程各个节点的阶段性成果：</p><p>【2023】年【7】月【10】日前&nbsp;完成施工班组进场</p><p>【2023】年【7】月【30】日前&nbsp;完成施工主材进场</p><p>【2023】年【8】月【30】日前&nbsp;完成机械施工</p><p>【2023】年【9】月【30】日前&nbsp;完成系统调试</p><p>【2023】年【10】月【30】日前&nbsp;通过甲方及最终用户的终验收。</p><p>具体项目时间节点可根据甲方和最终用户的实际要求调整。</p><p>(2)&nbsp;乙方履行本合同过程中，若因最终用户原因需暂停项目履行的，甲方有权要求乙方配合暂停工程的相关工作，并妥善保护好已完成的工作成果。后续若最终用户恢复项目履行的，乙方应当配合及时恢复工程进度，根据项目暂停的时间，工程后续各节点的履行期限相应顺延。</p>"
where id in
(
20230922002,
20230922003
)
;
update pam_ctc.purchase_contract_template_config
set
invoice_info = "<p>纸质或电子(打印2份)增值税专用发票及确认单快递至【江苏省昆山市玉山镇莱斯路1号】联系人【何良红】 联系方式【13636643587，<EMAIL>】</p>"
where id in
(
20230922001,
20230922004
)
;


-- HR部门更新有误
-- 更新表【pam_basedata.ltc_organization】；更新字段【status】；影响行数【2】
CREATE TABLE pam_backup.ltc_organization_2024011201_ygx_bak (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
select *
from pam_basedata.ltc_organization
where id in
(
1010541187087466496,
1010541187141992448
)
;
update pam_basedata.ltc_organization
set status = "N"
where id in
(
1010541187087466496,
1010541187141992448
)
;